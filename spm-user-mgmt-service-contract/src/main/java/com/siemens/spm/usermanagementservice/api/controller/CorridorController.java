// /*
//  * Copyright (C) Siemens.  All Rights Reserved.
//  *
//  * Source      : CorridorController.java
//  * Project     : SPM Platform
//  */
// package com.siemens.spm.usermanagementservice.api.controller;

// import jakarta.validation.Valid;
// import jakarta.validation.constraints.NotNull;
// import jakarta.validation.constraints.Pattern;

// import org.springframework.http.HttpStatus;
// import org.springframework.http.ResponseEntity;
// import org.springframework.web.bind.annotation.GetMapping;
// import org.springframework.web.bind.annotation.PatchMapping;
// import org.springframework.web.bind.annotation.PathVariable;
// import org.springframework.web.bind.annotation.PostMapping;
// import org.springframework.web.bind.annotation.PutMapping;
// import org.springframework.web.bind.annotation.RequestBody;
// import org.springframework.web.bind.annotation.RequestMapping;
// import org.springframework.web.bind.annotation.RequestParam;
// import org.springframework.web.bind.annotation.ResponseStatus;

// import com.siemens.spm.common.api.PublicController;
// import com.siemens.spm.common.shared.resource.UUIDConstants;
// import com.siemens.spm.usermanagementservice.api.vo.request.CorridorCreateRequestVO;
// import com.siemens.spm.usermanagementservice.api.vo.request.CorridorStatusUpdateRequestVO;
// import com.siemens.spm.usermanagementservice.api.vo.request.CorridorUpdateRequestVO;
// import com.siemens.spm.usermanagementservice.api.vo.response.CorridorListResultObject;
// import com.siemens.spm.usermanagementservice.api.vo.response.CorridorResultObject;
// import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionInternalSearchResultObject;
// import io.swagger.v3.oas.annotations.Operation;
// import io.swagger.v3.oas.annotations.Parameter;
// import io.swagger.v3.oas.annotations.enums.ParameterIn;
// import io.swagger.v3.oas.annotations.responses.ApiResponse;
// import io.swagger.v3.oas.annotations.responses.ApiResponses;
// import io.swagger.v3.oas.annotations.tags.Tag;

// /**
//  * <AUTHOR> Vo
//  */
// @RequestMapping(CorridorController.URL_ROOT)
// @Tag(name = "corridor", description = "Corridor API")
// public interface CorridorController extends PublicController {

//     String VERSION = "/v1";
//     String CORRIDOR_RESOURCE = "/corridors";
//     String URL_ROOT = PUBLIC_API + VERSION + "/agencies/{agency_id}" + CORRIDOR_RESOURCE;
//     String UPDATE_CORRIDOR_STATUS = "/activate";

//     /**
//      * POST  /corridors  : Create a new corridor.
//      *
//      * @param corridorVO the corridor to create.
//      * @return CorridorResultObject
//      */
//     @Operation(summary = "Create a new corridor")
//     @ApiResponses(value = { @ApiResponse(responseCode = "400", description = "Bad Request"),
//             @ApiResponse(responseCode = "404", description = "Not Found") })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER, description = "Bearer Token type")
//     @ResponseStatus(HttpStatus.CREATED)
//     @PostMapping
//     ResponseEntity<CorridorResultObject> createCorridor(
//             @Parameter(name = "agency_id", description = "Agency Id")
//             @PathVariable(value = "agency_id")
//             Integer agencyId,

//             @Parameter(name = "request_body", description = "Corridor create request")
//             @Valid @RequestBody CorridorCreateRequestVO corridorVO);

//     /**
//      * PUT  /corridors/{corridor_id}  : Update existing Corridor
//      *
//      * @param corridorId the id of corridor.
//      * @param corridorVO the corridor to update.
//      * @return If success, updated Corridor will be returned.
//      */
//     @Operation(summary = "Update existing corridor")
//     @ApiResponses(value = { @ApiResponse(responseCode = "400", description = "Bad Request"), })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER, description = "Bearer Token type")
//     @ResponseStatus(HttpStatus.OK)
//     @PutMapping("{corridor_id}")
//     ResponseEntity<CorridorResultObject> updateCorridor(
//             @Parameter(name = "agency_id", description = "Agency Id")
//             @PathVariable(value = "agency_id")
//             Integer agencyId,

//             @Parameter(name = "corridor_id", description = "Corridor Id")
//             @PathVariable("corridor_id") @NotNull(message = "corridor_id_not_null")
//             @Pattern(regexp = UUIDConstants.REGEXP_FOR_1_CORRIDOR_ID, message = "corridor_id_invalid")
//             final String corridorId,
//             @Parameter(name = "request_body", description = "Corridor update request")
//             @Valid @RequestBody CorridorUpdateRequestVO corridorVO);

//     /**
//      * GET /corridors/{corridor_id}: Get a corridor detail
//      *
//      * @param corridorId
//      * @return CorridorListResultObject
//      */
//     @Operation(summary = "Get a corridor detail")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "400", description = "Bad Request")
//     })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER, description = "Bearer Token type")
//     @ResponseStatus(HttpStatus.OK)
//     @GetMapping("{corridor_id}")
//     ResponseEntity<CorridorResultObject> getCorridorDetail(
//             @Parameter(name = "agency_id", description = "Agency Id")
//             @PathVariable(value = "agency_id")
//             Integer agencyId,

//             @Parameter(name = "corridor_id", description = "Corridor Id")
//             @PathVariable("corridor_id")
//             @NotNull(message = "corridor_id_not_null")
//             @Pattern(regexp = UUIDConstants.REGEXP_FOR_1_CORRIDOR_ID, message = "corridor_id_invalid")
//             final String corridorId);

//     /**
//      * GET /corridors: Get corridors
//      *
//      * @param text
//      * @param status
//      * @param agencyId
//      * @param orderByColumns
//      * @param page
//      * @param size
//      * @return CorridorListResultObject
//      */
//     @Operation(summary = "Get corridors")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "400", description = "Bad Request")
//     })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER, description = "Bearer Token type")
//     @ResponseStatus(HttpStatus.OK)
//     @GetMapping()
//     ResponseEntity<CorridorListResultObject> getCorridors(
//             @Parameter(name = "agency_id", description = "Agency id")
//             @PathVariable(value = "agency_id") Integer agencyId,

//             @Parameter(name = "text", description = "Text can be corridor name")
//             @RequestParam(value = "text", required = false) String text,

//             @Parameter(name = "status", description = "Corridor status (ACTIVE/INACTIVE). If status is NULL, it will return data including both ACTIVE and INACTIVE")
//             @RequestParam(value = "status", required = false) String status,

//             @Parameter(name = "sort", description = "Sortable columns are: {name, status, last_modified_at}. Default order is descending. For example: name:ascend, status:descend")
//             @RequestParam(value = "sort", required = false, defaultValue = "last_modified_at:descend")
//             String[] orderByColumns,

//             @Parameter(name = "page", description = "Page number. If page is empty, it will be 0 (default).", example = "0")
//             @RequestParam(value = "page", required = false, defaultValue = "0") Integer page,

//             @Parameter(name = "size", description = "Size of page. If size is empty, it will be 10 (default)", example = "10")
//             @RequestParam(value = "size", required = false, defaultValue = "10") Integer size);

//     /**
//      * PATCH /corridors/activate : Update corridor status.
//      *
//      * @param requestVO
//      * @return CorridorResultObject
//      */
//     @Operation(summary = "Update corridor status")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "204", description = "No Content"),
//             @ApiResponse(responseCode = "400", description = "Bad Request")
//     })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER, description = "Bearer Token type")
//     @ResponseStatus(HttpStatus.NO_CONTENT)
//     @PatchMapping(UPDATE_CORRIDOR_STATUS)
//     ResponseEntity<CorridorResultObject> updateCorridorStatus(
//             @Parameter(name = "agency_id", description = "Agency id")
//             @PathVariable(value = "agency_id") Integer agencyId,

//             @Parameter(name = "request_body", description = "Corridor status update request")
//             @Valid @RequestBody CorridorStatusUpdateRequestVO requestVO);

//     /**
//      * Search intersections: GET /corridors/intersections/available
//      *
//      * @param agencyId
//      * @param corridorId
//      * @param text
//      * @param orderByColumns
//      * @param page
//      * @param size
//      * @return IntersectionInternalSearchResultObject
//      */
//     @Operation(summary = "Retrieve all available intersections for the current user can select")
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER, description = "Bearer Token type")
//     @ResponseStatus(HttpStatus.OK)
//     @GetMapping("intersections/available")
//     ResponseEntity<IntersectionInternalSearchResultObject> searchAvailableIntersections(
//             @Parameter(name = "agency_id", description = "Id of the agency need to filter")
//             @PathVariable(value = "agency_id") Integer agencyId,

//             @Parameter(description = "Id of the corridor to search intersections")
//             @RequestParam(value = "corridor_id", required = false)
//             String corridorId,

//             @Parameter(description = "It will be used to search intersections by name")
//             @RequestParam(value = "text", required = false)
//             String text,

//             @Parameter(description = "Fields will be used to sort returning data")
//             @RequestParam(value = "sort", required = false)
//             String[] orderByColumns,

//             @Parameter(description = "Page number. If page is empty, it will be 0 (default)")
//             @RequestParam(value = "page", required = false, defaultValue = "0")
//             Integer page,

//             @Parameter(description = "Size of page. If size is empty, it will be 10 (default)")
//             @RequestParam(value = "size", required = false, defaultValue = "10")
//             Integer size
//     );

// }
