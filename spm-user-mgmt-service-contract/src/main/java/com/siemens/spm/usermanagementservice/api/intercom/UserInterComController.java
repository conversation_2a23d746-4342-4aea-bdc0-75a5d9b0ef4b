/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : UserInterComController.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.api.intercom;

import com.siemens.spm.common.intercom.InterComController;
import com.siemens.spm.common.shared.vo.SimpleResultObject;
import com.siemens.spm.common.util.RestUtils;
import com.siemens.spm.usermanagementservice.api.vo.Expiration;
import com.siemens.spm.usermanagementservice.api.vo.response.UsersForAlarmSummaryResultObject;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * Internal REST interface providing information to other services. This interface is implemented by the actual running
 * service.
 *
 * <AUTHOR>
 */
@RequestMapping(UserInterComController.API_ROOT)
public interface UserInterComController extends InterComController {

    String API_ROOT = INTERNAL_API + "/v1";
    String USERS_EXPIRATION_DATA_RESOURCE = "/users/expiration-data";
    String USERS_FOR_ALARM_SUMMARY_RESOURCE = "/users/alarm-summary";

    /**
     * Scan all users for expired data
     *
     * @param exp list of data to scan
     * @return SimpleResultObject
     */
    @PostMapping(USERS_EXPIRATION_DATA_RESOURCE)
    ResponseEntity<SimpleResultObject> scanExpirationData(@RequestBody List<Expiration> exp);

    /**
     * Invoke expiration data scanning
     *
     * @param endpoint end-point of target service
     * @param expList  list of data to scan
     * @return ResponseEntity<SimpleResultObject>
     */
    static ResponseEntity<SimpleResultObject> invokeScanExpirationData(String endpoint, String agencyId,
        List<Expiration> expList) {
        String url = endpoint + API_ROOT + USERS_EXPIRATION_DATA_RESOURCE;

        return RestUtils.postWithAgencyHeader(url, agencyId, expList, SimpleResultObject.class);
    }

    @GetMapping(USERS_FOR_ALARM_SUMMARY_RESOURCE)
    ResponseEntity<UsersForAlarmSummaryResultObject> getUsersForAlarmSummary(
        @RequestParam(name = "agency_id") Integer agencyId);

    static ResponseEntity<UsersForAlarmSummaryResultObject> invokeGetUsersForAlarmSummary(String endpoint,
        Integer agencyId) {
        String url = endpoint + API_ROOT + USERS_FOR_ALARM_SUMMARY_RESOURCE + "?agency_id=" + agencyId;

        return RestUtils.getWithAgencyHeader(url, String.valueOf(agencyId),
            UsersForAlarmSummaryResultObject.class);
    }

}
