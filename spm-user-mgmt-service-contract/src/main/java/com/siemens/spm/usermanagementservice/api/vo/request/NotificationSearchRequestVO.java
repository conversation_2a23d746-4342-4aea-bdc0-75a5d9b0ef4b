/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : NotificationSearchRequestVO.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.api.vo.request;

import java.io.Serializable;
import java.sql.Timestamp;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class NotificationSearchRequestVO implements Serializable {

    private static final long serialVersionUID = -7305443601631523434L;

    @JsonProperty("created_at_from")
    private Timestamp createdAtFrom;

    @JsonProperty("created_at_to")
    private Timestamp createdAtTo;

    @JsonProperty("flag_status")
    private String flagStatus;

    // this is for searching by intersection name or intersection id
    @JsonProperty("intersection")
    private String intersection;

    @JsonProperty("agency_id")
    private Integer agencyId;

    @JsonProperty("order_by_columns")
    private String[] orderByColumns;

    @JsonProperty("page")
    private Integer page;

    @JsonProperty("read_status")
    private String readStatus;

    @JsonProperty("size")
    private Integer size;

    @JsonProperty("text")
    private String text;

    @JsonProperty("type_id")
    private Long typeId;

}
