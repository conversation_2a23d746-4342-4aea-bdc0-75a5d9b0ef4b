/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : ElementForDashboardChartVO.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.api.vo;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_NULL)
public class ElementForDashboardChartVO implements Serializable {

    private static final long serialVersionUID = -8301850517276173696L;

    @JsonProperty("name")
    private String name;

    @JsonProperty("count")
    private Long count;

}
