/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : DisplaySettingsVO.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.api.vo;

import java.io.Serial;
import java.io.Serializable;
import java.util.Arrays;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.common.message.MessageService;
import com.siemens.spm.common.shared.domaintype.MetricConstants;
import com.siemens.spm.common.shared.resource.TextKey;
import com.siemens.spm.common.shared.vo.TranslatableVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MetricDisplaySettingVO implements Serializable, TranslatableVO {

    @Serial
    private static final long serialVersionUID = -6484012436380227714L;

    private static final String GREEN_COLOR = "#00BD18";

    private static final String RED_COLOR = "#CC0000";

    private static final List<ColorSettingVO> DEFAULT_UNREAD_ALARM_NOTI_NUM_COLOR_SETTINGS = Arrays.asList(
            new ColorSettingVO(0.0, "#2daf00"),
            new ColorSettingVO(1.0, "#bd0000"));

    private static final List<ColorSettingVO> DEFAULT_ALARM_RECORD_NUM_COLOR_SETTINGS = Arrays.asList(
            new ColorSettingVO(0.0, GREEN_COLOR),
            new ColorSettingVO(100.0, RED_COLOR));

    private static final List<ColorSettingVO> DEFAULT_AOR_PERCENT_COLOR_SETTINGS = Arrays.asList(
            new ColorSettingVO(0.0, "#e9ebe9"),
            new ColorSettingVO(10.0, "#52eb08"),
            new ColorSettingVO(20.0, "#faff00"),
            new ColorSettingVO(40.0, "#ff6f00"),
            new ColorSettingVO(50.0, "#ff0000"));

    private static final List<ColorSettingVO> DEFAULT_AOG_PERCENT_COLOR_SETTINGS = Arrays.asList(
            new ColorSettingVO(0.0, "#d8d8d8"),
            new ColorSettingVO(60.0, "#ff8200"),
            new ColorSettingVO(70.0, "#f5e700"),
            new ColorSettingVO(80.0, GREEN_COLOR),
            new ColorSettingVO(90.0, "#0aff20"));

    private static final List<ColorSettingVO> DEFAULT_VEHICLE_VOLUME_COLOR_SETTINGS = Arrays.asList(
            new ColorSettingVO(0.0, "#cdcdcd"),
            new ColorSettingVO(1.0, "#bd0088"),
            new ColorSettingVO(10.0, "#0b2ae4"),
            new ColorSettingVO(30.0, "#ecd713"),
            new ColorSettingVO(50.0, "#871afe"),
            new ColorSettingVO(70.0, "#05cbc8"));

    private static final List<ColorSettingVO> DEFAULT_COORD_HEALTH_TRANS_COLOR_SETTINGS = Arrays.asList(
            new ColorSettingVO(0.0, GREEN_COLOR),
            new ColorSettingVO(100.0, RED_COLOR));

    private static final List<ColorSettingVO> DEFAULT_SPLIT_FAILURE_COLOR_SETTINGS = Arrays.asList(
            new ColorSettingVO(0.0, GREEN_COLOR),
            new ColorSettingVO(1000.0, RED_COLOR));

    private static final List<ColorSettingVO> DEFAULT_AVG_PED_DELAY_COLOR_SETTINGS = Arrays.asList(
            new ColorSettingVO(0.0, "#f5f5f5"),
            new ColorSettingVO(1.0, GREEN_COLOR),
            new ColorSettingVO(20.0, "#447c4b"),
            new ColorSettingVO(40.0, "#fbff28"),
            new ColorSettingVO(50.0, "#ffaf2d"),
            new ColorSettingVO(60.0, "#ff0303"));

    private static final List<ColorSettingVO> DEFAULT_AVG_QUEUE_LENGTH_COLOR_SETTING = Arrays.asList(
            new ColorSettingVO(0.0, "#e9ebe9"),
            new ColorSettingVO(20.0, "#52eb08"),
            new ColorSettingVO(100.0, "#faff00"),
            new ColorSettingVO(200.0, "#ff6f00"),
            new ColorSettingVO(500.0, "#ff0000"));

    private static final List<ColorSettingVO> DEFAULT_AVG_APP_DELAY_COLOR_SETTINGS = Arrays.asList(
            new ColorSettingVO(0.0, GREEN_COLOR),
            new ColorSettingVO(1000.0, RED_COLOR));

    private static final List<ColorSettingVO> DEFAULT_PP_REQUESTS_COLOR_SETTINGS = Arrays.asList(
            new ColorSettingVO(0.0, "#dcdfdc"),
            new ColorSettingVO(1.0, "#20ec30"),
            new ColorSettingVO(10.0, "#ec2c12"));

    private static final List<ColorSettingVO> DEFAULT_RLV_COUNT_COLOR_SETTINGS = Arrays.asList(
            new ColorSettingVO(0.0, "#e9ebe9"),
            new ColorSettingVO(2.0, "#52eb08"),
            new ColorSettingVO(5.0, "#faff00"),
            new ColorSettingVO(8.0, "#ff6f00"),
            new ColorSettingVO(10.0, "#ff0000"));

    public static MetricDisplaySettingVO getDefaultUnreadAlarmNotiNumDisplaySetting() {
        return new MetricDisplaySettingVO(MetricConstants.OPEN_ALARM_NOTI_COUNT_LABEL_KEY,
                null,
                DEFAULT_UNREAD_ALARM_NOTI_NUM_COLOR_SETTINGS);
    }

    public static MetricDisplaySettingVO getDefaultAlarmRecordNumDisplaySetting() {
        return new MetricDisplaySettingVO(MetricConstants.ALARM_RECORD_COUNT_LABEL_KEY,
                null,
                DEFAULT_ALARM_RECORD_NUM_COLOR_SETTINGS);
    }

    public static MetricDisplaySettingVO getDefaultAorPercentDisplaySetting() {
        return new MetricDisplaySettingVO(MetricConstants.AOR_PERCENT_LABEL_KEY,
                TextKey.UNIT_PERCENT,
                DEFAULT_AOR_PERCENT_COLOR_SETTINGS);
    }

    public static MetricDisplaySettingVO getDefaultAogPercentDisplaySetting() {
        return new MetricDisplaySettingVO(MetricConstants.AOG_PERCENT_LABEL_KEY,
                TextKey.UNIT_PERCENT,
                DEFAULT_AOG_PERCENT_COLOR_SETTINGS);
    }

    public static MetricDisplaySettingVO getDefaultVehicleVolumeDisplaySetting() {
        return new MetricDisplaySettingVO(MetricConstants.VEHICLE_VOLUME_LABEL_KEY,
                TextKey.UNIT_VPH,
                DEFAULT_VEHICLE_VOLUME_COLOR_SETTINGS);
    }

    public static MetricDisplaySettingVO getDefaultAvgPedDelayDisplaySetting() {
        return new MetricDisplaySettingVO(MetricConstants.AVG_PED_DELAY_ID,
                TextKey.UNIT_SECOND_DASHBOARD,
                DEFAULT_AVG_PED_DELAY_COLOR_SETTINGS);
    }

    public static MetricDisplaySettingVO getDefaultCoordHealthDisplaySetting() {
        return new MetricDisplaySettingVO(MetricConstants.TRANSITION_PERCENT_ID,
                TextKey.UNIT_PERCENT,
                DEFAULT_COORD_HEALTH_TRANS_COLOR_SETTINGS);
    }

    public static MetricDisplaySettingVO getDefaultSplitFailureDisplaySetting() {
        return new MetricDisplaySettingVO(MetricConstants.SPLIT_FAILURE_ID,
                TextKey.UNIT_RAW_COUNT,
                DEFAULT_SPLIT_FAILURE_COLOR_SETTINGS);
    }

    public static MetricDisplaySettingVO getDefaultAvgAppDelayDisplaySetting() {
        return new MetricDisplaySettingVO(MetricConstants.AVG_APP_DELAY_ID,
                TextKey.UNIT_SECOND_DASHBOARD,
                DEFAULT_AVG_APP_DELAY_COLOR_SETTINGS);
    }

    public static MetricDisplaySettingVO getDefaultAvgQueueLengthDisplaySetting() {
        return new MetricDisplaySettingVO(MetricConstants.AVG_QUEUE_LENGTH_ID,
                TextKey.UNIT_VEHICLE,
                DEFAULT_AVG_QUEUE_LENGTH_COLOR_SETTING);
    }

    public static MetricDisplaySettingVO getDefaultPpRequestsDisplaySetting() {
        return new MetricDisplaySettingVO(MetricConstants.PREEMPTION_PRIORITY_ID,
                TextKey.UNIT_RAW_COUNT,
                DEFAULT_PP_REQUESTS_COLOR_SETTINGS);
    }

    public static MetricDisplaySettingVO getDefaultRlvDisplaySetting() {
        return new MetricDisplaySettingVO(MetricConstants.RED_LIGHT_VIOLATION_COUNT_ID,
                TextKey.UNIT_RAW_COUNT,
                DEFAULT_RLV_COUNT_COLOR_SETTINGS);
    }

    @JsonProperty("label_key")
    private String labelKey;

    @JsonProperty("label")
    private String label;

    @JsonProperty("unit_key")
    private String unitKey;

    @JsonProperty("unit")
    private String unit;

    @JsonProperty("color")
    private List<ColorSettingVO> colorSettingVOList;

    public MetricDisplaySettingVO(String labelKey, String unitKey, List<ColorSettingVO> colorSettingVOList) {
        setLabelKey(labelKey);
        setUnitKey(unitKey);
        this.colorSettingVOList = colorSettingVOList;
    }

    /**
     * Will reset label until translator is accepted
     *
     * @param labelKey
     */
    public void setLabelKey(String labelKey) {
        this.labelKey = labelKey;
        this.label = labelKey;
    }

    public void setUnitKey(String unitKey) {
        this.unitKey = unitKey;
        this.unit = unitKey;
    }

    @Override
    public void acceptTranslator(MessageService translator) {
        if (translator == null)
            throw new IllegalArgumentException();

        label = translator.getMessage(labelKey);
        unit = translator.getMessage(unitKey);
    }

}
