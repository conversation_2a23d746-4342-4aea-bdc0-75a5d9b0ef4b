/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : ChangeLanguageResultObject.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.api.vo.response;

import org.springframework.http.HttpStatus;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.siemens.spm.common.shared.vo.AbstractResultObject;
import com.siemens.spm.usermanagementservice.api.vo.ChangeLanguageVO;
import com.siemens.spm.usermanagementservice.api.vo.response.ChangeLanguageResultObject.ChangeLanguageResultStatusCode;

import lombok.AllArgsConstructor;
import lombok.Getter;

@JsonInclude(Include.NON_NULL)
public class ChangeLanguageResultObject extends
        AbstractResultObject<ChangeLanguageVO, ChangeLanguageResultStatusCode> {

    private static final long serialVersionUID = -9205084441657959286L;

    private ChangeLanguageVO data;

    private ChangeLanguageResultStatusCode statusCode;

    /**
     * Constructor for ERROR case
     */
    public ChangeLanguageResultObject() {

    }

    /**
     * Constructor to set status to other than success
     *
     * @param data
     * @param statusCode
     */
    public ChangeLanguageResultObject(ChangeLanguageVO data, ChangeLanguageResultStatusCode statusCode) {
        super(data, statusCode);
    }

    @Override
    protected void setData(ChangeLanguageVO value) {
        this.data = value;

    }

    @Override
    public ChangeLanguageVO getData() {
        return data;
    }

    /**
     * Constructor for SUCCESS case
     *
     * @param data
     */
    public ChangeLanguageResultObject(ChangeLanguageVO data) {
        super(data);
    }

    @Override
    public ChangeLanguageResultStatusCode getStatusCode() {
        return statusCode;
    }

    @Override
    protected void setStatusCode(ChangeLanguageResultStatusCode value) {
        this.statusCode = value;
    }

    @Override
    public ChangeLanguageResultStatusCode getSuccessfulStatusValue() {
        return ChangeLanguageResultStatusCode.SUCCESS;
    }

    public String getMessage() {
        return statusCode.getMessage();
    }

    @Override
    public HttpStatus getHttpStatus() {
        return statusCode.getHttpStatus();
    }

    @AllArgsConstructor
    @Getter
    public enum ChangeLanguageResultStatusCode {
        SUCCESS("success", HttpStatus.OK),
        ERROR("unknown_exception", HttpStatus.INTERNAL_SERVER_ERROR),
        NOT_SUPPORT_LANGUAGE("language", "not_support_language", HttpStatus.BAD_REQUEST);

        private final String errorField;
        private final String message;
        private final HttpStatus httpStatus;

        ChangeLanguageResultStatusCode(String message, HttpStatus httpStatus) {
            this(null, message, httpStatus);
        }
    }

    @Override
    public String getErrorFieldName() {
        return this.statusCode.getErrorField();
    }

    @Override
    protected ChangeLanguageResultStatusCode getErrorStatusValue() {

        return ChangeLanguageResultStatusCode.ERROR;
    }
}
