package com.siemens.spm.usermanagementservice.api.vo;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * This class used for serialization to persist data into DB. Be careful when have any change in this class(field,
 * annotation, ..). This can break when deserialize data fetch from DB
 *
 * <AUTHOR> Nguyen - <EMAIL>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SummaryReportWidgetMetricVO implements Serializable {

    private static final long serialVersionUID = 3754667089886645954L;

    @JsonProperty("metric_id")
    private String metricId;

    @JsonProperty("warning_condition")
    private WarningConditionVO warningConditionVO;

    @JsonProperty("enabled")
    private Boolean enabled;

}
