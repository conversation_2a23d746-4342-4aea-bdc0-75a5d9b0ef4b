/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : IntersectionListForDashboardMapResultObject.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.api.vo.response;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

import org.springframework.http.HttpStatus;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.common.shared.vo.AbstractResultObject;
import com.siemens.spm.usermanagementservice.api.vo.IntersectionForDashboardMapVO;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionListForDashboardMapResultObject.ResponseData;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionListForDashboardMapResultObject.StatusCode;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

@JsonInclude(Include.NON_NULL)
public class IntersectionListForDashboardMapResultObject extends AbstractResultObject<ResponseData, StatusCode> {

    private static final long serialVersionUID = 8681865341255257937L;

    private ResponseData data;

    private StatusCode statusCode;

    /**
     * Constructor for SUCCESS case
     *
     * @param data
     */
    public IntersectionListForDashboardMapResultObject(ResponseData data) {
        super(data);
    }

    /**
     * Constructor to set status to other than success
     *
     * @param data
     * @param statusCode
     */
    public IntersectionListForDashboardMapResultObject(ResponseData data, StatusCode statusCode) {
        super(data, statusCode);
    }

    @Override
    public ResponseData getData() {
        return data;
    }

    @Override
    public String getMessage() {
        return statusCode.getMessage();
    }

    @Override
    public StatusCode getStatusCode() {
        return statusCode;
    }

    @Override
    public String getErrorFieldName() {
        return statusCode.getErrorField();
    }

    @Override
    protected void setStatusCode(StatusCode value) {
        statusCode = value;
    }

    @Override
    protected StatusCode getErrorStatusValue() {
        return StatusCode.ERROR;
    }

    @Override
    protected void setData(ResponseData value) {
        data = value;
    }

    @Getter
    @AllArgsConstructor
    public enum StatusCode {
        /** OK */
        SUCCESS("success", HttpStatus.OK),

        /** Internal Server Error */
        ERROR("unknown_exception", HttpStatus.INTERNAL_SERVER_ERROR),

        /** Bad Request */
        INVALID_SORT_COLUMN("sort", "invalid_sort_column", HttpStatus.BAD_REQUEST),
        INVALID_SORT_ORDER("sort", "invalid_sort_order", HttpStatus.BAD_REQUEST),
        INVALID_LOCATION("location", "invalid_location", HttpStatus.BAD_REQUEST),

        /** Not Found */
        AGENCY_NOT_FOUND("agency_not_found", HttpStatus.NOT_FOUND);

        private final String errorField;
        private final String message;
        private final HttpStatus httpStatus;

        StatusCode(String message, HttpStatus httpStatus) {
            this(null, message, httpStatus);
        }
    }

    @Override
    public HttpStatus getHttpStatus() {
        return statusCode.getHttpStatus();
    }

    @Override
    public StatusCode getSuccessfulStatusValue() {
        return StatusCode.SUCCESS;
    }

    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(Include.NON_NULL)
    public static class ResponseData implements Serializable {

        private static final long serialVersionUID = -9160946091109311087L;

        @JsonProperty("total_count")
        private Long totalCount;

        @JsonProperty("intersections")
        private List<IntersectionForDashboardMapVO> intersections;

        @JsonProperty("updated_time")
        @JsonFormat(shape = JsonFormat.Shape.NUMBER)
        protected Timestamp updatedTime;
    }

}
