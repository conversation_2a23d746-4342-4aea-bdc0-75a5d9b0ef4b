/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : IntersectionForDashboardMapVO.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.api.vo;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.common.shared.domaintype.MetricConstants;
import com.siemens.spm.common.shared.vo.ActionVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_NULL)
public class IntersectionForDashboardMapVO implements Serializable {

    private static final long serialVersionUID = 4424783456256329504L;

    @JsonProperty("id")
    private String id;

    @JsonProperty("name")
    private String name;

    @JsonProperty("latitude")
    private Double lat;

    @JsonProperty("longitude")
    private Double lon;

    @JsonProperty(MetricConstants.AOR_PERCENT_ID)
    private Double aorPercent;

    @JsonProperty(MetricConstants.AOG_PERCENT_ID)
    private Double aogPercent;

    @JsonProperty(MetricConstants.DETECTOR_ACTUATION_VOLUME_ID)
    private Long vehicleVolume;

    @JsonProperty(MetricConstants.TRANSITION_PERCENT_ID)
    private Double coordHealthTrans;

    @JsonProperty(MetricConstants.SPLIT_FAILURE_ID)
    private Long splitFailure;

    @JsonProperty(MetricConstants.AVG_APP_DELAY_ID)
    private Double avgAppDelay;

    @JsonProperty(MetricConstants.OPEN_ALARM_NOTI_COUNT_ID)
    private Long openAlarmCount;

    @JsonProperty(MetricConstants.ALARM_RECORD_COUNT_ID)
    private Long alarmRecordCount;

    @JsonProperty(MetricConstants.AVG_PED_DELAY_ID)
    private Double avgPedDelay;

    @JsonProperty(MetricConstants.RED_LIGHT_VIOLATION_COUNT_ID)
    private Long rlvCount;

    @JsonProperty(MetricConstants.AVG_QUEUE_LENGTH_ID)
    private double avgQueueLength;

    @JsonProperty(MetricConstants.PREEMPTION_PRIORITY_ID)
    private double preemption;

    @JsonProperty("avg_preemption_priority")
    private double avgPreemption;

    @JsonProperty("updated_time")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    protected Timestamp updatedTime;

    @JsonProperty("action_map")
    private Map<String, ActionVO> actionMap;

}
