package com.siemens.spm.usermanagementservice.api.vo.response;

import java.util.ArrayList;

import org.springframework.http.HttpStatus;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.siemens.spm.common.shared.vo.AbstractResultObject;
import com.siemens.spm.usermanagementservice.api.vo.UserSimpleVO;
import com.siemens.spm.usermanagementservice.api.vo.response.UserSimpleListResultObject.UserSimpleListStatusCode;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@RequiredArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserSimpleListResultObject
        extends AbstractResultObject<ArrayList<UserSimpleVO>, UserSimpleListStatusCode> {

    private ArrayList<UserSimpleVO> data;

    @NonNull
    private UserSimpleListStatusCode statusCode;

    @Override
    public String getMessage() {
        return statusCode.message;
    }

    @Override
    public HttpStatus getHttpStatus() {
        return statusCode.httpStatus;
    }

    @Override
    public String getErrorFieldName() {
        return statusCode.errorField;
    }

    @Override
    protected UserSimpleListStatusCode getErrorStatusValue() {
        return UserSimpleListStatusCode.ERROR;
    }

    @Override
    public UserSimpleListStatusCode getSuccessfulStatusValue() {
        return UserSimpleListStatusCode.SUCCESS;
    }

    @AllArgsConstructor
    @Getter
    public enum UserSimpleListStatusCode {
        /**
         * OK
         */
        SUCCESS("success", HttpStatus.OK),

        /**
         * Internal Server Error
         */
        ERROR("unknown_exception", HttpStatus.INTERNAL_SERVER_ERROR);

        private final String errorField;
        private final String message;
        private final HttpStatus httpStatus;

        UserSimpleListStatusCode(String message, HttpStatus httpStatus) {
            this(null, message, httpStatus);
        }
    }
}
