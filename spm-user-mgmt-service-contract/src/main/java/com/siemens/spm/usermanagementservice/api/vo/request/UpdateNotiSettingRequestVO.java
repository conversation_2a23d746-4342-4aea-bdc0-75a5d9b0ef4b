package com.siemens.spm.usermanagementservice.api.vo.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpdateNotiSettingRequestVO {

    @JsonIgnore
    private Integer agencyId;

    @JsonProperty("as_enabled")
    private boolean alarmSummaryEnabled;

    @JsonProperty("as_frequency")
    private String alarmSummaryFrequency;

}
