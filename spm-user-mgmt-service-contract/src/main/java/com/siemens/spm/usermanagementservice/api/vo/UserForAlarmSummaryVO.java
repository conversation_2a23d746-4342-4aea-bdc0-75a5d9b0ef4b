package com.siemens.spm.usermanagementservice.api.vo;

import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserForAlarmSummaryVO implements Serializable {

    private static final long serialVersionUID = 6976557310087365259L;

    private UserInternalVO userInternalVO;

    private LocalDateTime alarmTimeFrom;

    private LocalDateTime alarmTimeTo;

}
