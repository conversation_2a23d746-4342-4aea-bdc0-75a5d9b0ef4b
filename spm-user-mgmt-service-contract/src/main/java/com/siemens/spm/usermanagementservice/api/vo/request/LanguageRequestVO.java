package com.siemens.spm.usermanagementservice.api.vo.request;

import java.io.Serializable;

import jakarta.validation.constraints.NotBlank;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_NULL)
public class LanguageRequestVO implements Serializable {
    
    private static final long serialVersionUID = 6757801500270458061L;
    
    @NotBlank(message = "name_required")
    @JsonProperty("language")
    private String language;
}
