/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AgencySettingsVO.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.api.vo;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.common.message.MessageService;
import com.siemens.spm.common.shared.vo.AlarmCategoryVO;
import com.siemens.spm.common.shared.vo.TranslatableVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_NULL)
public class AgencySettingsVO implements Serializable, TranslatableVO {

    private static final long serialVersionUID = -6327816368364035751L;

    @JsonProperty("display")
    private DisplaySettingsVO displaySettingsVO;

    @JsonProperty("timezone")
    private String zoneOffset;

    @JsonProperty("show_unavailable_map_data")
    private Boolean showUnavailableMapData;

    @JsonProperty("alarm_categories")
    private List<AlarmCategoryVO> alarmCategoryList;

    @JsonProperty("saturation")
    private AgencySaturationVO saturation;

    @JsonProperty("center_point")
    private LocationVO centerPoint;

    @Override
    public void acceptTranslator(MessageService translator) {
        if (translator == null)
            throw new IllegalArgumentException();

        displaySettingsVO.acceptTranslator(translator);
    }

}
