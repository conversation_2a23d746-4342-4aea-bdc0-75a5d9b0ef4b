/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : CorridorStatusUpdateRequestVO.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.api.vo.request;

import java.io.Serializable;
import java.util.List;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CorridorStatusUpdateRequestVO implements Serializable {

    private static final long serialVersionUID = 5557971378941375473L;

    @JsonProperty("status")
    @NotNull(message = "status_not_null")
    private String status;

    @JsonProperty("ids")
    @NotEmpty(message = "ids_not_empty")
    private List<String> ids;

}
