package com.siemens.spm.usermanagementservice.api.vo;

import java.io.Serializable;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.common.shared.vo.StatisticDataVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SummaryIntersectionStatisticVO implements Serializable {

    private static final long serialVersionUID = -1969158071205411303L;

    @JsonProperty("int_uuid")
    private String intUUID;

    @JsonProperty("int_name")
    private String intName;

    @JsonProperty("statistics")
    private Map<String, StatisticDataVO> statisticDataVOMap;

}
