/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : IntersectionService.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.api.boundary;

import com.siemens.spm.usermanagementservice.api.vo.request.IntersectionSearchRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionDetailResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionInternalSearchResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionSearchResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionSimpleListResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionStatusHistoriesResultObject;

import java.time.LocalDateTime;

/**
 * Intersection Service Interface
 */
public interface IntersectionService {

    /**
     * Search intersections
     *
     * @param searchRequest
     * @return IntersectionSearchResultObject
     */
    IntersectionSearchResultObject searchIntersections(IntersectionSearchRequestVO searchRequest);

    /**
     * Get all simple intersections by agencyId
     *
     * @param agencyId
     * @return IntersectionSimpleListResultObject
     */
    IntersectionSimpleListResultObject getAllSimpleIntersections(Integer agencyId, String status);

    /**
     * Get intersection detail
     *
     * @param agencyId       id of agency
     * @param intersectionId id of intersection
     * @return IntersectionDetailsResultObject
     */
    IntersectionDetailResultObject getIntersectionDetail(Integer agencyId, String intersectionId);

    /**
     * Search intersections internally for other service(s)
     *
     * @param searchRequest
     * @return IntersectionInternalSearchResultObject
     */
    IntersectionInternalSearchResultObject searchIntersectionsInternal(IntersectionSearchRequestVO searchRequest);

    /**
     * Get intersection status histories
     *
     * @param agencyId       id of agency
     * @param intersectionId id of intersection
     * @return IntersectionStatusHistoriesResultObject
     */
    IntersectionStatusHistoriesResultObject getIntersectionStatusHistories(Integer agencyId,
                                                                           String intersectionId,
                                                                           Integer page,
                                                                           Integer size);

    /**
     * Update perflog uploaded from info event
     * @param intersectionId id of intersection
     * @param uploadedTime uploaded time
     */
    void updatePerflogUploadedFromInfoEvent(String intersectionId, LocalDateTime uploadedTime);

}
