package com.siemens.spm.usermanagementservice.api.vo.request;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.usermanagementservice.api.vo.AbstractWidgetVO;
import com.siemens.spm.usermanagementservice.api.vo.MapViewWidgetVO;
import com.siemens.spm.usermanagementservice.api.vo.NotificationWidgetVO;
import com.siemens.spm.usermanagementservice.api.vo.OpenAlarmWidgetVO;
import com.siemens.spm.usermanagementservice.api.vo.SummaryReportWidgetVO;
import com.siemens.spm.usermanagementservice.api.vo.TopIntOpenAlarmWidgetVO;
import com.siemens.spm.usermanagementservice.api.vo.VolumeReportWidgetVO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class UpdateWidgetsSettingRequestVO implements Serializable {

    private static final long serialVersionUID = -2117543281569525439L;

    @JsonIgnore
    private Integer agencyId;

    /**
     * Used to update a specific widget's setting. Target widget can be identified by
     * {@link AbstractWidgetVO#getWidgetId()}
     */
    @JsonProperty("widget")
    private AbstractWidgetVO abstractWidgetVO;

    @JsonProperty("map_view")
    private MapViewWidgetVO mapViewWidgetVO;

    @JsonProperty("open_alarm")
    private OpenAlarmWidgetVO openAlarmWidgetVO;

    @JsonProperty("top_int_open_alarm")
    private TopIntOpenAlarmWidgetVO topIntOpenAlarmWidgetVO;

    @JsonProperty("summary_report")
    private SummaryReportWidgetVO summaryReportWidgetVO;

    @JsonProperty("volume_report")
    private VolumeReportWidgetVO volumeReportWidgetVO;

    @JsonProperty("notification")
    private NotificationWidgetVO notificationWidgetVO;

}
