/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : Expiration.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.api.vo;

import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum Expiration {
    USER_KEY("user_key_expiration"),
    HOUSE_KEEPING("house_keeping");

    private final String column;

    Expiration(String column) {
        this.column = column;
    }

}
