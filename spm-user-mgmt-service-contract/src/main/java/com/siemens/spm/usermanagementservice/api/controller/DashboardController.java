// /*
//  * Copyright (C) Siemens.  All Rights Reserved.
//  *
//  * Source      : NotificationController.java
//  * Project     : SPM Platform
//  */

// package com.siemens.spm.usermanagementservice.api.controller;

// import java.time.LocalDateTime;
// import java.util.List;

// import jakarta.validation.constraints.NotNull;

// import org.springframework.format.annotation.DateTimeFormat;
// import org.springframework.http.HttpStatus;
// import org.springframework.http.ResponseEntity;
// import org.springframework.validation.annotation.Validated;
// import org.springframework.web.bind.annotation.GetMapping;
// import org.springframework.web.bind.annotation.PathVariable;
// import org.springframework.web.bind.annotation.RequestMapping;
// import org.springframework.web.bind.annotation.RequestParam;
// import org.springframework.web.bind.annotation.ResponseStatus;

// import com.siemens.spm.common.api.PublicController;
// import com.siemens.spm.common.constant.IntersectionOption;
// import com.siemens.spm.usermanagementservice.api.vo.response.AgencySettingsResultObject;
// import com.siemens.spm.usermanagementservice.api.vo.response.ElementListForDashboardChartResultObject;
// import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionListForDashboardMapResultObject;
// import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionListForDashboardTopRankResultObject;
// import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionListVolumeCountResultObject;
// import com.siemens.spm.usermanagementservice.api.vo.response.NotificationListForUserNotificationObject;
// import com.siemens.spm.usermanagementservice.api.vo.response.SummaryStatisticForDashboardResultObject;
// import io.swagger.v3.oas.annotations.Operation;
// import io.swagger.v3.oas.annotations.Parameter;
// import io.swagger.v3.oas.annotations.enums.ParameterIn;
// import io.swagger.v3.oas.annotations.responses.ApiResponse;
// import io.swagger.v3.oas.annotations.responses.ApiResponses;
// import io.swagger.v3.oas.annotations.tags.Tag;

// @RequestMapping(DashboardController.URL_ROOT)
// @Tag(name = "dashboard", description = "Dashboard API")
// @Validated
// public interface DashboardController extends PublicController {

//     String VERSION = "/v1";
//     String DASHBOARD_RESOURCE = "/dashboard";
//     String URL_ROOT = PUBLIC_API + VERSION + DASHBOARD_RESOURCE;

//     String MAP_SETTINGS_RESOURCE = "/map/{agency_id}/settings";
//     String MAP_INTERSECTIONS_RESOURCE = "/map/intersections";
//     String TOP_RANK_INTERSECTIONS_RESOURCE = "/top-rank/intersections";
//     String CHART_ELEMENTS_ALL_RESOURCE = "/chart/elements/all";

//     String USER_NOTIFICATION = "/notification";

//     String SUMMARY_STATISTIC_RESOURCE = "/widget-data/summary-report";

//     String GET_INTERSECTION_VOLUME_COUNT = "/volume-count";

//     /**
//      * Get supported metrics for display in Dashboard
//      *
//      * @return
//      */
//     @Operation(summary = "Get supported metrics for display in Dashboard")
//     @ResponseStatus(HttpStatus.OK)
//     @GetMapping(MAP_SETTINGS_RESOURCE)
//     ResponseEntity<AgencySettingsResultObject> getMapSettings(
//             @Parameter(name = "agency_id", description = "Agency Id")
//             @PathVariable("agency_id")
//             @NotNull(message = "agency_id_not_null") final Integer agencyId
//     );

//     /**
//      * Get intersections for map of dashboard GET
//      * dashboard/map/intersections?agency={agencyID}&top_left={latitude,longitude}&bottom_right={latitude,longitude}&sort={firstColumn,secondColumn}&page={page}&size={size}
//      *
//      * @return ResponseEntity<IntersectionListForDashboardMapResultObject>
//      */
//     @Operation(summary = "Get intersection for map of dashboard")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "400", description = "Bad Request"),
//             @ApiResponse(responseCode = "404", description = "Not Found") })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true,
//             in = ParameterIn.HEADER, description = "Bearer Token type")
//     @ResponseStatus(HttpStatus.OK)
//     @GetMapping(MAP_INTERSECTIONS_RESOURCE)
//     ResponseEntity<IntersectionListForDashboardMapResultObject> getIntersectionsForMap(
//             @Parameter(name = "agency_id", description = "Agency ID")
//             @RequestParam(value = "agency_id")
//             Integer agencyId,

//             @Parameter(name = "top_left", description = "The top left location [latitude, longitude] of geographical area where to retrieve intersection information.")
//             @RequestParam(value = "top_left")
//             Double[] topLeft,

//             @Parameter(name = "bottom_right", description = "The bottom right location [latitude, longitude] of geographical area where to retrieve intersection information.")
//             @RequestParam(value = "bottom_right")
//             Double[] bottomRight,

//             @Parameter(name = "from_time", description = "Time start get intersection.")
//             @RequestParam(value = "from_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
//             LocalDateTime fromTime,

//             @Parameter(name = "to_time", description = "Time end get intersection.")
//             @RequestParam(value = "to_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
//             LocalDateTime toTime,

//             @Parameter(name = "sort", description = "Sortable columns are: {latitude, longitude}. Default order is ascending.")
//             @RequestParam(value = "sort", required = false)
//             String[] orderByColumns,

//             @Parameter(name = "page", description = "Page number. If page is empty, it will be 0 (default).")
//             @RequestParam(value = "page", required = false)
//             Integer page,

//             @Parameter(name = "size", description = "Size of page. If size is empty, it will be 20 (default)")
//             @RequestParam(value = "size", required = false)
//             Integer size
//     );

//     /**
//      * Get intersections for dashboard top rank GET /dashboard/top-intersections?agency={agencyID}&size={size}
//      *
//      * @return IntersectionListForDashboardTopRankResultObject
//      */
//     @Operation(summary = "Retrieve intersections for dashboard top rank")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "404", description = "Not Found") })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true,
//             in = ParameterIn.HEADER, description = "Bearer Token type")

//     @ResponseStatus(HttpStatus.OK)
//     @GetMapping(TOP_RANK_INTERSECTIONS_RESOURCE)
//     ResponseEntity<IntersectionListForDashboardTopRankResultObject> getIntersectionsForTopRank(
//             @Parameter(name = "agency_id", description = "Agency ID")
//             @RequestParam(value = "agency_id", required = true)
//             Integer agencyId,

//             @Parameter(name = "int_ids", description = "INTERSECTION IDS")
//             @RequestParam(value = "int_ids", required = false)
//             List<String> intIds,

//             @Parameter(name = "exclude_int_ids", description = "EXCLUDE INTERSECTION IDS")
//             @RequestParam(value = "exclude_int_ids", required = false)
//             List<String> excludeIntIds,

//             @Parameter(name = "size", description = "Size of page. If size is empty, it will be 20 (default)")
//             @RequestParam(value = "size", required = false)
//             Integer size
//     );

//     /**
//      * Get all elements for chart of dashboard GET /dashboard/chart/elements/all?agency={agencyID}
//      *
//      * @return ElementListForDashboardChartResultObject
//      */
//     @Operation(summary = "Retrieve all elements for chart of dashboard")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "404", description = "Not Found") })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true,
//             in = ParameterIn.HEADER, description = "Bearer Token type")

//     @ResponseStatus(HttpStatus.OK)
//     @GetMapping(CHART_ELEMENTS_ALL_RESOURCE)
//     ResponseEntity<ElementListForDashboardChartResultObject> getAllElementsForChart(
//             @Parameter(name = "agency_id", description = "Agency ID")
//             @RequestParam(value = "agency_id", required = true)
//             Integer agencyId,

//             @Parameter(name = "int_ids", description = "INTERSECTION IDS")
//             @RequestParam(value = "int_ids", required = false)
//             List<String> intIds,

//             @Parameter(name = "exclude_int_ids", description = "EXCLUDE INTERSECTION IDS")
//             @RequestParam(value = "exclude_int_ids", required = false)
//             List<String> excludeIntIds
//     );

//     @Operation(summary = "")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "404", description = "Not Found") })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true,
//             in = ParameterIn.HEADER, description = "Bearer Token type")

//     @ResponseStatus(HttpStatus.OK)
//     @GetMapping(USER_NOTIFICATION)
//     ResponseEntity<NotificationListForUserNotificationObject> getNotificationForUserNotification
//             (@Parameter(name = "agency_id", description = "Agency Id")
//              @RequestParam(value = "agency_id")
//              Integer agencyId,

//              @Parameter(name = "type_id", description = "TYPE ID")
//              @RequestParam(value = "type_ids")
//              List<Long> typeIds,

//              @Parameter(name = "from_time", description = "This time must be lower or equal than from_time_utc")
//              @RequestParam(value = "from_time")
//              @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
//              LocalDateTime fromTime,

//              @Parameter(name = "to_time", description = "This time must be greater or equal than to_time_utc")
//              @RequestParam(value = "to_time")
//              @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
//              LocalDateTime toTime
//             );

//     @GetMapping(SUMMARY_STATISTIC_RESOURCE)
//     @ResponseStatus(HttpStatus.OK)
//     ResponseEntity<SummaryStatisticForDashboardResultObject> getSummaryStatistics(
//             @Parameter(name = "agency_id", description = "Agency ID")
//             @RequestParam(value = "agency_id", required = false)
//             Integer agencyId,

//             @Parameter(name = "filter", description = "filter")
//             @RequestParam(value = "filter", required = false)
//             String[] filter,

//             @Parameter(name = "sort", description = "sort")
//             @RequestParam(value = "sort", required = false)
//             String[] sort,

//             @Parameter(name = "page", description = "Page number. If page is empty, it will be 0 (default).")
//             @RequestParam(value = "page", required = false)
//             Integer page,

//             @Parameter(name = "size", description = "Size of page. If size is empty, it will be 10 (default)")
//             @RequestParam(value = "size", required = false)
//             Integer size,

//             @Parameter(description = "List of metrics wants to do statistic")
//             @RequestParam(value = "metrics")
//             String[] metrics,

//             @Parameter(description = "Time to start statistic")
//             @RequestParam(value = "from_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
//             LocalDateTime fromTime,

//             @Parameter(description = "Time to end statistic")
//             @RequestParam(value = "to_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
//             LocalDateTime toTime
//     );

//     /**
//      * GET /dashboard/volume-count : Get intersection volume count
//      *
//      * @return IntersectionSearchResultObject
//      */
//     @Operation(summary = "Get intersection volume count")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "400", description = "Bad Request")
//     })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true,
//             in = ParameterIn.HEADER, description = "Bearer Token type")

//     @ResponseStatus(HttpStatus.OK)
//     @GetMapping(GET_INTERSECTION_VOLUME_COUNT)
//     ResponseEntity<IntersectionListVolumeCountResultObject> getIntersectionVolumeCount(
//             @Parameter(name = "from_time", description = "From time, LocalDateTime format.")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
//             @RequestParam(value = "from_time") LocalDateTime fromTime,

//             @Parameter(name = "to_time", description = "To time, LocalDateTime format.")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
//             @RequestParam(value = "to_time") LocalDateTime toTime,

//             @Parameter(name = "agency_id", description = "Agency id")
//             @RequestParam(value = "agency_id", required = false) Integer agencyId,

//             @Parameter(name = "intersection_option")
//             @RequestParam(name = "intersection_option", required = false) IntersectionOption intersectionOption,

//             @Parameter(name = "intersection_ids")
//             @RequestParam(name = "intersection_ids", required = false) List<String> intersectionIds
//     );

// }
