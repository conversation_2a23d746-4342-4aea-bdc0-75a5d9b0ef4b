package com.siemens.spm.usermanagementservice.api.vo;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ReleaseNoteVO implements Serializable {

    private static final long serialVersionUID = -5110895029366587282L;

    @JsonProperty("title")
    private String title;

    @JsonProperty("content")
    private ReleaseContentVO[] content;

    @JsonProperty("description")
    private String description;

    @JsonProperty("version")
    private String version;

}
