package com.siemens.spm.usermanagementservice.api.vo;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class NotiSettingVO implements Serializable {

    private static final long serialVersionUID = 4156898895945724739L;

    @JsonProperty("as_enabled")
    private Boolean alarmSummaryEnabled;

    @JsonProperty("as_frequency")
    private String alarmSummaryFrequency;

}
