/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : DataHubSynchronizerService.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.api.boundary;

import java.time.LocalDateTime;

/**
 * DataHubSynchronizer Service Interface
 */
public interface DataHubSynchronizerService {

    /**
     * This method is used to sync all intersections which belong to Agencies defined in SPM
     */
    void syncIntersections();

    void initCache(Integer agencyId, String intUUID, LocalDateTime fromTime, LocalDateTime toTime);
}
