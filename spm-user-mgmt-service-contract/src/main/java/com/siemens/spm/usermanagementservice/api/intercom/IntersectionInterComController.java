/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : IntersectionInterComController.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.api.intercom;

import com.siemens.spm.common.intercom.InterComController;
import com.siemens.spm.common.util.RestUtils;
import com.siemens.spm.usermanagementservice.api.vo.request.IntersectionSearchRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionInternalSearchResultObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.validation.constraints.NotNull;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.time.LocalDateTime;

/**
 * Internal REST interface providing information to other services. This interface is implemented by the actual running
 * service.
 *
 * <AUTHOR> Nguyen
 */
@RequestMapping(IntersectionInterComController.API_ROOT)
public interface IntersectionInterComController extends InterComController {

    String API_ROOT = INTERNAL_API + "/v1";

    String INTERSECTIONS_RESOURCE = "/intersections";
    String SYNC_DATA_RESOURCE = INTERSECTIONS_RESOURCE + "/sync-data";
    String INIT_CACHE_RESOURCE = INTERSECTIONS_RESOURCE + "/init-cache";

    /**
     * Search intersections
     *
     * @param searchRequest
     * @return IntersectionInternalSearchResultObject
     */
    @PostMapping(INTERSECTIONS_RESOURCE)
    ResponseEntity<IntersectionInternalSearchResultObject> searchIntersections(
        @NotNull @RequestBody IntersectionSearchRequestVO searchRequest);

    /**
     * invoke searchIntersections request
     *
     * @param endpoint
     * @param searchRequest
     * @return IntersectionInternalSearchResultObject
     */
    static ResponseEntity<IntersectionInternalSearchResultObject> invokeSearchIntersections(String endpoint,
        IntersectionSearchRequestVO searchRequest) {
        String url = endpoint + API_ROOT + INTERSECTIONS_RESOURCE;
        Integer agencyId = searchRequest.getAgencyId();

        return RestUtils.postWithAgencyHeader(url, agencyId.toString(), searchRequest,
            IntersectionInternalSearchResultObject.class);

    }

    /**
     * PUT /intersections/sync-data : Sync all intersections which belong to agencies defined in SPM
     */
    @Operation(summary = "This method is used to update all intersections which belong to agencies defined in SPM")
    @ApiResponses(
        value = {
            @ApiResponse(responseCode = "404", description = "Not Found")
        })

    @ResponseStatus(HttpStatus.NO_CONTENT)
    @PutMapping(SYNC_DATA_RESOURCE)
    void syncIntersections();

    /**
     * PUT /intersections/init-cache Create tasks for crawling PerfLog data for specific intersection for a time range.
     */
    @Operation(summary = "Create tasks for crawling PerfLog data for specific intersection for a time range.")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @PutMapping(INIT_CACHE_RESOURCE)
    void initCache(@Parameter(description = "Agency Id") @RequestParam(value = "agency_id") Integer agencyId,

        @Parameter(description = "Intersection UUID") @RequestParam(value = "int_uuid") String intUUID,

        @Parameter(description = "Begining of initializing time range") @RequestParam(value = "from_time") @DateTimeFormat(
            iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime fromTime,

        @Parameter(description = "Ending of initializing time range") @RequestParam(value = "to_time") @DateTimeFormat(
            iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime toTime);

}
