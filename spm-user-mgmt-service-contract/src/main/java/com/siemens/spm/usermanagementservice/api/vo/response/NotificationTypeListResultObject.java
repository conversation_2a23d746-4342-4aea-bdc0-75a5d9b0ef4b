/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : NotificationTypeListResultObject.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.api.vo.response;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.common.shared.vo.AbstractResultObject;
import com.siemens.spm.common.shared.vo.NotificationTypeVO;
import com.siemens.spm.usermanagementservice.api.vo.response.NotificationTypeListResultObject.ResponseData;
import com.siemens.spm.usermanagementservice.api.vo.response.NotificationTypeListResultObject.StatusCode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.springframework.http.HttpStatus;

@JsonInclude(Include.NON_NULL)
public class NotificationTypeListResultObject extends
        AbstractResultObject<ResponseData, StatusCode> {

    private static final long serialVersionUID = 9051584607055931573L;

    private ResponseData data;

    private StatusCode statusCode;

    /**
     * Constructor for SUCCESS case
     *
     * @param data
     */
    public NotificationTypeListResultObject(ResponseData data) {
        super(data);
    }

    /**
     * Constructor to set status to other than success
     *
     * @param data
     * @param statusCode
     */
    public NotificationTypeListResultObject(ResponseData data, StatusCode statusCode) {
        super(data, statusCode);
    }

    @Override
    public StatusCode getStatusCode() {
        return statusCode;
    }

    @Override
    public String getMessage() {
        return statusCode.getMessage();
    }

    @Override
    protected void setData(ResponseData value) {
        this.data = value;
    }

    @Override
    protected void setStatusCode(StatusCode value) {
        this.statusCode = value;
    }

    @Override
    public String getErrorFieldName() {
        return statusCode.getErrorField();
    }

    @Override
    public HttpStatus getHttpStatus() {
        return statusCode.getHttpStatus();
    }

    @Override
    protected StatusCode getErrorStatusValue() {
        return StatusCode.ERROR;
    }

    @Getter
    @AllArgsConstructor
    public enum StatusCode {

        ERROR("unknown_exception", HttpStatus.INTERNAL_SERVER_ERROR),
        SUCCESS("success", HttpStatus.OK);

        private final String errorField;
        private final String message;
        private final HttpStatus httpStatus;

        StatusCode(String message, HttpStatus httpStatus) {
            this(null, message, httpStatus);
        }

    }

    @Override
    public ResponseData getData() {
        return data;
    }

    @Builder
    @Data
    @JsonInclude(Include.NON_NULL)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ResponseData implements Serializable {

        private static final long serialVersionUID = -4214900888812312320L;

        @JsonProperty("total_count")
        private Long totalCount;

        @JsonProperty("types")
        private List<NotificationTypeVO> notificationTypes;

    }

    @Override
    public StatusCode getSuccessfulStatusValue() {
        return StatusCode.SUCCESS;
    }
}