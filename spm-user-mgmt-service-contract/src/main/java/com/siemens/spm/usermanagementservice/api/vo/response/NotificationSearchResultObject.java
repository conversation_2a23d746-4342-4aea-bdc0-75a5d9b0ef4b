/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : NotificationSearchResultObject.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.api.vo.response;

import java.io.Serializable;
import java.util.List;

import org.springframework.http.HttpStatus;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.common.shared.vo.AbstractResultObject;
import com.siemens.spm.usermanagementservice.api.vo.NotificationSearchVO;
import com.siemens.spm.usermanagementservice.api.vo.response.NotificationSearchResultObject.ResponseData;
import com.siemens.spm.usermanagementservice.api.vo.response.NotificationSearchResultObject.StatusCode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

@JsonInclude(Include.NON_NULL)
public class NotificationSearchResultObject extends AbstractResultObject<ResponseData, StatusCode> {

    private static final long serialVersionUID = -9205084441657959286L;

    private ResponseData data;

    private StatusCode statusCode;

    /**
     * Constructor for ERROR case
     */
    public NotificationSearchResultObject() {
    }

    /**
     * Constructor to set status to other than success
     *
     * @param data
     * @param statusCode
     */
    public NotificationSearchResultObject(ResponseData data, StatusCode statusCode) {
        super(data, statusCode);
    }

    /**
     * Constructor for SUCCESS case
     *
     * @param data
     */
    public NotificationSearchResultObject(ResponseData data) {
        super(data);
    }

    @Override
    protected void setData(ResponseData value) {
        this.data = value;

    }

    @Override
    protected void setStatusCode(StatusCode value) {
        this.statusCode = value;
    }

    @Override
    protected StatusCode getErrorStatusValue() {

        return StatusCode.ERROR;
    }

    public String getMessage() {
        return statusCode.getMessage();
    }

    @Override
    public HttpStatus getHttpStatus() {
        return statusCode.getHttpStatus();
    }

    @Override
    public String getErrorFieldName() {
        return statusCode.getErrorField();
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(Include.NON_NULL)
    public static class ResponseData implements Serializable {

        private static final long serialVersionUID = -7816514467488457821L;

        @JsonProperty("total_count")
        private Long totalCount;

        @JsonProperty("notifications")
        private List<NotificationSearchVO> notifications;

    }

    @Override
    public ResponseData getData() {
        return data;
    }

    @Override
    public StatusCode getSuccessfulStatusValue() {
        return StatusCode.SUCCESS;
    }

    @Override
    public StatusCode getStatusCode() {
        return statusCode;
    }

    @Getter
    @AllArgsConstructor
    public enum StatusCode {
        /** OK */
        SUCCESS("success", HttpStatus.OK),
        NO_DATA("no_data", HttpStatus.NO_CONTENT),

        /** Internal Server Error */
        ERROR("unknown_exception", HttpStatus.INTERNAL_SERVER_ERROR),

        /** Bad Request */
        INVALID_SORT_COLUMN("sort", "invalid_sort_column", HttpStatus.BAD_REQUEST),
        INVALID_SORT_ORDER("sort", "invalid_sort_order", HttpStatus.BAD_REQUEST),
        INVALID_LOCATION("location", "invalid_location", HttpStatus.BAD_REQUEST),

        USER_NOT_FOUND("user_not_found", HttpStatus.UNAUTHORIZED),

        /** Not Found */
        AGENCY_NOT_FOUND("agency_id", "agency_not_found", HttpStatus.NOT_FOUND),

        /** Intersection errors */
        INTERSECTION_DISABLED("intersection", "intersection_unavailable", HttpStatus.FORBIDDEN),
        INTERSECTION_NOT_FOUND("intersection", "intersection_not_found", HttpStatus.NOT_FOUND)
        ;

        private final String errorField;
        private final String message;
        private final HttpStatus httpStatus;

        StatusCode(String message, HttpStatus httpStatus) {
            this(null, message, httpStatus);
        }
    }

}
