/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : IntersectionInternalVO.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.api.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_NULL)
public class IntersectionInternalVO implements Serializable {

    private static final long serialVersionUID = -2279640237607189877L;

    @JsonProperty("id")
    private String id;

    @JsonProperty("name")
    private String name;

    @JsonProperty("number")
    private String number;

    @JsonProperty("status")
    private String status;

    @JsonProperty("timezone")
    private String timezone;

}
