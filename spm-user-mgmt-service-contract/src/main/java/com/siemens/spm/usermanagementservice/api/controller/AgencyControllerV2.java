/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AgencyController.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.api.controller;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;

import com.siemens.spm.common.api.PublicController;
import com.siemens.spm.common.constant.AgencyConstants;
import com.siemens.spm.usermanagementservice.api.vo.request.AgencyLicenseUpdateRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.request.SetAgencySettingsRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.response.AgencyDetailResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.AgencyLicenseResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.AgencySettingsResultObject;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

/**
 * <AUTHOR> Nguyen
 */
@RequestMapping(AgencyControllerV2.URL_ROOT)
@Tag(name = "agency-v2", description = "Agency API")
@Validated
public interface AgencyControllerV2 extends PublicController {

    String VERSION = "/v2";
    String AGENCIES_RESOURCE = "/agency";
    String URL_ROOT = PUBLIC_API + VERSION + AGENCIES_RESOURCE;

    /**
     * Get agency detail by Id
     *
     * @param agencyId
     * @return AgencyDetailResultObject
     */
    @Operation(summary = "Get agency detail by Id")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "400", description = "Bad Request"),
            @ApiResponse(responseCode = "404", description = "Not Found")
    })
    @Parameter(name = "Authorization", example = "Bearer {jwt}",
            required = true, in = ParameterIn.HEADER, description = "Bearer Token type")

    @ResponseStatus(HttpStatus.OK)
    @GetMapping
    ResponseEntity<AgencyDetailResultObject> getAgencyDetail(
            @Parameter(name = AgencyConstants.AGENCY_ID_HEADER_V2, 
                       example = AgencyConstants.AGENCY_ID_HEADER_V2 + "=4246",
                       required = true,
                       in = ParameterIn.HEADER, description = "Agency Id")
            @RequestHeader(AgencyConstants.AGENCY_ID_HEADER_V2) Integer agencyId
    );

    // TODO Update API doc

    /**
     * GET /agency/settings
     */
    @GetMapping("settings")
    @ResponseStatus(HttpStatus.OK)
    ResponseEntity<AgencySettingsResultObject> getAgencySettings(
            @Parameter(name = AgencyConstants.AGENCY_ID_HEADER_V2, 
                       example = AgencyConstants.AGENCY_ID_HEADER_V2 + "=4246",
                       required = true,
                       in = ParameterIn.HEADER, description = "Agency Id")
            @RequestHeader(AgencyConstants.AGENCY_ID_HEADER_V2) Integer agencyId
    );

    // TODO Update API doc

    /**
     * PUT /agency/settings
     */
    @PutMapping("settings")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    ResponseEntity<AgencySettingsResultObject> setAgencySettings(
            @Parameter(name = AgencyConstants.AGENCY_ID_HEADER_V2, 
                       example = AgencyConstants.AGENCY_ID_HEADER_V2 + "=4246",
                       required = true,
                       in = ParameterIn.HEADER, description = "Agency Id")
            @RequestHeader(AgencyConstants.AGENCY_ID_HEADER_V2) Integer agencyId,

            @Valid @NotNull(message = "agency_settings_must_not_be_null")
            @RequestBody
            SetAgencySettingsRequestVO requestVO
    );

    /**
     * GET /agency/licenses
     */
    @GetMapping("licenses")
    @ResponseStatus(HttpStatus.OK)
    ResponseEntity<AgencyLicenseResultObject> getAgencyLicenses(
            @Parameter(name = AgencyConstants.AGENCY_ID_HEADER_V2, 
                       example = AgencyConstants.AGENCY_ID_HEADER_V2 + "=4246",
                       required = true,
                       in = ParameterIn.HEADER, description = "Agency Id")
            @RequestHeader(AgencyConstants.AGENCY_ID_HEADER_V2) Integer agencyId
    );

    /**
     * PUT /agency/licenses
     */
    @PutMapping("licenses")
    @ResponseStatus(HttpStatus.OK)
    ResponseEntity<AgencyLicenseResultObject> setAgencyLicenses(
            @Parameter(name = AgencyConstants.AGENCY_ID_HEADER_V2, 
                       example = AgencyConstants.AGENCY_ID_HEADER_V2 + "=4246",
                       required = true,
                       in = ParameterIn.HEADER, description = "Agency Id")
            @RequestHeader(AgencyConstants.AGENCY_ID_HEADER_V2) Integer agencyId,

            @Valid @NotNull(message = "agency_settings_must_not_be_null")
            @RequestBody
            AgencyLicenseUpdateRequestVO requestVO
    );

}
