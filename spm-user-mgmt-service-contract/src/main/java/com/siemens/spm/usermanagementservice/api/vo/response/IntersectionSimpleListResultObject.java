/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : IntersectionSimpleListResultObject.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.api.vo.response;

import java.util.ArrayList;

import org.springframework.http.HttpStatus;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.siemens.spm.common.shared.vo.AbstractResultObject;
import com.siemens.spm.usermanagementservice.api.vo.IntersectionSimpleVO;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionSimpleListResultObject.StatusCode;

import lombok.AllArgsConstructor;
import lombok.Getter;

@JsonInclude(Include.NON_NULL)
public class IntersectionSimpleListResultObject extends
        AbstractResultObject<ArrayList<IntersectionSimpleVO>, StatusCode> {

    private static final long serialVersionUID = -4078984043014122337L;

    private ArrayList<IntersectionSimpleVO> data;

    private StatusCode statusCode;

    /**
     * Constructor for ERROR case
     */
    public IntersectionSimpleListResultObject() {

    }

    /**
     * Constructor to set status to other than success
     *
     * @param data
     * @param statusCode
     */
    public IntersectionSimpleListResultObject(ArrayList<IntersectionSimpleVO> data, StatusCode statusCode) {
        super(data, statusCode);
    }

    /**
     * Constructor for SUCCESS case
     *
     * @param data
     */
    public IntersectionSimpleListResultObject(ArrayList<IntersectionSimpleVO> data) {
        super(data);
    }

    /**
     * Constructor for FAILURE case
     * @param statusCode Value of {@code IntersectionSimpleListResultObject.StatusCode} enum
     */
    public IntersectionSimpleListResultObject(StatusCode statusCode) {
        this.statusCode = statusCode;
    }

    @Override
    public ArrayList<IntersectionSimpleVO> getData() {
        return data;
    }

    @Override
    protected StatusCode getErrorStatusValue() {

        return StatusCode.ERROR;
    }

    @Override
    public StatusCode getSuccessfulStatusValue() {
        return StatusCode.SUCCESS;
    }

    @Override
    public HttpStatus getHttpStatus() {
        return statusCode.getHttpStatus();
    }

    @Override
    public String getErrorFieldName() {
        return this.statusCode.getErrorField();
    }

    @Override
    protected void setStatusCode(StatusCode value) {
        this.statusCode = value;
    }

    @AllArgsConstructor
    @Getter
    public enum StatusCode {
        /** OK */
        SUCCESS("success", HttpStatus.OK),

        /** Internal Server Error */
        ERROR("unknown_exception", HttpStatus.INTERNAL_SERVER_ERROR),

        /** Not found */
        USER_NOT_FOUND("user_not_found", HttpStatus.BAD_REQUEST),
        AGENCY_NOT_FOUND("agency_not_found", HttpStatus.BAD_REQUEST),
        INTERSECTION_NOT_FOUND("intersection_not_found", HttpStatus.BAD_REQUEST),
        INVALID_STATUS("invalid_status", HttpStatus.BAD_REQUEST);

        private final String errorField;
        private final String message;
        private final HttpStatus httpStatus;

        StatusCode(String message, HttpStatus httpStatus) {
            this(null, message, httpStatus);
        }
    }

    @Override
    public StatusCode getStatusCode() {
        return statusCode;
    }

    @Override
    protected void setData(ArrayList<IntersectionSimpleVO> value) {
        this.data = value;

    }

    public String getMessage() {
        return statusCode.getMessage();
    }

}
