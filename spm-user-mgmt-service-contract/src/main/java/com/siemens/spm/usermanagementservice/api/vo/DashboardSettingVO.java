package com.siemens.spm.usermanagementservice.api.vo;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DashboardSettingVO implements Serializable {

    private static final long serialVersionUID = -2564501290533921658L;

    @JsonProperty("open_alarm_widget")
    private OpenAlarmWidgetVO openAlarmWidgetVO;

    @JsonProperty("map_view_widget")
    private MapViewWidgetVO mapViewWidgetVO;

    @JsonProperty("top_int_open_alarm_widget")
    private TopIntOpenAlarmWidgetVO topIntOpenAlarmWidgetVO;

    @JsonProperty("summary_report_widget")
    private SummaryReportWidgetVO summaryReportWidgetVO;

    @JsonProperty("volume_report_widget")
    private VolumeReportWidgetVO volumeReportWidgetVO;

    @JsonProperty("notification_widget")
    private NotificationWidgetVO notificationWidgetVO;

}
