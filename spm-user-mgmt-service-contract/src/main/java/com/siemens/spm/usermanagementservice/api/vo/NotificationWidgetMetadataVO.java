package com.siemens.spm.usermanagementservice.api.vo;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * This class contain metadata to setting for {@link NotificationWidgetVO}
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class NotificationWidgetMetadataVO implements Serializable {

    private static final long serialVersionUID = -5776183122381331610L;

    @JsonProperty("notification_types")
    private transient List<NotificationTypeWidgetVO> notificationTypes;

}
