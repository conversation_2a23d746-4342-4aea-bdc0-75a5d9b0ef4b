/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : UserKeyVO.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.api.vo;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_NULL)
public class UserKeyVO implements Serializable {

    private static final long serialVersionUID = 3682087082572475554L;

    @JsonProperty("user_key")
    private String userKey;

    @JsonProperty("user_key_expiration")
    private Date userKeyExpiration;

}
