// /*
//  * Copyright (C) Siemens.  All Rights Reserved.
//  *
//  * Source      : UserController.java
//  * Project     : SPM Platform
//  */
// package com.siemens.spm.usermanagementservice.api.controller;

// import org.springframework.http.HttpStatus;
// import org.springframework.http.ResponseEntity;
// import org.springframework.web.bind.annotation.GetMapping;
// import org.springframework.web.bind.annotation.RequestMapping;
// import org.springframework.web.bind.annotation.RequestParam;
// import org.springframework.web.bind.annotation.ResponseStatus;

// import com.siemens.spm.common.api.PublicController;
// import com.siemens.spm.usermanagementservice.api.vo.response.UserSimpleListResultObject;
// import io.swagger.v3.oas.annotations.Operation;
// import io.swagger.v3.oas.annotations.Parameter;
// import io.swagger.v3.oas.annotations.enums.ParameterIn;
// import io.swagger.v3.oas.annotations.responses.ApiResponse;
// import io.swagger.v3.oas.annotations.responses.ApiResponses;
// import io.swagger.v3.oas.annotations.tags.Tag;

// /**
//  * <AUTHOR> Ngo & Tuan Nguyen
//  */
// @RequestMapping(UserController.URL_ROOT)
// @Tag(name = "user", description = "User API")
// public interface UserController extends PublicController {

//     String VERSION = "/v1";
//     String USER_RESOURCE = "/users";
//     String URL_ROOT = PUBLIC_API + VERSION + USER_RESOURCE;

//     /**
//      * GET  /users/all?agency_id={agency_id}  : Get all simple users by agencyId
//      *
//      * @return UserListResultObject
//      */
//     @Operation(summary = "Get all simple users by agencyId")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "400", description = "Bad Request"),
//             @ApiResponse(responseCode = "404", description = "Not Found")
//     })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER, description = "Bearer Token type")

//     @ResponseStatus(HttpStatus.OK)
//     @GetMapping("all")
//     ResponseEntity<UserSimpleListResultObject> getAllSimpleUsers(
//             @Parameter(name = "agency_id", description = "Agency Id")
//             @RequestParam(value = "agency_id", required = false)
//             Integer agencyId
//     );

// }
