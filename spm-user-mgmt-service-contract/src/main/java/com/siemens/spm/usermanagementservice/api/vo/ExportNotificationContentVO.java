/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : ExportNotificationContentVO.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.api.vo;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.common.constant.CommonConstants;
import com.siemens.spm.common.message.MessageService;
import com.siemens.spm.common.shared.domaintype.notification.DynamicContentType;
import com.siemens.spm.common.shared.domaintype.notification.NotificationContentVO;
import com.siemens.spm.common.shared.vo.DynamicContentVO;
import com.siemens.spm.common.util.BeanFinder;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class ExportNotificationContentVO implements NotificationContentVO {

    private static final long serialVersionUID = -6265337386954473780L;

    @JsonProperty("intersection_id")
    private String intersectionId;

    @JsonProperty("intersection_name")
    private String intersectionName;

    @Override
    @JsonIgnore
    public List<DynamicContentVO> getDynamicContents() {
        List<DynamicContentVO> dynamicContents = new ArrayList<>();

        MessageService messageService = BeanFinder.getDefaultMessageService();

        DynamicContentVO intersectionNameContent = DynamicContentVO.builder()
                .type(DynamicContentType.TEXT)
                .label(messageService.getMessage("alarm.record.intersection_name"))
                .values(intersectionName != null ? intersectionName : CommonConstants.NOT_AVAILABLE)
                .build();
        dynamicContents.add(intersectionNameContent);

        return dynamicContents;
    }

}
