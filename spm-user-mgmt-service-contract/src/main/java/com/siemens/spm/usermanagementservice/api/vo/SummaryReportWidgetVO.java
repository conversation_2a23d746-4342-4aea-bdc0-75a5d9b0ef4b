package com.siemens.spm.usermanagementservice.api.vo;

import java.sql.Timestamp;
import java.util.List;

import jakarta.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.usermanagementservice.api.constant.PeriodType;
import com.siemens.spm.usermanagementservice.api.constant.WidgetIds;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * This class used for serialization to persist data into DB. Be careful when have any change in this class(field,
 * annotation, ..). This can break when deserialize data fetch from DB
 *
 * <AUTHOR> Nguyen - <EMAIL>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SummaryReportWidgetVO extends AbstractWidgetVO {

    private static final long serialVersionUID = 2756179757942767215L;

    @NotNull
    @JsonProperty("period_type")
    private PeriodType periodType;

    /**
     * Only in case period is custom
     */
    @JsonProperty("from_time")
    private Timestamp fromTime;

    /**
     * Only in case period is custom
     */
    @JsonProperty("to_time")
    private Timestamp toTime;

    @NotNull
    @JsonProperty("metrics")
    private List<SummaryReportWidgetMetricVO> metricVOList;

    @Override
    public String getWidgetId() {
        return WidgetIds.SUMMARY_REPORT;
    }

}
