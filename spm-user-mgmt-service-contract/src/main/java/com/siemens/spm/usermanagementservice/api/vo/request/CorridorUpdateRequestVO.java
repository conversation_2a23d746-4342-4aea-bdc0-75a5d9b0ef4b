/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : CorridorUpdateRequestVO.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.api.vo.request;

import java.io.Serializable;
import java.util.List;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.usermanagementservice.api.vo.constraint.ValidCorridorIntersections;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class CorridorUpdateRequestVO implements Serializable {

    private static final long serialVersionUID = 3465235031144740535L;

    @NotBlank(message = "name_required")
    @JsonProperty("name")
    private String name;

    @NotNull(message = "speed_required")
    @JsonProperty("speed")
    private Double speed;

    @Min(value = 1, message = "global_upstream_phase_min_is_1")
    @Max(value = 16, message = "global_upstream_phase_max_is_16")
    @NotNull(message = "global_upstream_phase_required")
    @JsonProperty("global_upstream_phase")
    private Integer globalUpstreamPhase;

    @Min(value = 1, message = "global_downstream_phase_min_is_1")
    @Max(value = 16, message = "global_downstream_phase_max_is_16")
    @NotNull(message = "global_downstream_phase_required")
    @JsonProperty("global_downstream_phase")
    private Integer globalDownstreamPhase;

    @Valid
    @ValidCorridorIntersections(message = "corridor_intersections_duplicated")
    @JsonProperty("corridor_intersections")
    private List<CorridorIntersectionCreateRequestVO> corridorIntersections;

}
