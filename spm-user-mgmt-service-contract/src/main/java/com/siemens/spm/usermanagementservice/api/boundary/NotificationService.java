/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : NotificationService.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.api.boundary;

import java.util.List;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.siemens.spm.usermanagementservice.api.vo.ReleaseNoteVO;
import com.siemens.spm.usermanagementservice.api.vo.request.NotificationSearchRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.request.NotificationUpdatedRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.request.NotificationsCreateRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.response.NotificationDetailsResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.NotificationLatestUnreadResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.NotificationManipulateResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.NotificationSearchResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.NotificationTypeListResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.NotificationsCreateResultObject;

public interface NotificationService {

    /**
     * Create notifications for users
     *
     * @param requestVO
     * @return NotificationsCreateResultObject
     */
    NotificationsCreateResultObject createNotificationsForUsersInternal(NotificationsCreateRequestVO requestVO);

    /**
     * Create release note notifications to all users
     *
     * @return NotificationsCreateResultObject
     */
    NotificationsCreateResultObject createReleaseNoteNotifications(ReleaseNoteVO releaseNoteVO);

    /**
     * Get latest unread notifications
     *
     * @param page
     * @param size
     * @return LastestUnreadNotificationResultObject
     */
    NotificationLatestUnreadResultObject getLatestUnreadNotifications(Integer agencyId, Integer page, Integer size);

    /**
     * search/filter notifications
     *
     * @param searchRequest
     * @return NotificationSearchResultObject
     * @throws JsonProcessingException
     * @throws JsonMappingException
     */
    NotificationSearchResultObject searchNotifications(NotificationSearchRequestVO searchRequest)
            throws JsonMappingException, JsonProcessingException;

    /**
     * Update notifications
     *
     * @param updateRequest
     * @return NotificationManipulateResultObject
     */
    NotificationManipulateResultObject updateNotifications(NotificationUpdatedRequestVO updateRequest);

    /**
     * Mark all notifications as read (for current user)
     *
     * @return NotificationManipulateResultObject
     */
    NotificationManipulateResultObject markAllNotificationsAsRead(Integer agencyId);

    /**
     * Delete Notifications (for current user)
     *
     * @return NotificationManipulateResultObject
     */
    NotificationManipulateResultObject deleteNotifications(List<Long> notiIds);

    /**
     * Get Notification in detail
     *
     * @param notificationId
     * @return NotificationDetailsResultObject
     */
    NotificationDetailsResultObject getNotificationDetails(Long notificationId);

    /**
     * Get all notification types
     *
     * @return NotificationTypeListResultObject
     */
    NotificationTypeListResultObject getAllNotificationTypes();

}
