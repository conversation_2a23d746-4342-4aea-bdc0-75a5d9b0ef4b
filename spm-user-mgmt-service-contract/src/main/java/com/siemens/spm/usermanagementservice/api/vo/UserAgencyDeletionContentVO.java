/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AlarmNotificationContentVO.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.api.vo;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.common.message.MessageService;
import com.siemens.spm.common.shared.domaintype.notification.DynamicContentType;
import com.siemens.spm.common.shared.domaintype.notification.NotificationContentVO;
import com.siemens.spm.common.shared.vo.DynamicContentVO;
import com.siemens.spm.common.util.BeanFinder;
import com.siemens.spm.common.util.ListUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class UserAgencyDeletionContentVO implements NotificationContentVO {

    private static final long serialVersionUID = 4685980564200562509L;

    @JsonProperty("deleted_by")
    private String deletedBy;

    @SuppressWarnings("java:S3986")
    @Override
    @JsonIgnore
    public List<DynamicContentVO> getDynamicContents() {
        MessageService messageService = BeanFinder.getDefaultMessageService();
        return ListUtil.arrayListOf(DynamicContentVO.builder()
                .type(DynamicContentType.TEXT)
                .label(messageService.getMessage("deleted_by"))
                .values(deletedBy)
                .build()
        );
    }

}
