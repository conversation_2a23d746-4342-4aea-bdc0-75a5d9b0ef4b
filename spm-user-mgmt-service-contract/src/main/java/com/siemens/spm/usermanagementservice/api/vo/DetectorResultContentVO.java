package com.siemens.spm.usermanagementservice.api.vo;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.util.StringUtils;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.common.message.MessageService;
import com.siemens.spm.common.shared.domaintype.ActionTarget;
import com.siemens.spm.common.shared.domaintype.notification.DynamicContentType;
import com.siemens.spm.common.shared.domaintype.notification.NotificationContentVO;
import com.siemens.spm.common.shared.vo.DynamicContentVO;
import com.siemens.spm.common.shared.vo.HyperlinkContentVO;
import com.siemens.spm.common.util.BeanFinder;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DetectorResultContentVO implements NotificationContentVO {

    private static final long serialVersionUID = -1445597860247224881L;

    /**
     * {@code (key, value) ==> (id, name)}. Where:
     * <p>
     * {@code id} is id of result
     * <p>
     * {@code name} is name of intersection which calculated in the result
     */
    @JsonProperty("results")
    private Map<String, String> results;

    @JsonProperty("from_time")
    private LocalTime fromTime;

    @JsonProperty("to_time")
    private LocalTime toTime;

    @JsonProperty("from_date")
    private LocalDate fromDate;

    @JsonProperty("to_date")
    private LocalDate toDate;

    @JsonProperty("week_days")
    private Set<DayOfWeek> weekDays;

    @JsonProperty("time_zone")
    private String timeZone;

    @Override
    @JsonIgnore
    public List<DynamicContentVO> getDynamicContents() {
        List<DynamicContentVO> dynamicContents = new ArrayList<>();

        MessageService messageService = BeanFinder.getDefaultMessageService();

        if (results != null && !results.isEmpty()) {
            dynamicContents.add(buildResultsDynamicContent());
        } else {
            DynamicContentVO emptyListContent = DynamicContentVO.builder()
                    .type(DynamicContentType.TEXT)
                    .label(messageService.getMessage("detector_report.results.result_list"))
                    .values(messageService.getMessage("detector_report.results.empty_list"))
                    .build();
            dynamicContents.add(emptyListContent);
        }

        if (StringUtils.hasText(timeZone)) {
            DynamicContentVO timezoneContent = DynamicContentVO.builder()
                    .type(DynamicContentType.TEXT)
                    .label(messageService.getMessage("detector_report.results.timezone"))
                    .values(timeZone)
                    .build();
            dynamicContents.add(timezoneContent);
        }

        if (fromTime != null) {
            DynamicContentVO fromTimeContent = DynamicContentVO.builder()
                    .type(DynamicContentType.TEXT)
                    .label(messageService.getMessage("detector_report.results.from_time"))
                    .values(fromTime.format(DateTimeFormatter.ISO_LOCAL_TIME))
                    .build();
            dynamicContents.add(fromTimeContent);
        }

        if (toTime != null) {
            DynamicContentVO toTimeContent = DynamicContentVO.builder()
                    .type(DynamicContentType.TEXT)
                    .label(messageService.getMessage("detector_report.results.to_time"))
                    .values(toTime.format(DateTimeFormatter.ISO_LOCAL_TIME))
                    .build();
            dynamicContents.add(toTimeContent);
        }

        if (fromDate != null) {
            DynamicContentVO fromDateContent = DynamicContentVO.builder()
                    .type(DynamicContentType.TEXT)
                    .label(messageService.getMessage("detector_report.results.from_date"))
                    .values(fromDate.format(DateTimeFormatter.ISO_LOCAL_DATE))
                    .build();
            dynamicContents.add(fromDateContent);
        }

        if (toDate != null) {
            DynamicContentVO toDateContent = DynamicContentVO.builder()
                    .type(DynamicContentType.TEXT)
                    .label(messageService.getMessage("detector_report.results.to_date"))
                    .values(toDate.format(DateTimeFormatter.ISO_LOCAL_DATE))
                    .build();
            dynamicContents.add(toDateContent);
        }

        if (weekDays != null) {
            String dayOfWeekString = weekDays.stream()
                    .map(Object::toString)
                    .collect(Collectors.joining(", "));
            DynamicContentVO weekDaysContent = DynamicContentVO.builder()
                    .type(DynamicContentType.TEXT)
                    .label(messageService.getMessage("detector_report.results.week_days"))
                    .values(dayOfWeekString)
                    .build();
            dynamicContents.add(weekDaysContent);
        }

        return dynamicContents;
    }

    @JsonIgnore
    private DynamicContentVO buildResultsDynamicContent() {
        MessageService messageService = BeanFinder.getDefaultMessageService();

        List<HyperlinkContentVO> hyperlinkContents = new ArrayList<>();
        for (Map.Entry<String, String> entry : results.entrySet()) {
            HyperlinkContentVO hyperlinkContent = HyperlinkContentVO.builder()
                    .id(String.valueOf(entry.getKey()))
                    .text(entry.getValue())
                    .target(ActionTarget.PM_RESULTS)
                    .build();
            hyperlinkContents.add(hyperlinkContent);
        }

        return DynamicContentVO.builder()
                .type(DynamicContentType.HYPERLINKS)
                .label(messageService.getMessage("detector_report.results.result_list"))
                .values(hyperlinkContents)
                .build();
    }
}
