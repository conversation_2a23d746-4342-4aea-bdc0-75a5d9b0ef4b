/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AccountService.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.api.boundary;

import com.siemens.spm.usermanagementservice.api.vo.request.LanguageRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.request.UserKeyRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.response.AgencyUserAccessResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.ChangeLanguageResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.UserKeyResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.UserResultObject;

/**
 * This service provides APIs for retrieving/editing user info
 */
public interface AccountService {

    /**
     * Get current user
     *
     * @return UserResultObject
     * @throws Exception
     */
    UserResultObject getCurrentUser() throws Exception;

    /**
     * Create user key
     *
     * @param currentPasswordVO
     * @return UserKeyResultObject
     * @throws Exception
     */
    UserKeyResultObject createUserKey(UserKeyRequestVO currentPasswordVO) throws Exception;

    /**
     * Get user key
     *
     * @param currentPasswordVO
     * @return
     * @throws Exception
     */
    UserKeyResultObject getUserKey(UserKeyRequestVO currentPasswordVO) throws Exception;

    /**
     * Update user's language.
     *
     * @param langRequestVO
     * @return ChangeLanguageResultObject
     */
    ChangeLanguageResultObject updateLanguage(LanguageRequestVO langRequestVO);

    AgencyUserAccessResultObject accessAgency(Integer agencyId);

}
