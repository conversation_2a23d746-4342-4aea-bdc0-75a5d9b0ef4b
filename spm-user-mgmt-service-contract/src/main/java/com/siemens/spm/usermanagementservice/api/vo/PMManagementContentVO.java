package com.siemens.spm.usermanagementservice.api.vo;

import java.util.ArrayList;
import java.util.List;

import org.springframework.util.StringUtils;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.common.shared.domaintype.analysis.AnalysisType;
import com.siemens.spm.common.message.MessageService;
import com.siemens.spm.common.shared.domaintype.notification.DynamicContentType;
import com.siemens.spm.common.shared.domaintype.notification.NotificationContentVO;
import com.siemens.spm.common.shared.vo.DynamicContentVO;
import com.siemens.spm.common.util.BeanFinder;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PMManagementContentVO implements NotificationContentVO {

    private static final long serialVersionUID = 4171882410913581019L;

    @JsonProperty("metric_type")
    private String metricType;

    @JsonProperty("template_desc")
    private String templateDesc;

    @Override
    @JsonIgnore
    public List<DynamicContentVO> getDynamicContents() {
        List<DynamicContentVO> dynamicContents = new ArrayList<>();

        MessageService messageService = BeanFinder.getDefaultMessageService();

        AnalysisType type = AnalysisType.getById(metricType);
        if (type != null) {
            DynamicContentVO metricTypeContent = DynamicContentVO.builder()
                    .type(DynamicContentType.TEXT)
                    .label(messageService.getMessage("performance-metric.templates.metric-type"))
                    .values(type.getTranslatedName())
                    .build();
            dynamicContents.add(metricTypeContent);
        }

        if (StringUtils.hasText(templateDesc)) {
            DynamicContentVO templateDescContent = DynamicContentVO.builder()
                    .type(DynamicContentType.TEXT)
                    .label(messageService.getMessage("performance-metric.templates.description"))
                    .values(templateDesc)
                    .build();
            dynamicContents.add(templateDescContent);
        }

        return dynamicContents;
    }
}
