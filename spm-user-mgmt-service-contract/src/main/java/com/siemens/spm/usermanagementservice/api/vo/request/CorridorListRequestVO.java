package com.siemens.spm.usermanagementservice.api.vo.request;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class CorridorListRequestVO implements Serializable {

  private static final long serialVersionUID = -724292919426517806L;

  @JsonProperty("text")
  private String text;

  @JsonProperty("status")
  private String status;

  @JsonProperty("agency_id")
  private Integer agencyId;

  @JsonProperty("order_by_columns")
  private String[] orderByColumns;

  @JsonProperty("page")
  private Integer page;

  @JsonProperty("size")
  private Integer size;

  @JsonProperty("should_paginate")
  private boolean shouldPaginate;
}
