package com.siemens.spm.analysis.factory;

import com.siemens.spm.analysis.vo.PtChartVO;
import com.siemens.spm.analysis.vo.PtPlanStatisticsVO;
import com.siemens.spm.analysis.vo.TerminatePhaseEventVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

class PtChartBuilderTest {

    private static final int PHASE_NUM = 1;
    private static final int DET_NUM = 1; // Detector number is 1, assuming mapped to phase 1

    private static final int YEAR = 2021;
    private static final int MONTH = 1;
    private static final int DAY = 4;
    private static final int SECONDS_PER_MINUTE = 60;
    private static final LocalDateTime T0 = LocalDateTime.of(YEAR, MONTH, DAY, 12, 0);
    private static final LocalDateTime T1 = LocalDateTime.of(YEAR, MONTH, DAY, 13, 0);
    private static final LocalDateTime T2 = LocalDateTime.of(YEAR, MONTH, DAY, 14, 0);

    private PtChartBuilder chartBuilder;

    private PtChartVO chartVO;

    @BeforeEach
    public void setUp() {
        chartVO = new PtChartVO();
        chartVO.setPhase(PHASE_NUM);
        chartVO.setFromTime(T0);
        chartVO.setToTime(T2);

        chartBuilder = new PtChartBuilder(chartVO);
        chartBuilder.init();
    }

    @Test
    void test_build() {
        List<PerfLogEventVO> eventList = createListOfEvents();

        // Add events
        for (PerfLogEventVO eventVO : eventList) {
            chartBuilder.putEvent(eventVO);
        }
        chartBuilder.build();

        Assertions.assertEquals(13, chartVO.getPtList().size());

        List<TerminatePhaseEventVO> actualPtList = chartVO.getActualPtList();
        Assertions.assertEquals(2, actualPtList.size());

        TerminatePhaseEventVO terminatePhaseEventVO1 = actualPtList.get(0);
        Assertions.assertNotNull(terminatePhaseEventVO1);
        Assertions.assertEquals(T0.plusMinutes(5), terminatePhaseEventVO1.getDatetime());
        Assertions.assertEquals(0, terminatePhaseEventVO1.getPhaseTime());

        TerminatePhaseEventVO terminatePhaseEventVO2 = actualPtList.get(1);
        Assertions.assertNotNull(terminatePhaseEventVO2);
        Assertions.assertEquals(T1.plusMinutes(7), terminatePhaseEventVO2.getDatetime());
        Assertions.assertEquals(7 * SECONDS_PER_MINUTE, terminatePhaseEventVO2.getPhaseTime());

        List<PtPlanStatisticsVO> ptPlanStatisticsVOS = chartVO.getPlanStatistics();
        Assertions.assertEquals(1, ptPlanStatisticsVOS.size());

        PtPlanStatisticsVO ptPlanStatisticsVO = ptPlanStatisticsVOS.get(0);
        Assertions.assertEquals(10, ptPlanStatisticsVO.getGapOut());
        Assertions.assertEquals(1, ptPlanStatisticsVO.getActualGapOut());
        Assertions.assertEquals(50, ptPlanStatisticsVO.getActualGapOutPercent());

        Assertions.assertEquals(1, ptPlanStatisticsVO.getMaxOut());
        Assertions.assertEquals(0, ptPlanStatisticsVO.getActualMaxOut());
        Assertions.assertEquals(0, ptPlanStatisticsVO.getActualMaxOutPercent());

        Assertions.assertEquals(2, ptPlanStatisticsVO.getForceOff());
        Assertions.assertEquals(1, ptPlanStatisticsVO.getActualForceOff());
        Assertions.assertEquals(50, ptPlanStatisticsVO.getActualForceOffPercent());
    }

    private List<PerfLogEventVO> createListOfEvents() {
        List<PerfLogEventVO> perfLogEventVOList = new ArrayList<>();
        // Green time 1
        perfLogEventVOList
                .add(new PerfLogEventVO(T0.plusMinutes(1), PerfLogEventVO.Event.PHASE_GAP_OUT, DET_NUM));
        perfLogEventVOList.add(new PerfLogEventVO(T0.plusMinutes(2), PerfLogEventVO.Event.PHASE_GAP_OUT, DET_NUM));
        perfLogEventVOList.add(new PerfLogEventVO(T0.plusMinutes(3), PerfLogEventVO.Event.PHASE_GAP_OUT, DET_NUM));
        perfLogEventVOList.add(new PerfLogEventVO(T0.plusMinutes(3), PerfLogEventVO.Event.PHASE_MAX_OUT, DET_NUM));
        perfLogEventVOList.add(new PerfLogEventVO(T0.plusMinutes(4), PerfLogEventVO.Event.PHASE_GAP_OUT, DET_NUM));
        perfLogEventVOList.add(new PerfLogEventVO(T0.plusMinutes(5), PerfLogEventVO.Event.PHASE_GAP_OUT, DET_NUM));

        perfLogEventVOList
                .add(new PerfLogEventVO(T0.plusMinutes(6), PerfLogEventVO.Event.PHASE_GREEN_TERMINATION, DET_NUM));
        perfLogEventVOList
                .add(new PerfLogEventVO(T0.plusMinutes(7), PerfLogEventVO.Event.PHASE_BEGIN_YELLOW_CLEARANCE, DET_NUM));
        perfLogEventVOList.add(new PerfLogEventVO(T0.plusMinutes(8), PerfLogEventVO.Event.PHASE_CHECK, DET_NUM));
        perfLogEventVOList
                .add(new PerfLogEventVO(T0.plusMinutes(9), PerfLogEventVO.Event.PHASE_END_YELLOW_CLEARANCE, DET_NUM));
        perfLogEventVOList
                .add(new PerfLogEventVO(T0.plusMinutes(10), PerfLogEventVO.Event.PHASE_BEGIN_RED_CLEARANCE, DET_NUM));
        perfLogEventVOList.add(new PerfLogEventVO(T0.plusMinutes(11), PerfLogEventVO.Event.PHASE_INACTIVE, DET_NUM));
        perfLogEventVOList
                .add(new PerfLogEventVO(T0.plusMinutes(12), PerfLogEventVO.Event.PHASE_END_RED_CLEARANCE, DET_NUM));

        // Green time 2
        perfLogEventVOList.add(new PerfLogEventVO(T1, PerfLogEventVO.Event.PHASE_ON, DET_NUM));
        perfLogEventVOList.add(new PerfLogEventVO(T1.plusMinutes(1), PerfLogEventVO.Event.PHASE_BEGIN_GREEN, DET_NUM));
        perfLogEventVOList.add(new PerfLogEventVO(T1.plusMinutes(2), PerfLogEventVO.Event.PHASE_GAP_OUT, DET_NUM));
        perfLogEventVOList.add(new PerfLogEventVO(T1.plusMinutes(3), PerfLogEventVO.Event.PHASE_MIN_COMPLETE, DET_NUM));
        perfLogEventVOList.add(new PerfLogEventVO(T1.plusMinutes(4), PerfLogEventVO.Event.PHASE_GAP_OUT, DET_NUM));
        perfLogEventVOList.add(new PerfLogEventVO(T1.plusMinutes(5), PerfLogEventVO.Event.PHASE_GAP_OUT, DET_NUM));
        perfLogEventVOList.add(new PerfLogEventVO(T1.plusMinutes(5), PerfLogEventVO.Event.PHASE_FORCE_OFF, DET_NUM));
        perfLogEventVOList.add(new PerfLogEventVO(T1.plusMinutes(6), PerfLogEventVO.Event.PHASE_GAP_OUT, DET_NUM));
        perfLogEventVOList.add(new PerfLogEventVO(T1.plusMinutes(7), PerfLogEventVO.Event.PHASE_GAP_OUT, DET_NUM));
        perfLogEventVOList.add(new PerfLogEventVO(T1.plusMinutes(7), PerfLogEventVO.Event.PHASE_FORCE_OFF, DET_NUM));

        perfLogEventVOList
                .add(new PerfLogEventVO(T1.plusMinutes(10), PerfLogEventVO.Event.PHASE_GREEN_TERMINATION, DET_NUM));
        perfLogEventVOList.add(new PerfLogEventVO(T1.plusMinutes(11), PerfLogEventVO.Event.PHASE_BEGIN_YELLOW_CLEARANCE,
                DET_NUM));
        perfLogEventVOList.add(new PerfLogEventVO(T1.plusMinutes(12), PerfLogEventVO.Event.PHASE_CHECK, DET_NUM));
        perfLogEventVOList
                .add(new PerfLogEventVO(T1.plusMinutes(13), PerfLogEventVO.Event.PHASE_END_YELLOW_CLEARANCE, DET_NUM));
        perfLogEventVOList
                .add(new PerfLogEventVO(T1.plusMinutes(14), PerfLogEventVO.Event.PHASE_BEGIN_RED_CLEARANCE, DET_NUM));
        perfLogEventVOList.add(new PerfLogEventVO(T1.plusMinutes(15), PerfLogEventVO.Event.PHASE_INACTIVE, DET_NUM));
        perfLogEventVOList
                .add(new PerfLogEventVO(T1.plusMinutes(16), PerfLogEventVO.Event.PHASE_END_RED_CLEARANCE, DET_NUM));

        return perfLogEventVOList;
    }

}
