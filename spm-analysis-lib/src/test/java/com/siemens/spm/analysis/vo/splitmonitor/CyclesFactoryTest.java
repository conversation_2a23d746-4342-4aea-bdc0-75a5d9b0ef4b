package com.siemens.spm.analysis.vo.splitmonitor;

import com.siemens.spm.perflog.vo.PerfLogEventVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.*;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive unit tests for the CyclesFactory class.
 * 
 * This test suite covers all public methods with various input scenarios including:
 * - Valid inputs with expected outputs
 * - Boundary conditions (empty collections, null values, minimum/maximum values)
 * - Invalid inputs that should trigger exceptions
 * - Edge cases specific to the business logic
 * 
 * <AUTHOR> Suite Generator
 * @version 1.0
 * @since 2025-07-02
 */
@DisplayName("CyclesFactory Tests")
class CyclesFactoryTest {

    private CyclesFactory cyclesFactory;
    private LocalDateTime baseTime;

    @BeforeEach
    void setUp() {
        cyclesFactory = new CyclesFactory();
        baseTime = LocalDateTime.of(2025, 1, 1, 10, 0, 0);
    }

    /**
     * Creates a PerfLogEventVO with the specified parameters.
     * 
     * @param event the event type
     * @param parameter the phase/parameter number
     * @param dateTime the event timestamp
     * @return a new PerfLogEventVO instance
     */
    private PerfLogEventVO createEvent(PerfLogEventVO.Event event, long parameter, LocalDateTime dateTime) {
        return PerfLogEventVO.builder()
                .event(event)
                .parameter(parameter)
                .dateTime(dateTime)
                .build();
    }

    /**
     * Creates a complete cycle sequence for a single phase.
     * 
     * @param phase the phase number
     * @param startTime the cycle start time
     * @return list of events representing a complete cycle
     */
    private List<PerfLogEventVO> createCompleteCycle(long phase, LocalDateTime startTime) {
        return new ArrayList<>(Arrays.asList(
                createEvent(PHASE_BEGIN_GREEN, phase, startTime),
                createEvent(PHASE_GREEN_TERMINATION, phase, startTime.plusSeconds(30)),
                createEvent(PHASE_BEGIN_YELLOW_CLEARANCE, phase, startTime.plusSeconds(30)),
                createEvent(PHASE_END_YELLOW_CLEARANCE, phase, startTime.plusSeconds(33)),
                createEvent(PHASE_BEGIN_RED_CLEARANCE, phase, startTime.plusSeconds(33)),
                createEvent(PHASE_END_RED_CLEARANCE, phase, startTime.plusSeconds(36))
        ));
    }

    /**
     * CSV Test Data Helper Methods
     */

    /**
     * Loads test data from CSV file and returns events for a specific scenario.
     *
     * @param scenario the scenario identifier to filter by
     * @return list of PerfLogEventVO objects for the scenario
     * @throws IOException if CSV file cannot be read
     */
    private List<PerfLogEventVO> loadTestDataFromCsv(String scenario) throws IOException {
        List<CsvTestDataRow> allData = loadAllCsvTestData();
        return allData.stream()
                .filter(row -> scenario.equals(row.scenario))
                .sorted((a, b) -> Integer.compare(a.ordinalNumber, b.ordinalNumber))
                .map(this::convertToPerLogEventVO)
                .collect(Collectors.toList());
    }

    /**
     * Loads all test data from the CSV file.
     *
     * @return list of all CSV test data rows
     * @throws IOException if CSV file cannot be read
     */
    private List<CsvTestDataRow> loadAllCsvTestData() throws IOException {
        List<CsvTestDataRow> data = new ArrayList<>();

        try (InputStream inputStream = getClass().getResourceAsStream("/test-data/cycles-factory-test-data.csv");
             BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {

            String line;
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                // Skip comments and empty lines
                if (line.isEmpty() || line.startsWith("#")) {
                    continue;
                }

                String[] parts = line.split(",");
                if (parts.length >= 5) {
                    try {
                        CsvTestDataRow row = new CsvTestDataRow(
                                LocalDateTime.parse(parts[0], DateTimeFormatter.ISO_LOCAL_DATE_TIME),
                                parts[1].trim(),
                                Long.parseLong(parts[2].trim()),
                                Integer.parseInt(parts[3].trim()),
                                parts[4].trim()
                        );
                        data.add(row);
                    } catch (Exception e) {
                        // Skip malformed rows
                        System.err.println("Skipping malformed CSV row: " + line + " - " + e.getMessage());
                    }
                }
            }
        }

        return data;
    }

    /**
     * Converts a CSV test data row to a PerfLogEventVO object.
     *
     * @param row the CSV data row
     * @return PerfLogEventVO object
     */
    private PerfLogEventVO convertToPerLogEventVO(CsvTestDataRow row) {
        PerfLogEventVO event = new PerfLogEventVO();
        event.setDateTime(row.datetime);
        event.setParameter(row.param);

        // Convert event type string to enum
        try {
            PerfLogEventVO.Event eventEnum = PerfLogEventVO.Event.valueOf(row.eventType);
            event.setEvent(eventEnum);
        } catch (IllegalArgumentException e) {
            // Handle unknown event types - default to PHASE_ON
            event.setEvent(PerfLogEventVO.Event.PHASE_ON);
        }

        return event;
    }

    /**
     * Gets all available test scenarios from the CSV file.
     *
     * @return list of scenario names
     * @throws IOException if CSV file cannot be read
     */
    private List<String> getAvailableScenarios() throws IOException {
        return loadAllCsvTestData().stream()
                .map(row -> row.scenario)
                .distinct()
                .sorted()
                .collect(Collectors.toList());
    }

    /**
     * Data class to represent a row from the CSV test data file.
     * New format: datetime,event,param,ordinal_number
     * - datetime: Full timestamp with timezone (YYYY-MM-DD HH:mm:ss.SSS +ZZZZ)
     * - event: Numeric event type ID (PerfLogEventVO.Event enum ordinal values)
     * - param: Phase/parameter number
     * - ordinal_number: Sequence number for event ordering
     */
    private static class CsvTestDataRow {
        final LocalDateTime datetime;
        final int eventId;
        final long param;
        final int ordinalNumber;

        CsvTestDataRow(LocalDateTime datetime, int eventId, long param, int ordinalNumber) {
            this.datetime = datetime;
            this.eventId = eventId;
            this.param = param;
            this.ordinalNumber = ordinalNumber;
        }
    }

    /**
     * Provides CSV scenario names for parameterized tests.
     *
     * @return stream of scenario names
     */
    private static Stream<Arguments> csvScenarioProvider() {
        try {
            CyclesFactoryTest testInstance = new CyclesFactoryTest();
            return testInstance.getAvailableScenarios().stream()
                    .map(Arguments::of);
        } catch (IOException e) {
            // Return empty stream if CSV cannot be loaded
            return Stream.empty();
        }
    }

    @Nested
    @DisplayName("CSV Data Loading Tests")
    class CsvDataLoadingTests {

        @Test
        @DisplayName("Should load CSV test data successfully")
        void shouldLoadCsvTestDataSuccessfully() throws IOException {
            // When
            List<String> scenarios = getAvailableScenarios();

            // Then
            assertNotNull(scenarios, "Scenarios should not be null");
            assertFalse(scenarios.isEmpty(), "Should have at least one scenario");

            // Verify expected scenarios are present
            assertTrue(scenarios.contains("complete_single_cycle"),
                    "Should contain complete_single_cycle scenario");
            assertTrue(scenarios.contains("multiple_cycles_phase1"),
                    "Should contain multiple_cycles_phase1 scenario");
            assertTrue(scenarios.contains("multi_phase_simultaneous"),
                    "Should contain multi_phase_simultaneous scenario");
        }

        @Test
        @DisplayName("Should load specific scenario data correctly")
        void shouldLoadSpecificScenarioDataCorrectly() throws IOException {
            // Given
            String scenario = "complete_single_cycle";

            // When
            List<PerfLogEventVO> events = loadTestDataFromCsv(scenario);

            // Then
            assertNotNull(events, "Events should not be null");
            assertFalse(events.isEmpty(), "Events should not be empty");

            // Verify events are sorted by ordinal number (which should correspond to time order)
            for (int i = 1; i < events.size(); i++) {
                PerfLogEventVO prev = events.get(i - 1);
                PerfLogEventVO curr = events.get(i);
                assertTrue(prev.getDateTime().isBefore(curr.getDateTime()) ||
                        prev.getDateTime().equals(curr.getDateTime()),
                        "Events should be sorted by datetime");
            }

            // Verify all events have the expected phase (1 for complete_single_cycle)
            for (PerfLogEventVO event : events) {
                assertEquals(1L, event.getParameter(), "All events should be for phase 1");
            }
        }

        @Test
        @DisplayName("Should handle invalid scenario gracefully")
        void shouldHandleInvalidScenarioGracefully() throws IOException {
            // Given
            String invalidScenario = "non_existent_scenario";

            // When
            List<PerfLogEventVO> events = loadTestDataFromCsv(invalidScenario);

            // Then
            assertNotNull(events, "Events should not be null");
            assertTrue(events.isEmpty(), "Events should be empty for invalid scenario");
        }
    }

    @Nested
    @DisplayName("calculateCycle() Tests")
    class CalculateCycleTests {

        @Test
        @DisplayName("Should handle empty input gracefully")
        void shouldHandleEmptyInput() {
            // Given
            List<PerfLogEventVO> events = new ArrayList<>();

            // When
            List<CycleResult> results = cyclesFactory.calculateCycle(events);

            // Then
            assertNotNull(results, "Results should not be null");
            assertTrue(results.isEmpty(), "Results should be empty for empty input");
        }

        @Test
        @DisplayName("Should calculate complete single cycle from CSV data")
        void shouldCalculateCompleteSingleCycleFromCsv() throws IOException {
            // Given
            List<PerfLogEventVO> events = loadTestDataFromCsv("complete_single_cycle");

            // When
            List<CycleResult> results = cyclesFactory.calculateCycle(events);

            // Then
            assertNotNull(results, "Results should not be null");
            assertFalse(results.isEmpty(), "Results should not be empty for complete cycle");

            CycleResult cycle = results.get(0);
            assertEquals(1L, cycle.getPhase(), "Phase should be 1");
            assertNotNull(cycle.getGreenStart(), "Green start should not be null");
            assertNotNull(cycle.getGreenEnd(), "Green end should not be null");
            assertNotNull(cycle.getYellowStart(), "Yellow start should not be null");
            assertNotNull(cycle.getYellowEnd(), "Yellow end should not be null");
            assertNotNull(cycle.getRedStart(), "Red start should not be null");
            assertNotNull(cycle.getRedEnd(), "Red end should not be null");

            // Verify timing sequence
            assertTrue(cycle.getGreenStart().isBefore(cycle.getGreenEnd()),
                    "Green start should be before green end");
            assertTrue(cycle.getGreenEnd().isBefore(cycle.getYellowStart()) ||
                    cycle.getGreenEnd().equals(cycle.getYellowStart()),
                    "Green end should be before or equal to yellow start");
            assertTrue(cycle.getYellowEnd().isBefore(cycle.getRedStart()) ||
                    cycle.getYellowEnd().equals(cycle.getRedStart()),
                    "Yellow end should be before or equal to red start");
        }

        @Test
        @DisplayName("Should calculate multiple cycles from CSV data")
        void shouldCalculateMultipleCyclesFromCsv() throws IOException {
            // Given
            List<PerfLogEventVO> events = loadTestDataFromCsv("multiple_cycles_phase1");

            // When
            List<CycleResult> results = cyclesFactory.calculateCycle(events);

            // Then
            assertNotNull(results, "Results should not be null");
            assertTrue(results.size() >= 2, "Should have at least 2 cycles");

            // Verify all cycles are for phase 1
            for (CycleResult cycle : results) {
                assertEquals(1L, cycle.getPhase(), "All cycles should be for phase 1");
                assertNotNull(cycle.getGreenStart(), "Green start should not be null");
                assertNotNull(cycle.getRedEnd(), "Red end should not be null");
            }

            // Verify cycles are sorted by green start time
            for (int i = 1; i < results.size(); i++) {
                CycleResult prev = results.get(i - 1);
                CycleResult curr = results.get(i);
                assertTrue(prev.getGreenStart().isBefore(curr.getGreenStart()) ||
                        prev.getGreenStart().equals(curr.getGreenStart()),
                        "Cycles should be sorted by green start time");
            }
        }

        @Test
        @DisplayName("Should handle multi-phase simultaneous cycles from CSV data")
        void shouldHandleMultiPhaseSimultaneousFromCsv() throws IOException {
            // Given
            List<PerfLogEventVO> events = loadTestDataFromCsv("multi_phase_simultaneous");

            // When
            List<CycleResult> results = cyclesFactory.calculateCycle(events);

            // Then
            assertNotNull(results, "Results should not be null");

            // Should have cycles for both phase 1 and phase 2
            Map<Long, List<CycleResult>> cyclesByPhase = results.stream()
                    .collect(Collectors.groupingBy(CycleResult::getPhase));

            assertTrue(cyclesByPhase.containsKey(1L), "Should have cycles for phase 1");
            assertTrue(cyclesByPhase.containsKey(2L), "Should have cycles for phase 2");

            // Verify each phase has complete cycles
            for (Map.Entry<Long, List<CycleResult>> entry : cyclesByPhase.entrySet()) {
                Long phase = entry.getKey();
                List<CycleResult> phaseCycles = entry.getValue();

                for (CycleResult cycle : phaseCycles) {
                    assertEquals(phase, cycle.getPhase(), "Cycle phase should match");
                    assertNotNull(cycle.getGreenStart(), "Green start should not be null for phase " + phase);
                    assertNotNull(cycle.getRedEnd(), "Red end should not be null for phase " + phase);
                }
            }
        }

        @Test
        @DisplayName("Should handle incomplete cycles from CSV data")
        void shouldHandleIncompleteCyclesFromCsv() throws IOException {
            // Given
            List<PerfLogEventVO> events = loadTestDataFromCsv("incomplete_missing_red_end");

            // When
            List<CycleResult> results = cyclesFactory.calculateCycle(events);

            // Then
            assertNotNull(results, "Results should not be null");

            // Incomplete cycles might still be processed, but should have null red end
            if (!results.isEmpty()) {
                CycleResult cycle = results.get(0);
                assertEquals(3L, cycle.getPhase(), "Phase should be 3");
                assertNotNull(cycle.getGreenStart(), "Green start should not be null");
                // Red end might be null for incomplete cycle
            }
        }

        @Test
        @DisplayName("Should handle boundary conditions with same timestamps from CSV data")
        void shouldHandleBoundaryConditionsFromCsv() throws IOException {
            // Given
            List<PerfLogEventVO> events = loadTestDataFromCsv("boundary_same_time");

            // When
            List<CycleResult> results = cyclesFactory.calculateCycle(events);

            // Then
            assertNotNull(results, "Results should not be null");

            if (!results.isEmpty()) {
                CycleResult cycle = results.get(0);
                assertEquals(6L, cycle.getPhase(), "Phase should be 6");

                // Verify that events with same timestamp are handled correctly
                if (cycle.getGreenEnd() != null && cycle.getYellowStart() != null) {
                    assertTrue(cycle.getGreenEnd().equals(cycle.getYellowStart()) ||
                            cycle.getGreenEnd().isBefore(cycle.getYellowStart()),
                            "Green end should be equal to or before yellow start");
                }
            }
        }

        @Test
        @DisplayName("Should handle very short cycle times from CSV data")
        void shouldHandleShortCycleTimesFromCsv() throws IOException {
            // Given
            List<PerfLogEventVO> events = loadTestDataFromCsv("short_cycle_times");

            // When
            List<CycleResult> results = cyclesFactory.calculateCycle(events);

            // Then
            assertNotNull(results, "Results should not be null");

            if (!results.isEmpty()) {
                CycleResult cycle = results.get(0);
                assertEquals(7L, cycle.getPhase(), "Phase should be 7");

                // Verify short cycle is processed correctly
                if (cycle.getGreenStart() != null && cycle.getRedEnd() != null) {
                    long cycleDurationSeconds = java.time.Duration.between(
                            cycle.getGreenStart(), cycle.getRedEnd()).getSeconds();
                    assertTrue(cycleDurationSeconds <= 10,
                            "Short cycle should be 10 seconds or less, was: " + cycleDurationSeconds);
                }
            }
        }

        @ParameterizedTest
        @MethodSource("csvScenarioProvider")
        @DisplayName("Should process all CSV test scenarios correctly")
        void shouldProcessAllCsvScenariosCorrectly(String scenario) throws IOException {
            // Given
            List<PerfLogEventVO> events = loadTestDataFromCsv(scenario);

            // When
            List<CycleResult> results = cyclesFactory.calculateCycle(events);

            // Then
            assertNotNull(results, "Results should not be null for scenario: " + scenario);

            // Basic validation that applies to all scenarios
            for (CycleResult cycle : results) {
                assertNotNull(cycle, "Cycle should not be null");
                assertTrue(cycle.getPhase() > 0, "Phase should be positive for scenario: " + scenario);

                // If cycle has green start, it should be valid
                if (cycle.getGreenStart() != null) {
                    assertNotNull(cycle.getGreenStart(), "Green start should not be null if present");
                }

                // Verify timing sequence if all phases are present
                if (cycle.getGreenStart() != null && cycle.getGreenEnd() != null) {
                    assertTrue(cycle.getGreenStart().isBefore(cycle.getGreenEnd()) ||
                            cycle.getGreenStart().equals(cycle.getGreenEnd()),
                            "Green start should be before or equal to green end for scenario: " + scenario);
                }

                if (cycle.getYellowStart() != null && cycle.getYellowEnd() != null) {
                    assertTrue(cycle.getYellowStart().isBefore(cycle.getYellowEnd()) ||
                            cycle.getYellowStart().equals(cycle.getYellowEnd()),
                            "Yellow start should be before or equal to yellow end for scenario: " + scenario);
                }

                if (cycle.getRedStart() != null && cycle.getRedEnd() != null) {
                    assertTrue(cycle.getRedStart().isBefore(cycle.getRedEnd()) ||
                            cycle.getRedStart().equals(cycle.getRedEnd()),
                            "Red start should be before or equal to red end for scenario: " + scenario);
                }
            }
        }

        @Test
        @DisplayName("Should return empty list when input is empty")
        void shouldReturnEmptyListForEmptyInput() {
            // Given
            List<PerfLogEventVO> emptyEvents = Collections.emptyList();

            // When
            List<CycleResult> results = cyclesFactory.calculateCycle(emptyEvents);

            // Then
            assertNotNull(results, "Results should not be null");
            assertTrue(results.isEmpty(), "Results should be empty for empty input");
        }

        @Test
        @DisplayName("Should handle null input gracefully")
        void shouldHandleNullInput() {
            // When & Then
            assertThrows(NullPointerException.class, () -> {
                cyclesFactory.calculateCycle(null);
            }, "Should throw NullPointerException for null input");
        }

        @Test
        @DisplayName("Should handle events with no green start events")
        void shouldHandleEventsWithoutGreenStart() {
            // Given
            List<PerfLogEventVO> events = Arrays.asList(
                    createEvent(PHASE_GREEN_TERMINATION, 1L, baseTime),
                    createEvent(PHASE_BEGIN_YELLOW_CLEARANCE, 1L, baseTime.plusSeconds(5))
            );

            // When
            List<CycleResult> results = cyclesFactory.calculateCycle(events);

            // Then
            assertNotNull(results, "Results should not be null");
            assertTrue(results.isEmpty(), "Results should be empty when no green start events");
        }
    }

    @Nested
    @DisplayName("identifyCycleBoundaries() Tests")
    class IdentifyCycleBoundariesTests {

        @Test
        @DisplayName("Should identify single cycle boundary correctly")
        void shouldIdentifySingleCycleBoundary() {
            // Given
            List<PerfLogEventVO> events = Arrays.asList(
                    createEvent(PHASE_BEGIN_GREEN, 1L, baseTime)
            );

            // When
            List<CycleBoundary> boundaries = cyclesFactory.identifyCycleBoundaries(events);

            // Then
            assertNotNull(boundaries, "Boundaries should not be null");
            assertEquals(1, boundaries.size(), "Should have exactly one boundary");
            
            CycleBoundary boundary = boundaries.get(0);
            assertEquals(1L, boundary.getParam(), "Parameter should match");
            assertEquals(baseTime, boundary.getCycleStart(), "Cycle start should match");
            assertNull(boundary.getNextCycleStart(), "Next cycle start should be null for single cycle");
        }

        @Test
        @DisplayName("Should identify multiple cycle boundaries for same phase")
        void shouldIdentifyMultipleCycleBoundariesSamePhase() {
            // Given
            LocalDateTime secondCycleStart = baseTime.plusSeconds(60);
            List<PerfLogEventVO> events = Arrays.asList(
                    createEvent(PHASE_BEGIN_GREEN, 1L, baseTime),
                    createEvent(PHASE_BEGIN_GREEN, 1L, secondCycleStart)
            );

            // When
            List<CycleBoundary> boundaries = cyclesFactory.identifyCycleBoundaries(events);

            // Then
            assertNotNull(boundaries, "Boundaries should not be null");
            assertEquals(2, boundaries.size(), "Should have exactly two boundaries");
            
            CycleBoundary firstBoundary = boundaries.get(0);
            assertEquals(baseTime, firstBoundary.getCycleStart(), "First cycle start should match");
            assertEquals(secondCycleStart, firstBoundary.getNextCycleStart(), "Next cycle start should match");
            
            CycleBoundary secondBoundary = boundaries.get(1);
            assertEquals(secondCycleStart, secondBoundary.getCycleStart(), "Second cycle start should match");
            assertNull(secondBoundary.getNextCycleStart(), "Last cycle should have null next start");
        }

        @Test
        @DisplayName("Should handle multiple phases correctly")
        void shouldHandleMultiplePhases() {
            // Given
            List<PerfLogEventVO> events = Arrays.asList(
                    createEvent(PHASE_BEGIN_GREEN, 1L, baseTime),
                    createEvent(PHASE_BEGIN_GREEN, 2L, baseTime.plusSeconds(30)),
                    createEvent(PHASE_BEGIN_GREEN, 1L, baseTime.plusSeconds(60))
            );

            // When
            List<CycleBoundary> boundaries = cyclesFactory.identifyCycleBoundaries(events);

            // Then
            assertNotNull(boundaries, "Boundaries should not be null");
            assertEquals(3, boundaries.size(), "Should have three boundaries");
            
            // Verify phase 1 boundaries
            long phase1Count = boundaries.stream()
                    .filter(b -> b.getParam() == 1L)
                    .count();
            assertEquals(2, phase1Count, "Phase 1 should have 2 boundaries");
            
            // Verify phase 2 boundaries
            long phase2Count = boundaries.stream()
                    .filter(b -> b.getParam() == 2L)
                    .count();
            assertEquals(1, phase2Count, "Phase 2 should have 1 boundary");
        }

        @Test
        @DisplayName("Should handle events out of chronological order")
        void shouldHandleEventsOutOfOrder() {
            // Given - events in reverse chronological order
            List<PerfLogEventVO> events = Arrays.asList(
                    createEvent(PHASE_BEGIN_GREEN, 1L, baseTime.plusSeconds(60)),
                    createEvent(PHASE_BEGIN_GREEN, 1L, baseTime)
            );

            // When
            List<CycleBoundary> boundaries = cyclesFactory.identifyCycleBoundaries(events);

            // Then
            assertNotNull(boundaries, "Boundaries should not be null");
            assertEquals(2, boundaries.size(), "Should have two boundaries");
            
            // Verify proper ordering after processing
            CycleBoundary firstBoundary = boundaries.stream()
                    .filter(b -> b.getCycleStart().equals(baseTime))
                    .findFirst()
                    .orElse(null);
            assertNotNull(firstBoundary, "First boundary should exist");
            assertEquals(baseTime.plusSeconds(60), firstBoundary.getNextCycleStart(), 
                    "Next cycle start should be properly ordered");
        }

        @Test
        @DisplayName("Should return empty list for empty input")
        void shouldReturnEmptyListForEmptyInput() {
            // Given
            List<PerfLogEventVO> emptyEvents = Collections.emptyList();

            // When
            List<CycleBoundary> boundaries = cyclesFactory.identifyCycleBoundaries(emptyEvents);

            // Then
            assertNotNull(boundaries, "Boundaries should not be null");
            assertTrue(boundaries.isEmpty(), "Boundaries should be empty for empty input");
        }

        @Test
        @DisplayName("Should handle null input gracefully")
        void shouldHandleNullInput() {
            // When & Then
            assertThrows(NullPointerException.class, () -> {
                cyclesFactory.identifyCycleBoundaries(null);
            }, "Should throw NullPointerException for null input");
        }

        @Test
        @DisplayName("Should filter only green start events")
        void shouldFilterOnlyGreenStartEvents() {
            // Given
            List<PerfLogEventVO> events = Arrays.asList(
                    createEvent(PHASE_BEGIN_GREEN, 1L, baseTime),
                    createEvent(PHASE_GREEN_TERMINATION, 1L, baseTime.plusSeconds(30)),
                    createEvent(PHASE_BEGIN_YELLOW_CLEARANCE, 1L, baseTime.plusSeconds(30)),
                    createEvent(DETECTOR_ON, 1L, baseTime.plusSeconds(15))
            );

            // When
            List<CycleBoundary> boundaries = cyclesFactory.identifyCycleBoundaries(events);

            // Then
            assertNotNull(boundaries, "Boundaries should not be null");
            assertEquals(1, boundaries.size(), "Should only process green start events");
        }
    }

    @Nested
    @DisplayName("associateEventsWithCycles() Tests")
    class AssociateEventsWithCyclesTests {

        @Test
        @DisplayName("Should associate events within cycle boundaries correctly")
        void shouldAssociateEventsWithinBoundaries() {
            // Given
            List<PerfLogEventVO> events = Arrays.asList(
                    createEvent(PHASE_BEGIN_GREEN, 1L, baseTime),
                    createEvent(PHASE_GREEN_TERMINATION, 1L, baseTime.plusSeconds(30)),
                    createEvent(COORD_PATTERN_CHANGE, 1L, baseTime.plusSeconds(35))
            );

            List<CycleBoundary> boundaries = Arrays.asList(
                    new CycleBoundary(1L, baseTime, baseTime.plusSeconds(60))
            );

            // When
            List<CycleEvent> cycleEvents = cyclesFactory.associateEventsWithCycles(events, boundaries);

            // Then
            assertNotNull(cycleEvents, "Cycle events should not be null");
            assertEquals(3, cycleEvents.size(), "Should associate all relevant events");

            // Verify all events are properly associated
            cycleEvents.forEach(event -> {
                assertEquals(1L, event.param(), "Parameter should match");
                assertEquals(baseTime, event.cycleStart(), "Cycle start should match");
                assertEquals(baseTime.plusSeconds(60), event.nextCycleStart(), "Next cycle start should match");
            });
        }

        @Test
        @DisplayName("Should filter only relevant events")
        void shouldFilterOnlyRelevantEvents() {
            // Given
            List<PerfLogEventVO> events = Arrays.asList(
                    createEvent(PHASE_BEGIN_GREEN, 1L, baseTime),
                    createEvent(DETECTOR_ON, 1L, baseTime.plusSeconds(10)), // Not in filter
                    createEvent(PHASE_GREEN_TERMINATION, 1L, baseTime.plusSeconds(30)),
                    createEvent(PEDESTRIAN_BEGIN_WALK, 1L, baseTime.plusSeconds(35)) // Not in filter
            );

            List<CycleBoundary> boundaries = Arrays.asList(
                    new CycleBoundary(1L, baseTime, baseTime.plusSeconds(60))
            );

            // When
            List<CycleEvent> cycleEvents = cyclesFactory.associateEventsWithCycles(events, boundaries);

            // Then
            assertNotNull(cycleEvents, "Cycle events should not be null");
            assertEquals(2, cycleEvents.size(), "Should only associate filtered events");

            // Verify only relevant events are included
            assertTrue(cycleEvents.stream().anyMatch(e -> e.eventNum() == PHASE_BEGIN_GREEN.getEventNum()),
                    "Should include green start event");
            assertTrue(cycleEvents.stream().anyMatch(e -> e.eventNum() == PHASE_GREEN_TERMINATION.getEventNum()),
                    "Should include green termination event");
        }

        @Test
        @DisplayName("Should exclude events outside cycle boundaries")
        void shouldExcludeEventsOutsideBoundaries() {
            // Given
            List<PerfLogEventVO> events = Arrays.asList(
                    createEvent(PHASE_BEGIN_GREEN, 1L, baseTime.minusSeconds(10)), // Before cycle
                    createEvent(PHASE_GREEN_TERMINATION, 1L, baseTime.plusSeconds(30)), // Within cycle
                    createEvent(COORD_PATTERN_CHANGE, 1L, baseTime.plusSeconds(70)) // After cycle
            );

            List<CycleBoundary> boundaries = Arrays.asList(
                    new CycleBoundary(1L, baseTime, baseTime.plusSeconds(60))
            );

            // When
            List<CycleEvent> cycleEvents = cyclesFactory.associateEventsWithCycles(events, boundaries);

            // Then
            assertNotNull(cycleEvents, "Cycle events should not be null");
            assertEquals(1, cycleEvents.size(), "Should only include events within boundaries");
            assertEquals(PHASE_GREEN_TERMINATION.getEventNum(), cycleEvents.get(0).eventNum(),
                    "Should only include the event within cycle boundaries");
        }

        @Test
        @DisplayName("Should handle events with different parameters")
        void shouldHandleEventsWithDifferentParameters() {
            // Given
            List<PerfLogEventVO> events = Arrays.asList(
                    createEvent(PHASE_BEGIN_GREEN, 1L, baseTime),
                    createEvent(PHASE_GREEN_TERMINATION, 2L, baseTime.plusSeconds(30)) // Different parameter
            );

            List<CycleBoundary> boundaries = Arrays.asList(
                    new CycleBoundary(1L, baseTime, baseTime.plusSeconds(60))
            );

            // When
            List<CycleEvent> cycleEvents = cyclesFactory.associateEventsWithCycles(events, boundaries);

            // Then
            assertNotNull(cycleEvents, "Cycle events should not be null");
            assertEquals(1, cycleEvents.size(), "Should only include events with matching parameter");
            assertEquals(1L, cycleEvents.get(0).param(), "Should only include phase 1 event");
        }

        @Test
        @DisplayName("Should handle empty events list")
        void shouldHandleEmptyEventsList() {
            // Given
            List<PerfLogEventVO> emptyEvents = Collections.emptyList();
            List<CycleBoundary> boundaries = Arrays.asList(
                    new CycleBoundary(1L, baseTime, baseTime.plusSeconds(60))
            );

            // When
            List<CycleEvent> cycleEvents = cyclesFactory.associateEventsWithCycles(emptyEvents, boundaries);

            // Then
            assertNotNull(cycleEvents, "Cycle events should not be null");
            assertTrue(cycleEvents.isEmpty(), "Should return empty list for empty events");
        }

        @Test
        @DisplayName("Should handle empty boundaries list")
        void shouldHandleEmptyBoundariesList() {
            // Given
            List<PerfLogEventVO> events = Arrays.asList(
                    createEvent(PHASE_BEGIN_GREEN, 1L, baseTime)
            );
            List<CycleBoundary> emptyBoundaries = Collections.emptyList();

            // When
            List<CycleEvent> cycleEvents = cyclesFactory.associateEventsWithCycles(events, emptyBoundaries);

            // Then
            assertNotNull(cycleEvents, "Cycle events should not be null");
            assertTrue(cycleEvents.isEmpty(), "Should return empty list for empty boundaries");
        }

        @Test
        @DisplayName("Should handle null inputs gracefully")
        void shouldHandleNullInputs() {
            // Given
            List<CycleBoundary> boundaries = Arrays.asList(
                    new CycleBoundary(1L, baseTime, baseTime.plusSeconds(60))
            );

            // When & Then
            assertThrows(NullPointerException.class, () -> {
                cyclesFactory.associateEventsWithCycles(null, boundaries);
            }, "Should throw NullPointerException for null events");

            assertThrows(NullPointerException.class, () -> {
                cyclesFactory.associateEventsWithCycles(Collections.emptyList(), null);
            }, "Should throw NullPointerException for null boundaries");
        }
    }

    @Nested
    @DisplayName("calculateCycleLengths() Tests")
    class CalculateCycleLengthsTests {

        @Test
        @DisplayName("Should calculate cycle lengths for complete cycle")
        void shouldCalculateCycleLengthsForCompleteCycle() {
            // Given
            LocalDateTime greenStart = baseTime;
            LocalDateTime greenEnd = baseTime.plusSeconds(30);
            LocalDateTime yellowStart = baseTime.plusSeconds(30);
            LocalDateTime yellowEnd = baseTime.plusSeconds(33);
            LocalDateTime redStart = baseTime.plusSeconds(33);
            LocalDateTime nextCycleStart = baseTime.plusSeconds(60);

            List<CycleEvent> cycleEvents = Arrays.asList(
                    new CycleEvent(1L, PHASE_BEGIN_GREEN.getEventNum(), greenStart, greenStart, nextCycleStart),
                    new CycleEvent(1L, PHASE_GREEN_TERMINATION.getEventNum(), greenEnd, greenStart, nextCycleStart),
                    new CycleEvent(1L, PHASE_BEGIN_YELLOW_CLEARANCE.getEventNum(), yellowStart, greenStart, nextCycleStart),
                    new CycleEvent(1L, PHASE_END_YELLOW_CLEARANCE.getEventNum(), yellowEnd, greenStart, nextCycleStart),
                    new CycleEvent(1L, PHASE_BEGIN_RED_CLEARANCE.getEventNum(), redStart, greenStart, nextCycleStart)
            );

            // When
            List<CycleResult> results = cyclesFactory.calculateCycleLengths(cycleEvents);

            // Then
            assertNotNull(results, "Results should not be null");
            assertEquals(1, results.size(), "Should have one cycle result");

            CycleResult result = results.get(0);
            assertEquals(1L, result.getPhase(), "Phase should match");
            assertEquals(greenStart, result.getGreenStart(), "Green start should match");
            assertEquals(greenEnd, result.getGreenEnd(), "Green end should match");
            assertEquals(yellowStart, result.getYellowStart(), "Yellow start should match");
            assertEquals(yellowEnd, result.getYellowEnd(), "Yellow end should match");
            assertEquals(redStart, result.getRedStart(), "Red start should match");
            assertEquals(nextCycleStart, result.getRedEnd(), "Red end should match next cycle start");
        }

        @Test
        @DisplayName("Should handle incomplete cycles missing some phases")
        void shouldHandleIncompleteCycles() {
            // Given - cycle with only green start and termination
            LocalDateTime greenStart = baseTime;
            LocalDateTime greenEnd = baseTime.plusSeconds(30);
            LocalDateTime nextCycleStart = baseTime.plusSeconds(60);

            List<CycleEvent> cycleEvents = Arrays.asList(
                    new CycleEvent(1L, PHASE_BEGIN_GREEN.getEventNum(), greenStart, greenStart, nextCycleStart),
                    new CycleEvent(1L, PHASE_GREEN_TERMINATION.getEventNum(), greenEnd, greenStart, nextCycleStart)
            );

            // When
            List<CycleResult> results = cyclesFactory.calculateCycleLengths(cycleEvents);

            // Then
            assertNotNull(results, "Results should not be null");
            assertEquals(1, results.size(), "Should have one cycle result");

            CycleResult result = results.get(0);
            assertEquals(greenStart, result.getGreenStart(), "Green start should match");
            assertEquals(greenEnd, result.getGreenEnd(), "Green end should match");
            assertNull(result.getYellowStart(), "Yellow start should be null");
            assertNull(result.getYellowEnd(), "Yellow end should be null");
            assertNull(result.getRedStart(), "Red start should be null");
        }

        @Test
        @DisplayName("Should exclude cycles without red end")
        void shouldExcludeCyclesWithoutRedEnd() {
            // Given - cycle without red phase
            LocalDateTime greenStart = baseTime;
            LocalDateTime nextCycleStart = baseTime.plusSeconds(60);

            List<CycleEvent> cycleEvents = Arrays.asList(
                    new CycleEvent(1L, PHASE_BEGIN_GREEN.getEventNum(), greenStart, greenStart, nextCycleStart)
            );

            // When
            List<CycleResult> results = cyclesFactory.calculateCycleLengths(cycleEvents);

            // Then
            assertNotNull(results, "Results should not be null");
            assertTrue(results.isEmpty(), "Should exclude cycles without red end");
        }

        @Test
        @DisplayName("Should handle multiple cycles for same phase")
        void shouldHandleMultipleCyclesSamePhase() {
            // Given
            LocalDateTime firstCycleStart = baseTime;
            LocalDateTime secondCycleStart = baseTime.plusSeconds(60);
            LocalDateTime thirdCycleStart = baseTime.plusSeconds(120);

            List<CycleEvent> cycleEvents = Arrays.asList(
                    // First cycle
                    new CycleEvent(1L, PHASE_BEGIN_GREEN.getEventNum(), firstCycleStart, firstCycleStart, secondCycleStart),
                    new CycleEvent(1L, PHASE_BEGIN_RED_CLEARANCE.getEventNum(), firstCycleStart.plusSeconds(30), firstCycleStart, secondCycleStart),
                    // Second cycle
                    new CycleEvent(1L, PHASE_BEGIN_GREEN.getEventNum(), secondCycleStart, secondCycleStart, thirdCycleStart),
                    new CycleEvent(1L, PHASE_BEGIN_RED_CLEARANCE.getEventNum(), secondCycleStart.plusSeconds(30), secondCycleStart, thirdCycleStart)
            );

            // When
            List<CycleResult> results = cyclesFactory.calculateCycleLengths(cycleEvents);

            // Then
            assertNotNull(results, "Results should not be null");
            assertEquals(2, results.size(), "Should have two cycle results");

            // Verify cycles are sorted by green start time
            assertTrue(results.get(0).getGreenStart().isBefore(results.get(1).getGreenStart()),
                    "Results should be sorted by green start time");
        }

        @Test
        @DisplayName("Should handle multiple phases with proper sorting")
        void shouldHandleMultiplePhasesWithSorting() {
            // Given
            LocalDateTime cycleStart = baseTime;
            LocalDateTime nextCycleStart = baseTime.plusSeconds(60);

            List<CycleEvent> cycleEvents = Arrays.asList(
                    // Phase 2 (later in time)
                    new CycleEvent(2L, PHASE_BEGIN_GREEN.getEventNum(), cycleStart.plusSeconds(30), cycleStart.plusSeconds(30), nextCycleStart.plusSeconds(30)),
                    new CycleEvent(2L, PHASE_BEGIN_RED_CLEARANCE.getEventNum(), cycleStart.plusSeconds(50), cycleStart.plusSeconds(30), nextCycleStart.plusSeconds(30)),
                    // Phase 1 (earlier in time)
                    new CycleEvent(1L, PHASE_BEGIN_GREEN.getEventNum(), cycleStart, cycleStart, nextCycleStart),
                    new CycleEvent(1L, PHASE_BEGIN_RED_CLEARANCE.getEventNum(), cycleStart.plusSeconds(25), cycleStart, nextCycleStart)
            );

            // When
            List<CycleResult> results = cyclesFactory.calculateCycleLengths(cycleEvents);

            // Then
            assertNotNull(results, "Results should not be null");
            assertEquals(2, results.size(), "Should have two cycle results");

            // Verify sorting: first by green start time, then by phase
            CycleResult firstResult = results.get(0);
            CycleResult secondResult = results.get(1);

            assertTrue(firstResult.getGreenStart().isBefore(secondResult.getGreenStart()) ||
                    (firstResult.getGreenStart().equals(secondResult.getGreenStart()) &&
                     Long.compare(firstResult.getPhase(), secondResult.getPhase()) <= 0),
                    "Results should be sorted by green start time, then by phase");
        }

        @Test
        @DisplayName("Should find maximum datetime for duplicate events")
        void shouldFindMaxDatetimeForDuplicateEvents() {
            // Given - multiple events of same type with different times
            LocalDateTime greenStart = baseTime;
            LocalDateTime greenStart2 = baseTime.plusSeconds(5); // Later green start
            LocalDateTime nextCycleStart = baseTime.plusSeconds(60);

            List<CycleEvent> cycleEvents = Arrays.asList(
                    new CycleEvent(1L, PHASE_BEGIN_GREEN.getEventNum(), greenStart, greenStart, nextCycleStart),
                    new CycleEvent(1L, PHASE_BEGIN_GREEN.getEventNum(), greenStart2, greenStart, nextCycleStart), // Duplicate with later time
                    new CycleEvent(1L, PHASE_BEGIN_RED_CLEARANCE.getEventNum(), greenStart.plusSeconds(30), greenStart, nextCycleStart)
            );

            // When
            List<CycleResult> results = cyclesFactory.calculateCycleLengths(cycleEvents);

            // Then
            assertNotNull(results, "Results should not be null");
            assertEquals(1, results.size(), "Should have one cycle result");

            CycleResult result = results.get(0);
            assertEquals(greenStart2, result.getGreenStart(), "Should use maximum datetime for green start");
        }

        @Test
        @DisplayName("Should handle empty cycle events list")
        void shouldHandleEmptyCycleEventsList() {
            // Given
            List<CycleEvent> emptyCycleEvents = Collections.emptyList();

            // When
            List<CycleResult> results = cyclesFactory.calculateCycleLengths(emptyCycleEvents);

            // Then
            assertNotNull(results, "Results should not be null");
            assertTrue(results.isEmpty(), "Should return empty list for empty input");
        }

        @Test
        @DisplayName("Should handle null input gracefully")
        void shouldHandleNullInput() {
            // When & Then
            assertThrows(NullPointerException.class, () -> {
                cyclesFactory.calculateCycleLengths(null);
            }, "Should throw NullPointerException for null input");
        }
    }

    @Nested
    @DisplayName("Edge Cases and Integration Tests")
    class EdgeCasesAndIntegrationTests {

        @ParameterizedTest
        @MethodSource("provideInvalidEventParameters")
        @DisplayName("Should handle various invalid event parameters")
        void shouldHandleInvalidEventParameters(List<PerfLogEventVO> events, String description) {
            // When
            List<CycleResult> results = cyclesFactory.calculateCycle(events);

            // Then
            assertNotNull(results, "Results should not be null for: " + description);
            // Results may be empty or contain partial data, but should not throw exceptions
        }

        /**
         * Provides test data for parameterized tests with invalid event parameters.
         *
         * @return stream of test arguments
         */
        static Stream<Arguments> provideInvalidEventParameters() {
            LocalDateTime baseTime = LocalDateTime.of(2025, 1, 1, 10, 0, 0);

            return Stream.of(
                    Arguments.of(
                            Arrays.asList(createEventStatic(PHASE_BEGIN_GREEN, -1L, baseTime)),
                            "Negative parameter"
                    ),
                    Arguments.of(
                            Arrays.asList(createEventStatic(PHASE_BEGIN_GREEN, Long.MAX_VALUE, baseTime)),
                            "Maximum long parameter"
                    ),
                    Arguments.of(
                            Arrays.asList(createEventStatic(PHASE_BEGIN_GREEN, 0L, baseTime)),
                            "Zero parameter"
                    )
            );
        }

        /**
         * Static helper method for creating events in parameterized tests.
         */
        private static PerfLogEventVO createEventStatic(PerfLogEventVO.Event event, long parameter, LocalDateTime dateTime) {
            return PerfLogEventVO.builder()
                    .event(event)
                    .parameter(parameter)
                    .dateTime(dateTime)
                    .build();
        }

        @Test
        @DisplayName("Should handle complex real-world scenario")
        void shouldHandleComplexRealWorldScenario() {
            // Given - Complex scenario with multiple phases, overlapping times, and missing events
            List<PerfLogEventVO> events = new ArrayList<>();

            // Phase 1 - Complete cycle
            events.addAll(createCompleteCycle(1L, baseTime));

            // Phase 2 - Incomplete cycle (missing yellow phase)
            events.add(createEvent(PHASE_BEGIN_GREEN, 2L, baseTime.plusSeconds(30)));
            events.add(createEvent(PHASE_GREEN_TERMINATION, 2L, baseTime.plusSeconds(50)));
            events.add(createEvent(PHASE_BEGIN_RED_CLEARANCE, 2L, baseTime.plusSeconds(50)));

            // Phase 1 - Second cycle
            events.addAll(createCompleteCycle(1L, baseTime.plusSeconds(60)));

            // Add some noise events that should be filtered out
            events.add(createEvent(DETECTOR_ON, 1L, baseTime.plusSeconds(15)));
            events.add(createEvent(PEDESTRIAN_BEGIN_WALK, 2L, baseTime.plusSeconds(35)));

            // When
            List<CycleResult> results = cyclesFactory.calculateCycle(events);

            // Then
            assertNotNull(results, "Results should not be null");
            assertFalse(results.isEmpty(), "Should have results for complex scenario");

            // Verify we have results for both phases
            long phase1Count = results.stream().filter(r -> r.getPhase() == 1L).count();
            long phase2Count = results.stream().filter(r -> r.getPhase() == 2L).count();

            assertTrue(phase1Count > 0, "Should have results for phase 1");
            assertTrue(phase2Count >= 0, "Should handle phase 2 (may be 0 if incomplete)");

            // Verify sorting
            for (int i = 1; i < results.size(); i++) {
                CycleResult prev = results.get(i - 1);
                CycleResult curr = results.get(i);

                assertTrue(
                        prev.getGreenStart().isBefore(curr.getGreenStart()) ||
                        (prev.getGreenStart().equals(curr.getGreenStart()) &&
                         Long.compare(prev.getPhase(), curr.getPhase()) <= 0),
                        "Results should be properly sorted"
                );
            }
        }

        @Test
        @DisplayName("Should handle boundary time conditions")
        void shouldHandleBoundaryTimeConditions() {
            // Given - Events exactly at boundary times
            LocalDateTime cycleStart = baseTime;
            LocalDateTime nextCycleStart = baseTime.plusSeconds(60);

            List<PerfLogEventVO> events = Arrays.asList(
                    createEvent(PHASE_BEGIN_GREEN, 1L, cycleStart), // Exactly at start
                    createEvent(PHASE_GREEN_TERMINATION, 1L, nextCycleStart.minusNanos(1)), // Just before next cycle
                    createEvent(PHASE_BEGIN_GREEN, 1L, nextCycleStart) // Exactly at next cycle start
            );

            // When
            List<CycleResult> results = cyclesFactory.calculateCycle(events);

            // Then
            assertNotNull(results, "Results should not be null");
            // Should handle boundary conditions without errors
        }
    }
}
