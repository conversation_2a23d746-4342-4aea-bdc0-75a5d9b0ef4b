package com.siemens.spm.analysis.vo.splitmonitor;

import com.siemens.spm.perflog.vo.PerfLogEventVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Stream;

import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.*;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive unit tests for the CyclesFactory class.
 * 
 * This test suite covers all public methods with various input scenarios including:
 * - Valid inputs with expected outputs
 * - Boundary conditions (empty collections, null values, minimum/maximum values)
 * - Invalid inputs that should trigger exceptions
 * - Edge cases specific to the business logic
 * 
 * <AUTHOR> Suite Generator
 * @version 1.0
 * @since 2025-07-02
 */
@DisplayName("CyclesFactory Tests")
class CyclesFactoryTest {

    private CyclesFactory cyclesFactory;
    private LocalDateTime baseTime;

    @BeforeEach
    void setUp() {
        cyclesFactory = new CyclesFactory();
        baseTime = LocalDateTime.of(2025, 1, 1, 10, 0, 0);
    }

    /**
     * Creates a PerfLogEventVO with the specified parameters.
     * 
     * @param event the event type
     * @param parameter the phase/parameter number
     * @param dateTime the event timestamp
     * @return a new PerfLogEventVO instance
     */
    private PerfLogEventVO createEvent(PerfLogEventVO.Event event, long parameter, LocalDateTime dateTime) {
        return PerfLogEventVO.builder()
                .event(event)
                .parameter(parameter)
                .dateTime(dateTime)
                .build();
    }

    /**
     * Creates a complete cycle sequence for a single phase.
     * 
     * @param phase the phase number
     * @param startTime the cycle start time
     * @return list of events representing a complete cycle
     */
    private List<PerfLogEventVO> createCompleteCycle(long phase, LocalDateTime startTime) {
        return Arrays.asList(
                createEvent(PHASE_BEGIN_GREEN, phase, startTime),
                createEvent(PHASE_GREEN_TERMINATION, phase, startTime.plusSeconds(30)),
                createEvent(PHASE_BEGIN_YELLOW_CLEARANCE, phase, startTime.plusSeconds(30)),
                createEvent(PHASE_END_YELLOW_CLEARANCE, phase, startTime.plusSeconds(33)),
                createEvent(PHASE_BEGIN_RED_CLEARANCE, phase, startTime.plusSeconds(33)),
                createEvent(COORD_PATTERN_CHANGE, phase, startTime.plusSeconds(35))
        );
    }

    @Nested
    @DisplayName("calculateCycle() Tests")
    class CalculateCycleTests {

        @Test
        @DisplayName("Should calculate cycle successfully with valid complete data")
        void shouldCalculateCycleWithValidData() {
            // Given
            List<PerfLogEventVO> events = createCompleteCycle(1L, baseTime);
            events.addAll(createCompleteCycle(1L, baseTime.plusSeconds(60)));

            // When
            List<CycleResult> results = cyclesFactory.calculateCycle(events);

            // Then
            assertNotNull(results, "Results should not be null");
            assertFalse(results.isEmpty(), "Results should not be empty");
            
            CycleResult firstCycle = results.get(0);
            assertEquals("1", firstCycle.getPhase(), "Phase should match");
            assertNotNull(firstCycle.getGreenStart(), "Green start should not be null");
            assertNotNull(firstCycle.getRedEnd(), "Red end should not be null");
        }

        @Test
        @DisplayName("Should return empty list when input is empty")
        void shouldReturnEmptyListForEmptyInput() {
            // Given
            List<PerfLogEventVO> emptyEvents = Collections.emptyList();

            // When
            List<CycleResult> results = cyclesFactory.calculateCycle(emptyEvents);

            // Then
            assertNotNull(results, "Results should not be null");
            assertTrue(results.isEmpty(), "Results should be empty for empty input");
        }

        @Test
        @DisplayName("Should handle null input gracefully")
        void shouldHandleNullInput() {
            // When & Then
            assertThrows(NullPointerException.class, () -> {
                cyclesFactory.calculateCycle(null);
            }, "Should throw NullPointerException for null input");
        }

        @Test
        @DisplayName("Should handle events with no green start events")
        void shouldHandleEventsWithoutGreenStart() {
            // Given
            List<PerfLogEventVO> events = Arrays.asList(
                    createEvent(PHASE_GREEN_TERMINATION, 1L, baseTime),
                    createEvent(PHASE_BEGIN_YELLOW_CLEARANCE, 1L, baseTime.plusSeconds(5))
            );

            // When
            List<CycleResult> results = cyclesFactory.calculateCycle(events);

            // Then
            assertNotNull(results, "Results should not be null");
            assertTrue(results.isEmpty(), "Results should be empty when no green start events");
        }
    }

    @Nested
    @DisplayName("identifyCycleBoundaries() Tests")
    class IdentifyCycleBoundariesTests {

        @Test
        @DisplayName("Should identify single cycle boundary correctly")
        void shouldIdentifySingleCycleBoundary() {
            // Given
            List<PerfLogEventVO> events = Arrays.asList(
                    createEvent(PHASE_BEGIN_GREEN, 1L, baseTime)
            );

            // When
            List<CycleBoundary> boundaries = cyclesFactory.identifyCycleBoundaries(events);

            // Then
            assertNotNull(boundaries, "Boundaries should not be null");
            assertEquals(1, boundaries.size(), "Should have exactly one boundary");
            
            CycleBoundary boundary = boundaries.get(0);
            assertEquals(1L, boundary.getParam(), "Parameter should match");
            assertEquals(baseTime, boundary.getCycleStart(), "Cycle start should match");
            assertNull(boundary.getNextCycleStart(), "Next cycle start should be null for single cycle");
        }

        @Test
        @DisplayName("Should identify multiple cycle boundaries for same phase")
        void shouldIdentifyMultipleCycleBoundariesSamePhase() {
            // Given
            LocalDateTime secondCycleStart = baseTime.plusSeconds(60);
            List<PerfLogEventVO> events = Arrays.asList(
                    createEvent(PHASE_BEGIN_GREEN, 1L, baseTime),
                    createEvent(PHASE_BEGIN_GREEN, 1L, secondCycleStart)
            );

            // When
            List<CycleBoundary> boundaries = cyclesFactory.identifyCycleBoundaries(events);

            // Then
            assertNotNull(boundaries, "Boundaries should not be null");
            assertEquals(2, boundaries.size(), "Should have exactly two boundaries");
            
            CycleBoundary firstBoundary = boundaries.get(0);
            assertEquals(baseTime, firstBoundary.getCycleStart(), "First cycle start should match");
            assertEquals(secondCycleStart, firstBoundary.getNextCycleStart(), "Next cycle start should match");
            
            CycleBoundary secondBoundary = boundaries.get(1);
            assertEquals(secondCycleStart, secondBoundary.getCycleStart(), "Second cycle start should match");
            assertNull(secondBoundary.getNextCycleStart(), "Last cycle should have null next start");
        }

        @Test
        @DisplayName("Should handle multiple phases correctly")
        void shouldHandleMultiplePhases() {
            // Given
            List<PerfLogEventVO> events = Arrays.asList(
                    createEvent(PHASE_BEGIN_GREEN, 1L, baseTime),
                    createEvent(PHASE_BEGIN_GREEN, 2L, baseTime.plusSeconds(30)),
                    createEvent(PHASE_BEGIN_GREEN, 1L, baseTime.plusSeconds(60))
            );

            // When
            List<CycleBoundary> boundaries = cyclesFactory.identifyCycleBoundaries(events);

            // Then
            assertNotNull(boundaries, "Boundaries should not be null");
            assertEquals(3, boundaries.size(), "Should have three boundaries");
            
            // Verify phase 1 boundaries
            long phase1Count = boundaries.stream()
                    .filter(b -> b.getParam() == 1L)
                    .count();
            assertEquals(2, phase1Count, "Phase 1 should have 2 boundaries");
            
            // Verify phase 2 boundaries
            long phase2Count = boundaries.stream()
                    .filter(b -> b.getParam() == 2L)
                    .count();
            assertEquals(1, phase2Count, "Phase 2 should have 1 boundary");
        }

        @Test
        @DisplayName("Should handle events out of chronological order")
        void shouldHandleEventsOutOfOrder() {
            // Given - events in reverse chronological order
            List<PerfLogEventVO> events = Arrays.asList(
                    createEvent(PHASE_BEGIN_GREEN, 1L, baseTime.plusSeconds(60)),
                    createEvent(PHASE_BEGIN_GREEN, 1L, baseTime)
            );

            // When
            List<CycleBoundary> boundaries = cyclesFactory.identifyCycleBoundaries(events);

            // Then
            assertNotNull(boundaries, "Boundaries should not be null");
            assertEquals(2, boundaries.size(), "Should have two boundaries");
            
            // Verify proper ordering after processing
            CycleBoundary firstBoundary = boundaries.stream()
                    .filter(b -> b.getCycleStart().equals(baseTime))
                    .findFirst()
                    .orElse(null);
            assertNotNull(firstBoundary, "First boundary should exist");
            assertEquals(baseTime.plusSeconds(60), firstBoundary.getNextCycleStart(), 
                    "Next cycle start should be properly ordered");
        }

        @Test
        @DisplayName("Should return empty list for empty input")
        void shouldReturnEmptyListForEmptyInput() {
            // Given
            List<PerfLogEventVO> emptyEvents = Collections.emptyList();

            // When
            List<CycleBoundary> boundaries = cyclesFactory.identifyCycleBoundaries(emptyEvents);

            // Then
            assertNotNull(boundaries, "Boundaries should not be null");
            assertTrue(boundaries.isEmpty(), "Boundaries should be empty for empty input");
        }

        @Test
        @DisplayName("Should handle null input gracefully")
        void shouldHandleNullInput() {
            // When & Then
            assertThrows(NullPointerException.class, () -> {
                cyclesFactory.identifyCycleBoundaries(null);
            }, "Should throw NullPointerException for null input");
        }

        @Test
        @DisplayName("Should filter only green start events")
        void shouldFilterOnlyGreenStartEvents() {
            // Given
            List<PerfLogEventVO> events = Arrays.asList(
                    createEvent(PHASE_BEGIN_GREEN, 1L, baseTime),
                    createEvent(PHASE_GREEN_TERMINATION, 1L, baseTime.plusSeconds(30)),
                    createEvent(PHASE_BEGIN_YELLOW_CLEARANCE, 1L, baseTime.plusSeconds(30)),
                    createEvent(DETECTOR_ON, 1L, baseTime.plusSeconds(15))
            );

            // When
            List<CycleBoundary> boundaries = cyclesFactory.identifyCycleBoundaries(events);

            // Then
            assertNotNull(boundaries, "Boundaries should not be null");
            assertEquals(1, boundaries.size(), "Should only process green start events");
        }
    }

    @Nested
    @DisplayName("associateEventsWithCycles() Tests")
    class AssociateEventsWithCyclesTests {

        @Test
        @DisplayName("Should associate events within cycle boundaries correctly")
        void shouldAssociateEventsWithinBoundaries() {
            // Given
            List<PerfLogEventVO> events = Arrays.asList(
                    createEvent(PHASE_BEGIN_GREEN, 1L, baseTime),
                    createEvent(PHASE_GREEN_TERMINATION, 1L, baseTime.plusSeconds(30)),
                    createEvent(COORD_PATTERN_CHANGE, 1L, baseTime.plusSeconds(35))
            );

            List<CycleBoundary> boundaries = Arrays.asList(
                    new CycleBoundary(1L, baseTime, baseTime.plusSeconds(60))
            );

            // When
            List<CycleEvent> cycleEvents = cyclesFactory.associateEventsWithCycles(events, boundaries);

            // Then
            assertNotNull(cycleEvents, "Cycle events should not be null");
            assertEquals(3, cycleEvents.size(), "Should associate all relevant events");

            // Verify all events are properly associated
            cycleEvents.forEach(event -> {
                assertEquals(1L, event.param(), "Parameter should match");
                assertEquals(baseTime, event.cycleStart(), "Cycle start should match");
                assertEquals(baseTime.plusSeconds(60), event.nextCycleStart(), "Next cycle start should match");
            });
        }

        @Test
        @DisplayName("Should filter only relevant events")
        void shouldFilterOnlyRelevantEvents() {
            // Given
            List<PerfLogEventVO> events = Arrays.asList(
                    createEvent(PHASE_BEGIN_GREEN, 1L, baseTime),
                    createEvent(DETECTOR_ON, 1L, baseTime.plusSeconds(10)), // Not in filter
                    createEvent(PHASE_GREEN_TERMINATION, 1L, baseTime.plusSeconds(30)),
                    createEvent(PEDESTRIAN_BEGIN_WALK, 1L, baseTime.plusSeconds(35)) // Not in filter
            );

            List<CycleBoundary> boundaries = Arrays.asList(
                    new CycleBoundary(1L, baseTime, baseTime.plusSeconds(60))
            );

            // When
            List<CycleEvent> cycleEvents = cyclesFactory.associateEventsWithCycles(events, boundaries);

            // Then
            assertNotNull(cycleEvents, "Cycle events should not be null");
            assertEquals(2, cycleEvents.size(), "Should only associate filtered events");

            // Verify only relevant events are included
            assertTrue(cycleEvents.stream().anyMatch(e -> e.eventNum() == PHASE_BEGIN_GREEN.getEventNum()),
                    "Should include green start event");
            assertTrue(cycleEvents.stream().anyMatch(e -> e.eventNum() == PHASE_GREEN_TERMINATION.getEventNum()),
                    "Should include green termination event");
        }

        @Test
        @DisplayName("Should exclude events outside cycle boundaries")
        void shouldExcludeEventsOutsideBoundaries() {
            // Given
            List<PerfLogEventVO> events = Arrays.asList(
                    createEvent(PHASE_BEGIN_GREEN, 1L, baseTime.minusSeconds(10)), // Before cycle
                    createEvent(PHASE_GREEN_TERMINATION, 1L, baseTime.plusSeconds(30)), // Within cycle
                    createEvent(COORD_PATTERN_CHANGE, 1L, baseTime.plusSeconds(70)) // After cycle
            );

            List<CycleBoundary> boundaries = Arrays.asList(
                    new CycleBoundary(1L, baseTime, baseTime.plusSeconds(60))
            );

            // When
            List<CycleEvent> cycleEvents = cyclesFactory.associateEventsWithCycles(events, boundaries);

            // Then
            assertNotNull(cycleEvents, "Cycle events should not be null");
            assertEquals(1, cycleEvents.size(), "Should only include events within boundaries");
            assertEquals(PHASE_GREEN_TERMINATION.getEventNum(), cycleEvents.get(0).eventNum(),
                    "Should only include the event within cycle boundaries");
        }

        @Test
        @DisplayName("Should handle events with different parameters")
        void shouldHandleEventsWithDifferentParameters() {
            // Given
            List<PerfLogEventVO> events = Arrays.asList(
                    createEvent(PHASE_BEGIN_GREEN, 1L, baseTime),
                    createEvent(PHASE_GREEN_TERMINATION, 2L, baseTime.plusSeconds(30)) // Different parameter
            );

            List<CycleBoundary> boundaries = Arrays.asList(
                    new CycleBoundary(1L, baseTime, baseTime.plusSeconds(60))
            );

            // When
            List<CycleEvent> cycleEvents = cyclesFactory.associateEventsWithCycles(events, boundaries);

            // Then
            assertNotNull(cycleEvents, "Cycle events should not be null");
            assertEquals(1, cycleEvents.size(), "Should only include events with matching parameter");
            assertEquals(1L, cycleEvents.get(0).param(), "Should only include phase 1 event");
        }

        @Test
        @DisplayName("Should handle empty events list")
        void shouldHandleEmptyEventsList() {
            // Given
            List<PerfLogEventVO> emptyEvents = Collections.emptyList();
            List<CycleBoundary> boundaries = Arrays.asList(
                    new CycleBoundary(1L, baseTime, baseTime.plusSeconds(60))
            );

            // When
            List<CycleEvent> cycleEvents = cyclesFactory.associateEventsWithCycles(emptyEvents, boundaries);

            // Then
            assertNotNull(cycleEvents, "Cycle events should not be null");
            assertTrue(cycleEvents.isEmpty(), "Should return empty list for empty events");
        }

        @Test
        @DisplayName("Should handle empty boundaries list")
        void shouldHandleEmptyBoundariesList() {
            // Given
            List<PerfLogEventVO> events = Arrays.asList(
                    createEvent(PHASE_BEGIN_GREEN, 1L, baseTime)
            );
            List<CycleBoundary> emptyBoundaries = Collections.emptyList();

            // When
            List<CycleEvent> cycleEvents = cyclesFactory.associateEventsWithCycles(events, emptyBoundaries);

            // Then
            assertNotNull(cycleEvents, "Cycle events should not be null");
            assertTrue(cycleEvents.isEmpty(), "Should return empty list for empty boundaries");
        }

        @Test
        @DisplayName("Should handle null inputs gracefully")
        void shouldHandleNullInputs() {
            // Given
            List<CycleBoundary> boundaries = Arrays.asList(
                    new CycleBoundary(1L, baseTime, baseTime.plusSeconds(60))
            );

            // When & Then
            assertThrows(NullPointerException.class, () -> {
                cyclesFactory.associateEventsWithCycles(null, boundaries);
            }, "Should throw NullPointerException for null events");

            assertThrows(NullPointerException.class, () -> {
                cyclesFactory.associateEventsWithCycles(Collections.emptyList(), null);
            }, "Should throw NullPointerException for null boundaries");
        }
    }

    @Nested
    @DisplayName("calculateCycleLengths() Tests")
    class CalculateCycleLengthsTests {

        @Test
        @DisplayName("Should calculate cycle lengths for complete cycle")
        void shouldCalculateCycleLengthsForCompleteCycle() {
            // Given
            LocalDateTime greenStart = baseTime;
            LocalDateTime greenEnd = baseTime.plusSeconds(30);
            LocalDateTime yellowStart = baseTime.plusSeconds(30);
            LocalDateTime yellowEnd = baseTime.plusSeconds(33);
            LocalDateTime redStart = baseTime.plusSeconds(33);
            LocalDateTime nextCycleStart = baseTime.plusSeconds(60);

            List<CycleEvent> cycleEvents = Arrays.asList(
                    new CycleEvent(1L, PHASE_BEGIN_GREEN.getEventNum(), greenStart, greenStart, nextCycleStart),
                    new CycleEvent(1L, PHASE_GREEN_TERMINATION.getEventNum(), greenEnd, greenStart, nextCycleStart),
                    new CycleEvent(1L, PHASE_BEGIN_YELLOW_CLEARANCE.getEventNum(), yellowStart, greenStart, nextCycleStart),
                    new CycleEvent(1L, PHASE_END_YELLOW_CLEARANCE.getEventNum(), yellowEnd, greenStart, nextCycleStart),
                    new CycleEvent(1L, PHASE_BEGIN_RED_CLEARANCE.getEventNum(), redStart, greenStart, nextCycleStart)
            );

            // When
            List<CycleResult> results = cyclesFactory.calculateCycleLengths(cycleEvents);

            // Then
            assertNotNull(results, "Results should not be null");
            assertEquals(1, results.size(), "Should have one cycle result");

            CycleResult result = results.get(0);
            assertEquals("1", result.getPhase(), "Phase should match");
            assertEquals(greenStart, result.getGreenStart(), "Green start should match");
            assertEquals(greenEnd, result.getGreenEnd(), "Green end should match");
            assertEquals(yellowStart, result.getYellowStart(), "Yellow start should match");
            assertEquals(yellowEnd, result.getYellowEnd(), "Yellow end should match");
            assertEquals(redStart, result.getRedStart(), "Red start should match");
            assertEquals(nextCycleStart, result.getRedEnd(), "Red end should match next cycle start");
        }
    }
}
