package com.siemens.spm.analysis.factory;

import com.siemens.spm.analysis.vo.CoordinationHealthBinVO;
import com.siemens.spm.analysis.vo.CoordinationHealthChartVO;
import com.siemens.spm.analysis.vo.CoordinationHealthPlanStatisticsVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

class CoordHealthChartBuilderTest {
    private static final int[][] DET_CALL_PHASES = { { 1 } };
    private static final int PHASE_NUM = 1;
    private static final int CHA_INSTEP = 1; // Detector number is 1, assuming mapped to phase 1
    private static final int CHA_TRANSITION_2 = 2;
    private static final int CHA_TRANSITION_3 = 3;
    private static final int CHA_TRANSITION_4 = 4;
    private static final int YEAR = 2021;
    private static final int MONTH = 8;
    private static final int DAY = 10;
    private static final LocalDateTime T0 = LocalDateTime.of(YEAR, MONTH, DAY, 11, 0);
    private static final LocalDateTime T1 = LocalDateTime.of(YEAR, MONTH, DAY, 12, 0);
    private static final LocalDateTime T2 = LocalDateTime.of(YEAR, MONTH, DAY, 13, 0);
    private static final LocalDateTime T3 = LocalDateTime.of(YEAR, MONTH, DAY, 14, 0);

    private CoordinationHealthChartVO chartVO;
    private CoordHealthChartBuilder builder;

    @BeforeEach
    void setUp() {
        chartVO = new CoordinationHealthChartVO();
        chartVO.setFromTime(T0);
        chartVO.setToTime(T3);
        chartVO.setPhase(PHASE_NUM);

        builder = new CoordHealthChartBuilder(chartVO, CoordinationHealthBinVO.BIN_SIZE_900);

        // Unknown plan -------------------------------------
        // Instep state
        builder.putEvent(
                new PerfLogEventVO(T0.plusMinutes(2), PerfLogEventVO.Event.COORD_CYCLE_STATE_CHANGE, CHA_INSTEP));
        builder.putEvent(
                new PerfLogEventVO(T0.plusMinutes(3), PerfLogEventVO.Event.COORD_CYCLE_STATE_CHANGE, CHA_INSTEP));
        builder.putEvent(
                new PerfLogEventVO(T0.plusMinutes(4), PerfLogEventVO.Event.COORD_CYCLE_STATE_CHANGE, CHA_TRANSITION_2));

        // Plan 1
        // ----------------------------
        builder.putEvent(
                new PerfLogEventVO(T0.plusMinutes(5), PerfLogEventVO.Event.COORD_PATTERN_CHANGE, CHA_INSTEP)); // 131

        builder.putEvent(
                new PerfLogEventVO(T0.plusMinutes(6), PerfLogEventVO.Event.COORD_CYCLE_STATE_CHANGE, CHA_INSTEP));
        builder.putEvent(
                new PerfLogEventVO(T0.plusMinutes(9), PerfLogEventVO.Event.COORD_CYCLE_STATE_CHANGE, CHA_TRANSITION_2));
        builder.putEvent(new PerfLogEventVO(T0.plusMinutes(11), PerfLogEventVO.Event.COORD_CYCLE_STATE_CHANGE,
                CHA_TRANSITION_3));
        builder.putEvent(
                new PerfLogEventVO(T0.plusMinutes(13), PerfLogEventVO.Event.COORD_CYCLE_STATE_CHANGE, CHA_INSTEP));
        builder.putEvent(new PerfLogEventVO(T0.plusMinutes(56), PerfLogEventVO.Event.COORD_CYCLE_STATE_CHANGE,
                CHA_TRANSITION_3));
        builder.putEvent(
                new PerfLogEventVO(T0.plusMinutes(59), PerfLogEventVO.Event.COORD_CYCLE_STATE_CHANGE, CHA_INSTEP));
        builder.putEvent(
                new PerfLogEventVO(T1.plusMinutes(2), PerfLogEventVO.Event.COORD_CYCLE_STATE_CHANGE, CHA_INSTEP));
        builder.putEvent(
                new PerfLogEventVO(T1.plusMinutes(4), PerfLogEventVO.Event.COORD_CYCLE_STATE_CHANGE, CHA_TRANSITION_2));
        builder.putEvent(
                new PerfLogEventVO(T1.plusMinutes(5), PerfLogEventVO.Event.COORD_CYCLE_STATE_CHANGE, CHA_INSTEP));
        builder.putEvent(
                new PerfLogEventVO(T1.plusMinutes(8), PerfLogEventVO.Event.COORD_CYCLE_STATE_CHANGE, CHA_INSTEP));
        builder.putEvent(
                new PerfLogEventVO(T1.plusMinutes(10), PerfLogEventVO.Event.COORD_CYCLE_STATE_CHANGE, CHA_INSTEP));
        builder.putEvent(new PerfLogEventVO(T1.plusMinutes(13), PerfLogEventVO.Event.COORD_CYCLE_STATE_CHANGE,
                CHA_TRANSITION_4));
        builder.putEvent(
                new PerfLogEventVO(T1.plusMinutes(15), PerfLogEventVO.Event.COORD_CYCLE_STATE_CHANGE, CHA_INSTEP));
        builder.putEvent(
                new PerfLogEventVO(T1.plusMinutes(19), PerfLogEventVO.Event.COORD_CYCLE_STATE_CHANGE, CHA_INSTEP));
        builder.putEvent(
                new PerfLogEventVO(T1.plusMinutes(22), PerfLogEventVO.Event.COORD_CYCLE_STATE_CHANGE, CHA_INSTEP));
        // Plan 2
        // ----------------------------
        builder.putEvent(new PerfLogEventVO(T1.plusMinutes(26), PerfLogEventVO.Event.COORD_PATTERN_CHANGE, CHA_INSTEP));
        builder.putEvent(new PerfLogEventVO(T1.plusMinutes(27), PerfLogEventVO.Event.COORD_CYCLE_STATE_CHANGE,
                CHA_TRANSITION_4));
        builder.putEvent(
                new PerfLogEventVO(T1.plusMinutes(30), PerfLogEventVO.Event.COORD_CYCLE_STATE_CHANGE, CHA_INSTEP));
        builder.putEvent(
                new PerfLogEventVO(T1.plusMinutes(36), PerfLogEventVO.Event.COORD_CYCLE_STATE_CHANGE, CHA_INSTEP));
        builder.putEvent(new PerfLogEventVO(T1.plusMinutes(39), PerfLogEventVO.Event.COORD_CYCLE_STATE_CHANGE,
                CHA_TRANSITION_2));
        builder.putEvent(
                new PerfLogEventVO(T1.plusMinutes(44), PerfLogEventVO.Event.COORD_CYCLE_STATE_CHANGE, CHA_INSTEP));
        builder.putEvent(new PerfLogEventVO(T1.plusMinutes(46), PerfLogEventVO.Event.COORD_CYCLE_STATE_CHANGE,
                CHA_TRANSITION_2));
        builder.putEvent(
                new PerfLogEventVO(T1.plusMinutes(50), PerfLogEventVO.Event.COORD_CYCLE_STATE_CHANGE, CHA_INSTEP));
        builder.putEvent(
                new PerfLogEventVO(T1.plusMinutes(55), PerfLogEventVO.Event.COORD_CYCLE_STATE_CHANGE, CHA_INSTEP));
        builder.putEvent(
                new PerfLogEventVO(T1.plusMinutes(57), PerfLogEventVO.Event.COORD_CYCLE_STATE_CHANGE, CHA_INSTEP));
        builder.putEvent(
                new PerfLogEventVO(T2.plusMinutes(1), PerfLogEventVO.Event.COORD_CYCLE_STATE_CHANGE, CHA_TRANSITION_3));
        builder.putEvent(
                new PerfLogEventVO(T2.plusMinutes(5), PerfLogEventVO.Event.COORD_CYCLE_STATE_CHANGE, CHA_TRANSITION_3));
        builder.putEvent(
                new PerfLogEventVO(T2.plusMinutes(7), PerfLogEventVO.Event.COORD_CYCLE_STATE_CHANGE, CHA_TRANSITION_3));
        builder.putEvent(
                new PerfLogEventVO(T2.plusMinutes(9), PerfLogEventVO.Event.COORD_CYCLE_STATE_CHANGE, CHA_TRANSITION_3));
        builder.putEvent(new PerfLogEventVO(T2.plusMinutes(14), PerfLogEventVO.Event.COORD_CYCLE_STATE_CHANGE,
                CHA_TRANSITION_3));
        // Plan 3
        // ----------------------------
        builder.putEvent(new PerfLogEventVO(T2.plusMinutes(16), PerfLogEventVO.Event.COORD_PATTERN_CHANGE, CHA_INSTEP));
        builder.putEvent(new PerfLogEventVO(T2.plusMinutes(18), PerfLogEventVO.Event.COORD_CYCLE_STATE_CHANGE,
                CHA_TRANSITION_3));
        builder.putEvent(
                new PerfLogEventVO(T2.plusMinutes(25), PerfLogEventVO.Event.COORD_CYCLE_STATE_CHANGE, CHA_INSTEP));
        // Plan 4
        // ----------------------------
        builder.putEvent(new PerfLogEventVO(T2.plusMinutes(29), PerfLogEventVO.Event.COORD_PATTERN_CHANGE, CHA_INSTEP));
        builder.putEvent(
                new PerfLogEventVO(T2.plusMinutes(32), PerfLogEventVO.Event.COORD_CYCLE_STATE_CHANGE, CHA_INSTEP));
        builder.putEvent(new PerfLogEventVO(T2.plusMinutes(35), PerfLogEventVO.Event.COORD_CYCLE_STATE_CHANGE,
                CHA_TRANSITION_3));
        builder.putEvent(new PerfLogEventVO(T2.plusMinutes(38), PerfLogEventVO.Event.COORD_CYCLE_STATE_CHANGE,
                CHA_TRANSITION_3));
        builder.putEvent(
                new PerfLogEventVO(T2.plusMinutes(41), PerfLogEventVO.Event.COORD_CYCLE_STATE_CHANGE, CHA_INSTEP));
        builder.putEvent(new PerfLogEventVO(T2.plusMinutes(46), PerfLogEventVO.Event.COORD_CYCLE_STATE_CHANGE,
                CHA_TRANSITION_3));
        builder.putEvent(new PerfLogEventVO(T2.plusMinutes(50), PerfLogEventVO.Event.COORD_CYCLE_STATE_CHANGE,
                CHA_TRANSITION_3));
        builder.putEvent(
                new PerfLogEventVO(T2.plusMinutes(52), PerfLogEventVO.Event.COORD_CYCLE_STATE_CHANGE, CHA_INSTEP));
        // Plan 5
        // ----------------------------
        builder.putEvent(new PerfLogEventVO(T2.plusMinutes(57), PerfLogEventVO.Event.COORD_PATTERN_CHANGE, CHA_INSTEP));
        builder.putEvent(
                new PerfLogEventVO(T2.plusMinutes(59), PerfLogEventVO.Event.COORD_CYCLE_STATE_CHANGE, CHA_INSTEP));

        builder.build();
    }

    @Test
    @DisplayName("Test coordination Bin List")
    void testCoordHealthBinList() {
        List<CoordinationHealthBinVO> binList = chartVO.getCoorHealthBinList();
        assertEquals(12, binList.size());
        CoordinationHealthBinVO bin0 = binList.get(0);
        assertEquals(7, bin0.totalCycles());
        assertEquals(100.0 * 4 / 7, bin0.perChaInstep());
        assertEquals(100.0 * 3 / 7, bin0.perChaTransition());

        CoordinationHealthBinVO bin11 = binList.get(11);
        assertEquals(4, bin11.totalCycles());
        assertEquals(100.0 * 2 / 4, bin11.perChaInstep());
        assertEquals(100.0 * 2 / 4, bin11.perChaTransition());
    }

    @Test
    void testPlanStatisticsList() {
        List<CoordinationHealthPlanStatisticsVO> planList = chartVO.getCoorHealthPlanStatisticsList();
        assertEquals(6, planList.size());
        CoordinationHealthPlanStatisticsVO plan0 = planList.get(0);
        assertEquals(3, plan0.getCycle());
        assertEquals(100.0 * 2 / 3, plan0.perChaInstep());
        assertEquals(100.0 * 1 / 3, plan0.perChaTransition());

        CoordinationHealthPlanStatisticsVO plan5 = planList.get(5);
        assertEquals(1, plan5.getCycle());
        assertEquals(100.0 * 1, plan5.perChaInstep());
        assertEquals(100.0 * 0, plan5.perChaTransition());

    }

}
