package com.siemens.spm.analysis.factory.appdelay.singlelane;

import java.time.LocalDateTime;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;

import com.siemens.spm.analysis.factory.AppDelayChartBuilder;
import com.siemens.spm.analysis.factory.AppDelayLaneChartBuilder;
import com.siemens.spm.analysis.util.ChartType;
import com.siemens.spm.analysis.vo.AppDelayChartVO;
import com.siemens.spm.analysis.vo.AppDelayLaneChartVO;
import com.siemens.spm.perflog.vo.ApproachVO;
import com.siemens.spm.perflog.vo.DetectorVO;
import com.siemens.spm.perflog.vo.IntersectionConfigVO;
import com.siemens.spm.perflog.vo.LaneVO;
import com.siemens.spm.perflog.vo.PhaseVO;

// TODO: need to double check it
@Disabled
class AppDelayChartBuilderOneDetector900SecondBinSpecialCaseSimulationTest {
    private static final int PHASE_NUM = 1;
    private static final int YEAR = 2021;
    private static final int MONTH = 12;
    private static final int DAY = 9;
    public static final int BIN_SIZE_900 = 900;
    private static final LocalDateTime T0 = LocalDateTime.of(YEAR, MONTH, DAY, 0, 0);
    private static final LocalDateTime T3 = LocalDateTime.of(YEAR, MONTH, DAY, 3, 0);

    private AppDelayChartVO chartVO;
    private AppDelayChartBuilder builder;

    @BeforeEach
    void setUp() {
        chartVO = AppDelayLaneChartVO.builder()
                .fromTime(T0)
                .toTime(T3.plusMinutes(30))
                .phase(PHASE_NUM)
                .chartType(ChartType.APP_DELAY_LANE.getChartName())
                .build();

        builder = new AppDelayLaneChartBuilder(chartVO, BIN_SIZE_900, 0);

        // create config with one lane with "through" movement
        IntersectionConfigVO configVO = IntersectionConfigVO.builder().
                approaches(
                        List.of(
                                ApproachVO.builder()
                                        .phases(
                                                List.of(
                                                        PhaseVO.builder()
                                                                .lanes(
                                                                        List.of(
                                                                                LaneVO.builder()
                                                                                        .movement(
                                                                                                "Through"
                                                                                        )
                                                                                        .detectors(
                                                                                                List.of(DetectorVO
                                                                                                        .builder()
                                                                                                        .detectorNumber(
                                                                                                                1)
                                                                                                        .type(DetectorVO.DetectorType.STOP_BAR)
                                                                                                        .distance(0.0)
                                                                                                        .build())
                                                                                        )
                                                                                        .build()
                                                                        )
                                                                )
                                                                .phaseNumber(PHASE_NUM)
                                                                .build()
                                                )
                                        )
                                        .approachSpeed(31.0686) // 31.0686 mph = 50 kmph
                                        .build()
                        )
                ).build();
        builder.setIntersectionConfig(configVO);

    }

}
