package com.siemens.spm.analysis.factory;

import com.siemens.spm.analysis.vo.Plan;
import com.siemens.spm.analysis.vo.SplitFailureChartVO;
import com.siemens.spm.analysis.vo.SplitFailureCycleVO;
import com.siemens.spm.analysis.vo.SplitFailurePlanStatisticsVO;
import com.siemens.spm.perflog.vo.DetectorInfoVO;
import com.siemens.spm.perflog.vo.IntersectionConfigVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

class SplitFailureBuilderTest {
    private static final int PHASE_NUM = 1;
    private static final int DET_NUM = 1; // Detector number is 1, assuming mapped to phase 1

    private static final int YEAR = 2021;
    private static final int MONTH = 8;
    private static final int DAY = 10;
    private static final LocalDateTime T0 = LocalDateTime.of(YEAR, MONTH, DAY, 11, 0);
    private static final LocalDateTime T1 = LocalDateTime.of(YEAR, MONTH, DAY, 12, 0);
    private static final LocalDateTime T2 = LocalDateTime.of(YEAR, MONTH, DAY, 13, 0);
    private static final LocalDateTime T3 = LocalDateTime.of(YEAR, MONTH, DAY, 14, 0);

    private SplitFailureChartVO chartVO;
    private SplitFailureChartBuilder builder;

    @BeforeEach
    void setUp() {
        chartVO = new SplitFailureChartVO();
        chartVO.setFromTime(T0);
        chartVO.setToTime(T3);
        chartVO.setPhase(PHASE_NUM);

        builder = new SplitFailureChartBuilder(chartVO);
        // Set config with simulate det 1 call phase 1
        builder.setIntersectionConfig(IntersectionConfigVO.builder()
                .detInfo(DetectorInfoVO.builder()
                        .detCallPhases(new int[][] { { 1 } })
                        .build())
                .build());

        // -------------- PHASE ON --------------
        builder.putEvent(
                new PerfLogEventVO(T0.plusMinutes(1).plusSeconds(9), PerfLogEventVO.Event.PHASE_ON, PHASE_NUM));
        // -------------- GREEN --------------
        builder.putEvent(new PerfLogEventVO(T0.plusMinutes(1).plusSeconds(9), PerfLogEventVO.Event.PHASE_BEGIN_GREEN,
                PHASE_NUM));
        builder.putEvent(
                new PerfLogEventVO(T0.plusMinutes(1).plusSeconds(10), PerfLogEventVO.Event.DETECTOR_OFF, DET_NUM));
        builder.putEvent(
                new PerfLogEventVO(T0.plusMinutes(1).plusSeconds(11), PerfLogEventVO.Event.DETECTOR_ON, DET_NUM));
        builder.putEvent(
                new PerfLogEventVO(T0.plusMinutes(1).plusSeconds(18), PerfLogEventVO.Event.DETECTOR_OFF, DET_NUM));
        builder.putEvent(
                new PerfLogEventVO(T0.plusMinutes(1).plusSeconds(20), PerfLogEventVO.Event.DETECTOR_ON, DET_NUM));
        // -------YELLOW--------
        builder.putEvent(
                new PerfLogEventVO(T0.plusMinutes(1).plusSeconds(50), PerfLogEventVO.Event.PHASE_BEGIN_YELLOW_CLEARANCE,
                        PHASE_NUM));
        builder.putEvent(new PerfLogEventVO(T0.plusMinutes(2), PerfLogEventVO.Event.DETECTOR_OFF, DET_NUM));
        // -------------- RED --------------
        builder.putEvent(
                new PerfLogEventVO(T0.plusMinutes(3), PerfLogEventVO.Event.PHASE_BEGIN_RED_CLEARANCE, PHASE_NUM));
        builder.putEvent(new PerfLogEventVO(T0.plusMinutes(4), PerfLogEventVO.Event.DETECTOR_ON, DET_NUM));
        builder.putEvent(
                new PerfLogEventVO(T0.plusMinutes(4).plusSeconds(10), PerfLogEventVO.Event.DETECTOR_OFF, DET_NUM));

        // Plan 1
        // ----------------------------
        builder.putEvent(new PerfLogEventVO(T0.plusMinutes(5), PerfLogEventVO.Event.COORD_PATTERN_CHANGE,
                Plan.FREE_PLAN)); // 131
        // -------------- PHASE ON --------------
        builder.putEvent(new PerfLogEventVO(T0.plusMinutes(5).plusSeconds(9), PerfLogEventVO.Event.PHASE_ON,
                PHASE_NUM));
        // -------------- GREEN --------------
        builder.putEvent(new PerfLogEventVO(T0.plusMinutes(6).plusSeconds(9), PerfLogEventVO.Event.PHASE_BEGIN_GREEN,
                PHASE_NUM));
        builder.putEvent(new PerfLogEventVO(T0.plusMinutes(9), PerfLogEventVO.Event.DETECTOR_ON, DET_NUM));
        builder.putEvent(new PerfLogEventVO(T0.plusMinutes(11), PerfLogEventVO.Event.DETECTOR_OFF, DET_NUM));
        builder.putEvent(new PerfLogEventVO(T0.plusMinutes(13), PerfLogEventVO.Event.DETECTOR_ON, DET_NUM));
        builder.putEvent(new PerfLogEventVO(T0.plusMinutes(56), PerfLogEventVO.Event.DETECTOR_OFF, DET_NUM));
        builder.putEvent(new PerfLogEventVO(T0.plusMinutes(59), PerfLogEventVO.Event.DETECTOR_ON, DET_NUM));
        builder.putEvent(new PerfLogEventVO(T1.plusMinutes(2), PerfLogEventVO.Event.DETECTOR_OFF, DET_NUM));
        // -------------- YELLOW --------------
        builder.putEvent(
                new PerfLogEventVO(T1.plusMinutes(3), PerfLogEventVO.Event.PHASE_BEGIN_YELLOW_CLEARANCE, PHASE_NUM));
        builder.putEvent(new PerfLogEventVO(T1.plusMinutes(4), PerfLogEventVO.Event.DETECTOR_ON, DET_NUM));
        // -------------- RED --------------
        builder.putEvent(
                new PerfLogEventVO(T1.plusMinutes(5), PerfLogEventVO.Event.PHASE_BEGIN_RED_CLEARANCE, PHASE_NUM));
        // builder.putEvent(
        // new PerfLogEventVO(T1.plusMinutes(5), PerfLogEventVO.Event.DETECTOR_ON,
        // DET_NUM));
        builder.putEvent(
                new PerfLogEventVO(T1.plusMinutes(5).plusSeconds(2), PerfLogEventVO.Event.DETECTOR_OFF, DET_NUM));
        builder.putEvent(
                new PerfLogEventVO(T1.plusMinutes(5).plusSeconds(3), PerfLogEventVO.Event.DETECTOR_ON, DET_NUM));
        // -------------- PHASE ON --------------
        builder.putEvent(new PerfLogEventVO(T1.plusMinutes(15), PerfLogEventVO.Event.PHASE_ON, PHASE_NUM));
        // ------- GREEN --------
        builder.putEvent(new PerfLogEventVO(T1.plusMinutes(15), PerfLogEventVO.Event.PHASE_BEGIN_GREEN, PHASE_NUM));
        builder.putEvent(new PerfLogEventVO(T1.plusMinutes(19), PerfLogEventVO.Event.DETECTOR_OFF, DET_NUM));
        builder.putEvent(new PerfLogEventVO(T1.plusMinutes(22), PerfLogEventVO.Event.DETECTOR_ON, DET_NUM));
        // Plan 2
        // ----------------------------
        // builder.putEvent(new PerfLogEventVO(T1.plusMinutes(26),
        // PerfLogEventVO.Event.COORD_PATTERN_CHANGE, 2));
        builder.putEvent(new PerfLogEventVO(T1.plusMinutes(27), PerfLogEventVO.Event.DETECTOR_OFF, DET_NUM));
        builder.putEvent(new PerfLogEventVO(T1.plusMinutes(30), PerfLogEventVO.Event.DETECTOR_ON, DET_NUM));
        builder.putEvent(new PerfLogEventVO(T1.plusMinutes(36), PerfLogEventVO.Event.DETECTOR_OFF, DET_NUM));
        builder.putEvent(new PerfLogEventVO(T1.plusMinutes(39), PerfLogEventVO.Event.DETECTOR_ON, DET_NUM));
        builder.putEvent(new PerfLogEventVO(T1.plusMinutes(44), PerfLogEventVO.Event.DETECTOR_OFF, DET_NUM));
        builder.putEvent(new PerfLogEventVO(T1.plusMinutes(46), PerfLogEventVO.Event.DETECTOR_ON, DET_NUM));
        // -------YELLOW--------
        builder.putEvent(
                new PerfLogEventVO(T1.plusMinutes(50), PerfLogEventVO.Event.PHASE_BEGIN_YELLOW_CLEARANCE, PHASE_NUM));
        // -------RED--------
        builder.putEvent(
                new PerfLogEventVO(T1.plusMinutes(55).plusSeconds(0), PerfLogEventVO.Event.PHASE_BEGIN_RED_CLEARANCE,
                        PHASE_NUM));
        builder.putEvent(
                new PerfLogEventVO(T1.plusMinutes(55).plusSeconds(4), PerfLogEventVO.Event.DETECTOR_OFF, DET_NUM));
        // -------------- PHASE ON --------------
        builder.putEvent(new PerfLogEventVO(T2.plusMinutes(1), PerfLogEventVO.Event.PHASE_ON, PHASE_NUM));
        // ------- GREEN --------
        builder.putEvent(new PerfLogEventVO(T2.plusMinutes(1), PerfLogEventVO.Event.PHASE_BEGIN_GREEN, PHASE_NUM));

        // plan 3
        // ------------------------------
        builder.putEvent(new PerfLogEventVO(T2.plusMinutes(5), PerfLogEventVO.Event.COORD_PATTERN_CHANGE, 3));
        builder.putEvent(new PerfLogEventVO(T2.plusMinutes(7), PerfLogEventVO.Event.DETECTOR_ON, DET_NUM));
        builder.putEvent(new PerfLogEventVO(T2.plusMinutes(9), PerfLogEventVO.Event.DETECTOR_OFF, DET_NUM));
        builder.putEvent(new PerfLogEventVO(T2.plusMinutes(14), PerfLogEventVO.Event.DETECTOR_ON, DET_NUM));
        builder.build();
    }

    //@Test
    void testSplitFailureCycleList() {
        List<SplitFailureCycleVO> cycleList = chartVO.getSplitFailureCycleList();
        assertEquals(5, cycleList.size());

        SplitFailureCycleVO cycle1 = cycleList.get(1);
        assertEquals((double) 100 * 38 / 41, cycle1.perGor());
        assertEquals((double) 100 * 0 / 5, cycle1.perRor5());
        assertEquals(2, cycle1.indicator());

        SplitFailureCycleVO cycle2 = cycleList.get(2);
        assertEquals((double) 100 * 2880 / 3411, cycle2.perGor());
        assertEquals((double) 100 * 4 / 5, cycle2.perRor5());
        assertEquals(5, cycle2.indicator());

        SplitFailureCycleVO cycle3 = cycleList.get(3);
        assertEquals((double) 100 * 1440 / 2100, cycle3.perGor());
        assertEquals((double) 100 * 4 / 5, cycle3.perRor5());
        assertEquals(5, cycle3.indicator());

    }

    @Test
    void testPlanStatisticsList() {
        List<SplitFailurePlanStatisticsVO> planList = chartVO.getSplitFailurePlanStatisticsList();
        assertEquals(3, planList.size());
        SplitFailurePlanStatisticsVO plan0 = planList.get(0);
        assertEquals(0, plan0.getTotalSF());

        SplitFailurePlanStatisticsVO plan1 = planList.get(1);
        assertEquals(0, plan1.getTotalSF());

    }

}
