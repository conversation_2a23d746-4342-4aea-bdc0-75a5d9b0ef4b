// /*
//  * Copyright (C) Siemens.  All Rights Reserved.
//  *
//  * Source      : AorChartBuilderTest.java
//  * Project     : SPM Platform
//  */
// package com.siemens.spm.analysis.factory;

// import com.siemens.spm.analysis.vo.AorBinVO;
// import com.siemens.spm.analysis.vo.AorChartVO;
// import com.siemens.spm.analysis.vo.AorPlanStatisticsVO;
// import com.siemens.spm.perflog.vo.PerfLogEventVO;
// import com.siemens.spm.perflog.vo.PerfLogEventVO.Event;
// import org.junit.jupiter.api.BeforeEach;
// import org.junit.jupiter.api.Test;

// import java.time.LocalDateTime;
// import java.util.List;

// import static org.junit.jupiter.api.Assertions.assertEquals;

// class AorChartBuilderTest {

//     private static final int[][] DET_CALL_PHASES = { { 1 } };
//     private static final int PHASE_NUM = 1;
//     private static final int DET_NUM = 1; // Detector number is 1, assuming mapped to phase 1

//     private static final int YEAR = 2020;
//     private static final int MONTH = 5;
//     private static final int DAY = 20;
//     private static final LocalDateTime T0 = LocalDateTime.of(YEAR, MONTH, DAY, 12, 0);
//     private static final LocalDateTime T1 = LocalDateTime.of(YEAR, MONTH, DAY, 13, 0);
//     private static final LocalDateTime T2 = LocalDateTime.of(YEAR, MONTH, DAY, 14, 0);

//     private AorChartVO chartVO;
//     private AorChartBuilder builder;

//     @BeforeEach
//     void setUp() {
//         chartVO = new AorChartVO();
//         chartVO.setPhase(PHASE_NUM);
//         chartVO.setFromTime(T0);
//         chartVO.setToTime(T2);

//         builder = new AorChartBuilder(chartVO, AorBinVO.BIN_SIZE_900, DET_CALL_PHASES);

//         // Detector on
//         builder.putEvent(new PerfLogEventVO(T0, Event.DETECTOR_ON, DET_NUM));
//         builder.putEvent(new PerfLogEventVO(T0.plusMinutes(1), Event.DETECTOR_ON, DET_NUM));
//         // Green
//         builder.putEvent(new PerfLogEventVO(T0.plusMinutes(5), Event.PHASE_BEGIN_GREEN, PHASE_NUM));
//         // Detector on
//         builder.putEvent(new PerfLogEventVO(T0.plusMinutes(10), Event.DETECTOR_ON, DET_NUM));
//         // Yellow
//         builder.putEvent(new PerfLogEventVO(T0.plusMinutes(15), Event.PHASE_BEGIN_YELLOW_CLEARANCE, PHASE_NUM));
//         // Detector on
//         builder.putEvent(new PerfLogEventVO(T0.plusMinutes(20), Event.DETECTOR_ON, DET_NUM));
//         // Red
//         builder.putEvent(new PerfLogEventVO(T0.plusMinutes(25), Event.PHASE_BEGIN_RED_CLEARANCE, PHASE_NUM));
//         // Detector on
//         builder.putEvent(new PerfLogEventVO(T0.plusMinutes(30), Event.DETECTOR_ON, DET_NUM));

//         // Plan 1
//         // ----------------------------
//         builder.putEvent(new PerfLogEventVO(T0.plusMinutes(35), Event.COORD_PATTERN_CHANGE, PHASE_NUM));

//         // Detector on
//         builder.putEvent(new PerfLogEventVO(T0.plusMinutes(40), Event.DETECTOR_ON, DET_NUM));
//         // Green
//         builder.putEvent(new PerfLogEventVO(T0.plusMinutes(45), Event.PHASE_BEGIN_GREEN, PHASE_NUM));
//         // Detector on
//         builder.putEvent(new PerfLogEventVO(T0.plusMinutes(50), Event.DETECTOR_ON, DET_NUM));
//         // Yellow
//         builder.putEvent(new PerfLogEventVO(T0.plusMinutes(55), Event.PHASE_BEGIN_YELLOW_CLEARANCE, PHASE_NUM));
//         // Detector on
//         builder.putEvent(new PerfLogEventVO(T1, Event.DETECTOR_ON, DET_NUM));
//         // Red
//         builder.putEvent(new PerfLogEventVO(T1.plusMinutes(5), Event.PHASE_BEGIN_RED_CLEARANCE, PHASE_NUM));
//         // Detector on
//         builder.putEvent(new PerfLogEventVO(T1.plusMinutes(10), Event.DETECTOR_ON, DET_NUM));

//         builder.build();
//     }

//     @Test
//     void testTotalDetHits() {
//         assertEquals(9, chartVO.getTotalDetectorHits());
//     }

//     @Test
//     void testTotalAorHits() {
//         assertEquals(5, chartVO.getTotalAorHits());
//     }

//     @Test
//     void testAorBinList() {
//         List<AorBinVO> binList = chartVO.getAorBins();
//         assertEquals(8, binList.size());

//         AorBinVO bin0 = binList.get(0);
//         assertEquals(12, bin0.getVolume());
//         assertEquals(8, bin0.getAorVolume());

//         AorBinVO bin1 = binList.get(1);
//         assertEquals(4, bin1.getVolume());
//         assertEquals(0, bin1.getAorVolume());

//         AorBinVO bin2 = binList.get(2);
//         assertEquals(8, bin2.getVolume());
//         assertEquals(8, bin2.getAorVolume());

//         AorBinVO bin3 = binList.get(3);
//         assertEquals(4, bin3.getVolume());
//         assertEquals(0, bin3.getAorVolume());

//         AorBinVO bin4 = binList.get(4);
//         assertEquals(8, bin4.getVolume());
//         assertEquals(4, bin4.getAorVolume());
//     }

//     @Test
//     void testPlanStatisticsList() {
//         List<AorPlanStatisticsVO> planStatList = chartVO.getPlanStatistics();
//         assertEquals(2, planStatList.size());

//         AorPlanStatisticsVO stat0 = planStatList.get(0);
//         assertEquals(60.0, stat0.getAorPercent());
//         assertEquals(15.0 / 35.0 * 100, stat0.getRedTimePercent());

//         AorPlanStatisticsVO stat1 = planStatList.get(1);
//         assertEquals(50.0, stat1.getAorPercent());
//         assertEquals(65.0 / 85.0 * 100, stat1.getRedTimePercent());
//     }

// }
