package com.siemens.spm.analysis.factory.appdelay;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import org.junit.jupiter.api.Test;

import com.siemens.spm.analysis.aggregator.AppDelaySupporter;
import com.siemens.spm.analysis.vo.AppDelayVehicleVO;
import com.siemens.spm.perflog.vo.DetectorVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;
import com.siemens.spm.perflog.vo.DetectorVO.DetectorType;

class AppDelaySupporterTest {

    @Test
    void traveTimeToStopBar() {
        DetectorVO advanceDet = new DetectorVO();

        // 400 feet
        advanceDet.setDistance(400.0);

        AppDelaySupporter appDelaySupporter = new AppDelaySupporter(
                1,
                null,
                0,
                30,
                advanceDet
        );

        long travelTimeToStopBarMills = appDelaySupporter.getTravelTimeToStopBarMills();

        // compute the expected travel time to stop bar in milliseconds
        long expectedTravelTimeToStopBarMills = (long) ((400.0 * 3600 * 1000) / (30 * 5280));

        assertEquals(expectedTravelTimeToStopBarMills, travelTimeToStopBarMills);

        advanceDet.setDistance(300.0);
        appDelaySupporter = new AppDelaySupporter(
                1,
                null,
                0,
                35,
                advanceDet
        );
        travelTimeToStopBarMills = appDelaySupporter.getTravelTimeToStopBarMills();
        expectedTravelTimeToStopBarMills = (long) ((300.0 * 3600 * 1000) / (35 * 5280));

        assertEquals(expectedTravelTimeToStopBarMills, travelTimeToStopBarMills);
    }

    @Test
    void testDelayByPhaseCyclesCalculation() {
        final int phaseNum = 8;
        final int advanceDetNum = 40;
        AppDelaySupporter appDelaySupporter = new AppDelaySupporter(
                phaseNum,
                LocalDateTime.of(2025, 2, 10, 9, 0, 0),
                0,
                35,
                DetectorVO.builder()
                        .type(DetectorType.ADVANCE)
                        .detectorNumber(advanceDetNum)
                        .distance(360.0)
                        .build()
        );

        List<PerfLogEventVO> events = new ArrayList<>();

        // Phase begin green
        PerfLogEventVO eventVO = PerfLogEventVO.builder()
                .event(PerfLogEventVO.Event.PHASE_BEGIN_GREEN)
                .dateTime(LocalDateTime.parse("2025-02-10T09:12:43.500"))
                .parameter(phaseNum)
                .build();
        events.add(eventVO);

        // Advance detector during green
        eventVO = PerfLogEventVO.builder()
                .event(PerfLogEventVO.Event.DETECTOR_ON)
                .dateTime(LocalDateTime.parse("2025-02-10T09:12:58.500"))
                .parameter(advanceDetNum)
                .build();

        events.add(eventVO);

        // phase green termination
        eventVO = PerfLogEventVO.builder()
                .event(PerfLogEventVO.Event.PHASE_GREEN_TERMINATION)
                .dateTime(LocalDateTime.parse("2025-02-10T09:14:30.200"))
                .parameter(phaseNum)
                .build();

        events.add(eventVO);

        // Phase begin red
        eventVO = PerfLogEventVO.builder()
                .event(PerfLogEventVO.Event.PHASE_BEGIN_RED_CLEARANCE)
                .dateTime(LocalDateTime.parse("2025-02-10T09:14:34.200"))
                .parameter(phaseNum)
                .build();

        events.add(eventVO);

        // Advance detector on during red
        eventVO = PerfLogEventVO.builder()
                .event(PerfLogEventVO.Event.DETECTOR_ON)
                .dateTime(LocalDateTime.parse("2025-02-10T09:14:41.100"))
                .parameter(advanceDetNum)
                .build();

        events.add(eventVO);

        // Advance detector on during red => must be ignored because the event is right before next begin green
        // duration between advance detector on and next begin green is smaller than travel time to stop bar
        eventVO = PerfLogEventVO.builder()
                .event(PerfLogEventVO.Event.DETECTOR_ON)
                .dateTime(LocalDateTime.parse("2025-02-10T09:14:46.700"))
                .parameter(advanceDetNum)
                .build();

        events.add(eventVO);

        // next phase begin green
        eventVO = PerfLogEventVO.builder()
                .event(PerfLogEventVO.Event.PHASE_BEGIN_GREEN)
                .dateTime(LocalDateTime.parse("2025-02-10T09:14:49.800"))
                .parameter(phaseNum)
                .build();

        events.add(eventVO);

        events.forEach(appDelaySupporter::putEvent);

        appDelaySupporter.fillEndBins(LocalDateTime.parse("2025-02-10T09:15:00.000"));

        assertEquals(1, appDelaySupporter.getDelayVehicleList().size());

        AppDelayVehicleVO delayVehicleVO = appDelaySupporter.getDelayVehicleList().get(0);
        assertEquals(1, delayVehicleVO.getBinVolume());
        assertTrue(delayVehicleVO.getDelayDuration() - 104.288 < 0.001);
        
    }

}
