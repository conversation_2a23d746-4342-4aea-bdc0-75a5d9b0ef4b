package com.siemens.spm.analysis.factory.turningmovement;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.siemens.spm.analysis.vo.turningmovement.TurningMovementChartVO;
import com.siemens.spm.analysis.vo.turningmovement.TurningMovementDetectorCountVO;
import com.siemens.spm.analysis.vo.turningmovement.TurningMovementPlanStatVO;
import com.siemens.spm.perflog.vo.ApproachVO;
import com.siemens.spm.perflog.vo.DetectorVO;
import com.siemens.spm.perflog.vo.IntersectionConfigVO;
import com.siemens.spm.perflog.vo.LaneVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;
import com.siemens.spm.perflog.vo.PhaseVO;

class TurningMovementChartBuilderTest {

    private static final LocalDateTime FROM_TIME = LocalDateTime.of(2021, 5, 5, 10, 0);
    private static final LocalDateTime TO_TIME = LocalDateTime.of(2021, 5, 5, 11, 0);

    private static final String DIRECTION = "Eastbound";
    private static final int BIN_SIZE_300 = 300;
    private static final String MM_LEFT = "Left";
    private static final String MM_THROUGH_RIGHT = "Through/Right";

    private TurningMovementChartVO chartVO;
    private List<PerfLogEventVO> testEvents;

    @BeforeEach
    void setup() {
        TurningMovementChartBuilder chartBuilder = new TurningMovementChartBuilder(FROM_TIME, TO_TIME, DIRECTION,
                BIN_SIZE_300);
        chartBuilder.setIntersectionConfig(mockConfig());

        testEvents = readEvents();
        testEvents.forEach(perfLogEventVO -> chartBuilder.putEvent(perfLogEventVO, new HashMap<>()));

        chartVO = chartBuilder.build();
    }


    @Test
    void testPlanStatTimeRange() {
        // Create a new plan stat with specific time range
        LocalDateTime planStart = FROM_TIME.plusMinutes(15);
        LocalDateTime planEnd = FROM_TIME.plusMinutes(30);
        
        TurningMovementPlanStatVO planStat = new TurningMovementPlanStatVO(1, planStart);
        planStat.setToTime(planEnd);
        
        // Get detector IDs from config
        Set<Integer> detectorIds = new HashSet<>();
        detectorIds.add(3); // Left turn detector
        detectorIds.add(8); // Through/Right detector
        
        // Process events
        planStat.processEvents(testEvents, detectorIds);
        
        // Verify events are within time range
        Assertions.assertTrue(planStat.getTotal() > 0, "Should have events in time range");
        
        // Verify detector counts
        for (TurningMovementDetectorCountVO detCount : planStat.getDetCounts()) {
            Assertions.assertTrue(detCount.getDetectorValue() > 0, 
                "Detector " + detCount.getDetectorKey() + " should have events");
        }
    }

    @Test
    void testPlanStatDetectorFiltering() {
        // Create a plan stat
        TurningMovementPlanStatVO planStat = new TurningMovementPlanStatVO(1, FROM_TIME);
        planStat.setToTime(TO_TIME);
        
        // Test with only left turn detector
        Set<Integer> leftTurnDetector = new HashSet<>();
        leftTurnDetector.add(3);
        
        planStat.processEvents(testEvents, leftTurnDetector);
        
        // Verify only left turn events are counted
        Assertions.assertTrue(planStat.getTotal() > 0, "Should have left turn events");
        Assertions.assertEquals(1, planStat.getDetCounts().size(), 
            "Should only have one detector count");
        Assertions.assertEquals(3, planStat.getDetCounts().get(0).getDetectorKey(), 
            "Should be left turn detector");
    }

    @Test
    void testPlanStatEmptyTimeRange() {
        // Create a plan stat with empty time range
        LocalDateTime futureTime = TO_TIME.plusHours(1);
        TurningMovementPlanStatVO planStat = new TurningMovementPlanStatVO(1, futureTime);
        planStat.setToTime(futureTime.plusMinutes(15));
        
        // Get all detector IDs
        Set<Integer> detectorIds = new HashSet<>();
        detectorIds.add(3);
        detectorIds.add(8);
        
        // Process events
        planStat.processEvents(testEvents, detectorIds);
        
        // Verify no events are counted
        Assertions.assertEquals(0L, planStat.getTotal(), "Should have no events in future time range");
        Assertions.assertTrue(planStat.getDetCounts().isEmpty(), "Should have no detector counts");
    }

    @Test
    void testPlanStatMultiplePlans() {
        // Verify multiple plans are created
        List<TurningMovementPlanStatVO> planStats = chartVO.getPlanStatVOList();
        Assertions.assertFalse(planStats.isEmpty(), "Should have multiple plans");
        
        // Verify each plan has valid data
        for (TurningMovementPlanStatVO planStat : planStats) {
            Assertions.assertNotNull(planStat.getFromTime(), "Plan should have from time");
            Assertions.assertNotNull(planStat.getToTime(), "Plan should have to time");
            Assertions.assertNotNull(planStat.getTotal(), "Plan should have total count");
            Assertions.assertNotNull(planStat.getDetCounts(), "Plan should have detector counts");
        }
    }

    @Test
    void testPlanStatZeroDetectorValues() {
        // Create a plan stat
        TurningMovementPlanStatVO planStat = new TurningMovementPlanStatVO(1, FROM_TIME);
        planStat.setToTime(TO_TIME);
        
        // Test with a detector that has no events
        Set<Integer> detectorIds = new HashSet<>();
        detectorIds.add(3);  // Left turn detector
        detectorIds.add(8);  // Through/Right detector
        detectorIds.add(999); // Non-existent detector
        
        // Process events
        planStat.processEvents(testEvents, detectorIds);
        
        // Verify detector counts include zero values
        Map<Integer, Integer> detectorValueMap = planStat.getDetCounts().stream()
            .collect(HashMap::new,
                (map, detCount) -> map.put(detCount.getDetectorKey(), detCount.getDetectorValue()),
                HashMap::putAll);
        
        // Verify existing detectors have counts
        Assertions.assertTrue(detectorValueMap.get(3) > 0, "Left turn detector should have events");
        Assertions.assertTrue(detectorValueMap.get(8) > 0, "Through/Right detector should have events");
        
        // Verify non-existent detector is not included in counts
        Assertions.assertFalse(detectorValueMap.containsKey(999), 
            "Non-existent detector should not be included in counts");
    }


    private IntersectionConfigVO mockConfig() {
        return IntersectionConfigVO.builder()
                .approaches(mockApproaches())
                .build();
    }

    private List<ApproachVO> mockApproaches() {
        return List.of(
                ApproachVO.builder()
                        .direction(DIRECTION)
                        .phases(List.of(
                                PhaseVO.builder()
                                        .phaseNumber(3)
                                        .lanes(List.of(LaneVO.builder()
                                                .movement(MM_LEFT)
                                                .detectors(List.of(DetectorVO.builder()
                                                        .detectorNumber(3)
                                                        .type(DetectorVO.DetectorType.STOP_BAR)
                                                        .build()))
                                                .build()))
                                        .build(),
                                PhaseVO.builder()
                                        .phaseNumber(8)
                                        .lanes(List.of(LaneVO.builder()
                                                .movement(MM_THROUGH_RIGHT)
                                                .detectors(List.of(DetectorVO.builder()
                                                        .detectorNumber(8)
                                                        .type(DetectorVO.DetectorType.STOP_BAR)
                                                        .build()))
                                                .build()))
                                        .build()
                        ))
                        .build()
        );
    }

    private List<PerfLogEventVO> readEvents() {
        List<PerfLogEventVO> eventVOList = new ArrayList<>();

        ClassLoader classLoader = getClass().getClassLoader();
        File file = new File(Objects.requireNonNull(classLoader.getResource("turningmovement/2021-05-05T1000.csv"))
                .getFile());
        try (BufferedReader br = new BufferedReader(new FileReader(file))) {
            String line;
            while ((line = br.readLine()) != null) {
                String[] values = line.split(",");

                LocalDateTime time;
                try {
                    time = LocalDateTime.parse(values[0], DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.S"));
                } catch (Exception e) {
                    time = LocalDateTime.parse(values[0], DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"));
                }

                PerfLogEventVO.Event event = PerfLogEventVO.Event.of(Integer.parseInt(values[1]));
                long param = Long.parseLong(values[3]);

                eventVOList.add(new PerfLogEventVO(time, event, param));
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        return eventVOList;
    }

}
