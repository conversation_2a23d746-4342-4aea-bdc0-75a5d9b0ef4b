package com.siemens.spm.analysis.util;

import com.siemens.spm.analysis.vo.queuelength.QueueLengthCycleVO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

@Slf4j
class QueueLengthCycleUtilsTest {

    @Test
    void testBeautifyQueueLengthInCycleList_sizeBelow2_doNothing() {
        List<QueueLengthCycleVO> zeroCycleList = Collections.emptyList();

        List<QueueLengthCycleVO> cycleList = List.of(
                new QueueLengthCycleVO(LocalDateTime.MIN, LocalDateTime.MAX, 10D, 0)
        );
        QueueLengthCycleUtils.beautifyQueueLengthInCycleList(cycleList);

        Assertions.assertDoesNotThrow(() -> QueueLengthCycleUtils.beautifyQueueLengthInCycleList(zeroCycleList));
        Assertions.assertEquals(10D, cycleList.get(0).getQueueLength());
    }

    @Test
    void testBeautifyQueueLengthInCycleList_sizeEqualOrAbove2_beautify() {
        List<QueueLengthCycleVO> cycleList = List.of(
                new QueueLengthCycleVO(LocalDateTime.MIN, LocalDateTime.MAX, 10D, 0),
                new QueueLengthCycleVO(LocalDateTime.MIN, LocalDateTime.MAX, 10D, 0),
                new QueueLengthCycleVO(LocalDateTime.MIN, LocalDateTime.MAX, 10D, 0)
        );

        QueueLengthCycleUtils.beautifyQueueLengthInCycleList(cycleList);
        Assertions.assertEquals(7.5, cycleList.get(0).getQueueLength());
        Assertions.assertEquals(10, cycleList.get(1).getQueueLength());
        Assertions.assertEquals(7.5, cycleList.get(2).getQueueLength());
    }

    @Test
    void testBeautifyQueueLengthInCycleList_nullValue_beautifyUse0() {
        List<QueueLengthCycleVO> cycleList = List.of(
                new QueueLengthCycleVO(LocalDateTime.MIN, LocalDateTime.MAX, null, 0),
                new QueueLengthCycleVO(LocalDateTime.MIN, LocalDateTime.MAX, null, 0),
                new QueueLengthCycleVO(LocalDateTime.MIN, LocalDateTime.MAX, null, 0)
        );

        QueueLengthCycleUtils.beautifyQueueLengthInCycleList(cycleList);
        Assertions.assertEquals(0, cycleList.get(0).getQueueLength());
        Assertions.assertEquals(0, cycleList.get(1).getQueueLength());
        Assertions.assertEquals(0, cycleList.get(2).getQueueLength());
    }
}
