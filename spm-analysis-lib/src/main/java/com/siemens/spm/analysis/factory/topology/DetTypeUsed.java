package com.siemens.spm.analysis.factory.topology;

import java.lang.annotation.ElementType;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import com.siemens.spm.perflog.vo.DetectorVO.DetectorType;

/**
 * Annotation to specify the detector types used in a specific analysis factory.
 * Only events from detector(s) of given types are used for creating analysis.
 * <p>
 * The annotation can be applied to classes that represent analysis factories.
 * </p>
 * <p>
 * Example usage:
 * <pre>
 * {@code
 * @DetTypeUsed({ DetectorType.ADVANCE, DetectorType.STOP_BAR })
 * public class MyAnalysisFactory extends AbstractAnalysisFactory {
 *     // Implementation of the analysis factory
 * }
 * }
 * </pre>
 * </p>
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Inherited
public @interface DetTypeUsed {

    /**
     * The detector types that are required for the analysis.
     *
     * @return an array of required detector types
     */
    DetectorType[] value() default {};

    /**
     * The requirement for the detector types.
     * <ul>
     * <li> If set to {@link Require#ALL}, detector(s) of each specified type must be present.
     * <li> If set to {@link Require#ONE}, detector(s) of at least one of the specified types must be present.
     * <p> If detector(s) of multiple types are available, detector(s) of the type with lowest index in the array {@link #value()} will be used for analysis.
     * </ul>
     *
     * @return the requirement for the detector types
     */
    Require require() default Require.ONE;

    /**
     * The detector types that are optional for the analysis. If a detector type is specified as optional and required, it must be present.
     *
     * @return an array of optional detector types
     */
    DetectorType[] optional() default {};
}
