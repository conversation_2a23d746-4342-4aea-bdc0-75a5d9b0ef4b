package com.siemens.spm.analysis.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.analysis.vo.splitmonitor.SkippedInterval;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AppDelayChartVO extends PhaseChartVO {

    private static final long serialVersionUID = -7699168616638462850L;

    @JsonProperty("chart_type")
    private String chartType;

    // NOTE: Bin[fromTime, toTime)
    @JsonProperty("delay_vehicle")
    private List<AppDelayVehicleVO> appDelayVehicleList;

    @JsonProperty("plan_statistics")
    private List<AppDelayPlanStatisticsVO> appDelayPlanStatisticsList;

    @JsonProperty("skipped_intervals")
    private List<SkippedInterval> skippedPhaseIntervals;

    @JsonIgnore
    public int getSumAppDelayHit() {
        if (appDelayVehicleList == null) {
            return 0;
        }
        return appDelayVehicleList.stream().mapToInt(AppDelayVehicleVO::getBinVolume).sum();
    }

    @JsonIgnore
    public double getSumAppDelayTime() {
        if (appDelayVehicleList == null) {
            return 0;
        }
        return appDelayVehicleList.stream().mapToDouble(AppDelayVehicleVO::getDelayDuration).sum();
    }

}
