package com.siemens.spm.analysis.factory;

import com.siemens.spm.perflog.vo.IntersectionConfigVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;

public abstract class ChartBuilder {

    /**
     * Set intersection config to use whenever needed
     *
     * @param configVO {@link IntersectionConfigVO} object pass for use
     */
    protected abstract void setIntersectionConfig(IntersectionConfigVO configVO);

    /**
     * Put event to chart builder for processing
     *
     * @param eventVO eventVO
     */
    protected abstract void putEvent(PerfLogEventVO eventVO);

    /**
     * Finalize chart building and output to referenced chartVO
     */
    protected abstract void build();

}
