package com.siemens.spm.analysis.vo.abnormaldata;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AbnormalDataEventFilterVO implements Serializable {

    private static final long serialVersionUID = 844128144792594169L;

    @JsonProperty("event")
    private Integer eventCode;

    @JsonProperty("params")
    private List<Long> params;

    @JsonProperty("deviation")
    private Double deviation;

}
