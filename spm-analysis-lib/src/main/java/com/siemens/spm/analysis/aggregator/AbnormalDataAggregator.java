package com.siemens.spm.analysis.aggregator;

import com.siemens.spm.analysis.vo.abnormaldata.AbnormalDataDetailVO;
import com.siemens.spm.analysis.vo.abnormaldata.AbnormalDataVO;
import com.siemens.spm.analysis.vo.abnormaldata.DateHoursRawCount;
import com.siemens.spm.analysis.vo.abnormaldata.DateRawCount;
import com.siemens.spm.common.shared.domaintype.alarm.trafficmetric.MetricSpecifier;
import com.siemens.spm.common.shared.domaintype.alarm.trafficmetric.TrafficMetric;
import com.siemens.spm.perflog.vo.IntersectionConfigVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
@Builder
public class AbnormalDataAggregator implements PerfLogEventAggregator {

    private int eventCode;

    private Set<Long> params;

    private Double deviation;

    private final Map<String, DateRawCount> counts = new HashMap<>();

    private final Map<String, DateHoursRawCount> hoursRawCounts = new HashMap<>();

    private final Map<Long, Map<String, DateRawCount>> paramCounts = new HashMap<>();

    private static final Double DEFAULT_DEVIATION = 10D;

    public AbnormalDataAggregator(int eventCode, Set<Long> params, Double deviation) {
        this.eventCode = eventCode;
        this.params = params;

        this.deviation = Objects.requireNonNullElse(deviation, DEFAULT_DEVIATION);

        if (params != null) {
            params.forEach(p -> paramCounts.put(p, new HashMap<>()));
        }
    }

    @Override
    public void setTargetIdentifier(String targetIdentifier) {
        this.eventCode = Integer.getInteger(targetIdentifier);
    }

    @Override
    public void setBinSize(int binSize) {
        // un-used
    }

    @Override
    public void setFromTime(LocalDateTime fromTime) {
        // un-used
    }

    @Override
    public void setToTime(LocalDateTime toTime) {
        // un-used
    }

    @Override
    public void setConfig(IntersectionConfigVO configVO) {
        // un-used
    }

    @Override
    public void putEvent(PerfLogEventVO eventVO) {
        PerfLogEventVO.Event event = eventVO.getEvent();
        Long param = eventVO.getParameter();

        if (!isProcess(event.getEventNum(), param)) {
            return;
        }

        LocalDateTime eventTime = eventVO.getDateTime();
        LocalDate eventDate = eventTime.toLocalDate();

        countByHours(param, eventTime);
        incByDate(counts, eventDate);

        if (paramCounts.containsKey(param)) {
            incByDate(paramCounts.get(param), eventDate);
        } else {
            Map<String, DateRawCount> countInit = new HashMap<>();
            incByDate(countInit, eventDate);
            paramCounts.put(param, countInit);
        }
    }

    private void countByHours(Long param, LocalDateTime time) {
        String key = DateHoursRawCount.buildKey(param, time.toLocalDate(), time.getHour());
        if (this.hoursRawCounts.containsKey(key)) {
            this.hoursRawCounts.get(key).inc();
        } else {
            this.hoursRawCounts.put(key,
                    DateHoursRawCount.builder()
                            .param(param)
                            .date(time.toLocalDate())
                            .hours(time.getHour())
                            .build()
                            .inc());
        }
    }

    private boolean isProcess(Integer eventCode, Long param) {
        if (this.eventCode != eventCode) {
            return false;
        }

        return params == null || params.isEmpty() || params.contains(param);
    }

    private void incByDate(Map<String, DateRawCount> counts, LocalDate date) {
        if (counts.containsKey(date.toString())) {
            counts.get(date.toString()).inc();
        } else {
            counts.put(date.toString(), DateRawCount.builder().date(date).build().inc());
        }
    }

    @Override
    public List<TrafficMetric> getSupportedMetric() {
        return Collections.emptyList();
    }

    @Override
    public Double getAggregatedValue(MetricSpecifier metricSpecifier) {
        return null;
    }

    // TODO: Refactor using factory to build chart data from aggregator.
    public AbnormalDataVO toAbnormalDataVO(LocalDate targetDate, int period) {
        List<AbnormalDataDetailVO> details = paramCounts.entrySet()
                .stream()
                .map(entry -> AbnormalDataDetailVO.builder()
                        .param(entry.getKey())
                        .rawCounts(fillUp(entry.getValue().values(), targetDate, period))
                        .build()
                )
                .sorted(Comparator.comparing(AbnormalDataDetailVO::getParam))
                .toList();
        details.forEach(abnormalDataDetailVO -> abnormalDataDetailVO.updateAvg(targetDate));

        Map<LocalDate, Long> counter = details.stream()
                .flatMap(abnormalDataDetailVO -> abnormalDataDetailVO.getRawCounts().stream())
                .collect(Collectors.groupingBy(DateRawCount::getDate,
                        Collectors.summingLong(value -> value.getRawCount().get()))
                );
        List<DateRawCount> rawCounts = counter.entrySet()
                .stream()
                .map(entry -> DateRawCount.builder()
                        .date(entry.getKey())
                        .build()
                        .inc(entry.getValue())
                )
                .sorted(Comparator.comparing(DateRawCount::getDate, Collections.reverseOrder()))
                .toList();

        List<DateRawCount> rawCountsAvg = rawCounts
                .stream()
                .filter(dateRawCount -> !dateRawCount.getDate().isEqual(targetDate))
                .toList();
        double avg = (1D * rawCountsAvg.stream()
                .map(dateRawCount -> dateRawCount.getRawCount().get())
                .reduce(0L, Long::sum)) / rawCountsAvg.size();

        return AbnormalDataVO.builder()
                .eventCode(String.valueOf(eventCode))
                .eventName(PerfLogEventVO.Event.of(eventCode).translatedName())
                .rawCounts(fillUp(rawCounts, targetDate, period))
                .params(details.stream().map(AbnormalDataDetailVO::getParam).collect(Collectors.toSet()))
                .deviation(deviation)
                .chart(new ArrayList<>(hoursRawCounts.values()))
                .details(details)
                .avg(avg)
                .build();
    }

    private List<DateRawCount> fillUp(Collection<DateRawCount> origin, LocalDate targetDate, int period) {
        Set<LocalDate> allDates = origin.stream()
                .map(DateRawCount::getDate)
                .collect(Collectors.toSet());

        List<DateRawCount> filledResult = new ArrayList<>(origin);
        for (int i = 0; i <= period; i++) {
            LocalDate date = targetDate.minusDays(i * 7L);

            if (!allDates.contains(date)) {
                DateRawCount dateRawCount = DateRawCount.builder()
                        .date(date)
                        .build();
                filledResult.add(dateRawCount);
            }
        }

        filledResult.sort(Comparator.comparing(DateRawCount::getDate, Collections.reverseOrder()));

        return filledResult;
    }

}
