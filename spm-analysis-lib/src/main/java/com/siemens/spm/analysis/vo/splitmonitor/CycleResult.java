package com.siemens.spm.analysis.vo.splitmonitor;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

/**
 * Represents the result of a single cycle for a single phase.
 */
@Getter
@Setter
public class CycleResult {

    private final long phase;

    private final LocalDateTime greenStart;

    private final LocalDateTime greenEnd;

    private final LocalDateTime yellowStart;

    private final LocalDateTime yellowEnd;

    private final LocalDateTime redStart;

    private final LocalDateTime redEnd;

    private final double greenLength;

    private final double yellowLength;

    private final double redLength;

    private final double phaseLength;

    public CycleResult(long phase,
                       LocalDateTime greenStart,
                       LocalDateTime greenEnd,
                       LocalDateTime yellowStart,
                       LocalDateTime yellowEnd,
                       LocalDateTime redStart,
                       LocalDateTime redEnd) {
        this.phase = phase;
        this.greenStart = greenStart;
        this.greenEnd = greenEnd;
        this.yellowStart = yellowStart;
        this.yellowEnd = yellowEnd;
        this.redStart = redStart;
        this.redEnd = redEnd;

        // @formatter:off
        this.greenLength = getLength(greenStart, greenEnd);
        this.yellowLength = getLength(yellowStart, yellowEnd);
        this.redLength = getLength(redStart, redEnd);
        this.phaseLength = getLength(greenStart, redEnd);
        // @formatter:on
    }

    private static long getLength(LocalDateTime start, LocalDateTime end) {
        return start != null && end != null ? ChronoUnit.SECONDS.between(start, end) : 0;
    }

}