/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : PtChartBuilder.java
 * Project     : SPM Platform
 */
package com.siemens.spm.analysis.factory;

import com.siemens.spm.analysis.aggregator.PhaseSplitAggregator;
import com.siemens.spm.analysis.aggregator.PhaseTerminationAggregator;
import com.siemens.spm.analysis.util.DateTimeUtil;
import com.siemens.spm.analysis.vo.Plan;
import com.siemens.spm.analysis.vo.PtChartVO;
import com.siemens.spm.analysis.vo.PtPlanStatisticsVO;
import com.siemens.spm.analysis.vo.SkipPhaseEventVO;
import com.siemens.spm.analysis.vo.TerminatePhaseEventVO;
import com.siemens.spm.perflog.vo.IntersectionConfigVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;
import lombok.Getter;
import lombok.Setter;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;

public class PtChartBuilder extends PhaseChartBuilder {

    @Getter
    @Setter
    private class PtPlanStatistics {

        Plan plan;
        LocalDateTime fromTime;
        LocalDateTime toTime;
        int programmedSplit;
        PhaseTerminationAggregator ptAggr;
        PhaseSplitAggregator phaseSplitAggr;

        public PtPlanStatistics(int planNum, int phaseNum, LocalDateTime fromTime) {
            this.plan = new Plan(planNum);
            this.fromTime = fromTime;

            this.ptAggr = new PhaseTerminationAggregator(phaseNum, fromTime);
            this.phaseSplitAggr = new PhaseSplitAggregator(phaseNum);
        }

        /**
         * @param eventVO
         */
        public void putEvent(PerfLogEventVO eventVO) {
            ptAggr.putEvent(eventVO);
            phaseSplitAggr.putEvent(eventVO);
        }

    }

    private PtChartVO chartVO;
    private IntersectionConfigVO intConfigVO;

    private PtPlanStatistics currentPlanStat;
    private ArrayList<PtPlanStatistics> planStatList; // List of processed planStatistics

    private TerminatePhaseEventVO actualPtEventCandidate;

    /**
     * @param chartVO Chart object to be built, must not be null
     */
    public PtChartBuilder(PtChartVO chartVO) {
        super(chartVO.getPhase());
        this.chartVO = chartVO;
    }

    /**
     * Initialize states of builder. This method must be called at start of processing and before other processing
     * methods.
     */
    public void init() {
        currentPlanStat = newPlanStat(Plan.UNKNOWN_PLAN, chartVO.getFromTime());
        planStatList = new ArrayList<>();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void setIntersectionConfig(IntersectionConfigVO configVO) {
        this.intConfigVO = configVO;
    }

    @Override
    public void putEvent(PerfLogEventVO eventVO) {
        super.putEvent(eventVO);

        if (eventVO == null) {
            throw new IllegalArgumentException("eventVO must not be null");
        }

        if (!DateTimeUtil.eventDateTimeGuard(eventVO, chartVO.getFromTime())) {
            return; // Skip event with invalid datetime
        }

        switch (eventVO.getEvent()) {
            case COORD_PATTERN_CHANGE:
                putCoordPatternChange(eventVO);
                break;

            case PHASE_GAP_OUT, PHASE_MAX_OUT, PHASE_FORCE_OFF:
                putPhaseTermination(eventVO);
                break;

            case PHASE_INACTIVE:
                putActualPhaseTermination();
                break;

            case PHASE_SKIP:
                putPhaseSkip(eventVO);
                break;

            default:
                // Unused event
        }

        // Current plan stat should not be null here
        currentPlanStat.putEvent(eventVO);
    }

    private void putActualPhaseTermination() {
        if (actualPtEventCandidate != null) {
            chartVO.addActualPhaseTermination(actualPtEventCandidate);

            // Reset phase terminate actual event
            actualPtEventCandidate = null;
        }
    }

    /**
     * Finalize chart building and output to referenced chartVO
     */
    public void build() {
        // Close current currentBin if there is any data
        closeCurrentPlanStat(chartVO.getToTime());

        // Add plan statistics to chart
        for (PtPlanStatistics planStat : planStatList) {
            chartVO.addPlanStatistics(createPlanStatVO(planStat));
        }
    }

    private void putCoordPatternChange(PerfLogEventVO eventVO) {
        closeCurrentPlanStat(eventVO.getDateTime());

        // Start new planStatistics
        currentPlanStat = newPlanStat((int) eventVO.getParameter(), eventVO.getDateTime());
    }

    private void putPhaseTermination(PerfLogEventVO eventVO) {
        TerminatePhaseEventVO tpeVO = createTerminatePhaseEventVO(eventVO);
        chartVO.addPhaseTermination(tpeVO);

        // Add latest phase terminate event as actual phase terminate event candidate
        actualPtEventCandidate = tpeVO;
    }

    private void putPhaseSkip(PerfLogEventVO eventVO) {
        SkipPhaseEventVO speVO = new SkipPhaseEventVO(eventVO);
        chartVO.addPhaseSkip(speVO);
    }

    /**
     * Create phase termination from PerfLog event
     *
     * @param eventVO
     * @return
     */
    private TerminatePhaseEventVO createTerminatePhaseEventVO(PerfLogEventVO eventVO) {
        TerminatePhaseEventVO ptVO = new TerminatePhaseEventVO(eventVO);
        Duration duration = phase.getOnTime() != null ? Duration.between(phase.getOnTime(), eventVO.getDateTime())
                : Duration.ZERO;
        ptVO.setPhaseTime((int) duration.getSeconds());
        return ptVO;
    }

    private PtPlanStatistics newPlanStat(int plan, LocalDateTime fromTime) {
        PtPlanStatistics newPlanStat = new PtPlanStatistics(plan, chartVO.getPhase(), fromTime);

        if (plan == Plan.UNKNOWN_PLAN || plan == Plan.FREE_PLAN) {
            newPlanStat.setProgrammedSplit(0);
        } else {
            if (intConfigVO != null && intConfigVO.getPatternInfo() != null) {
                int[] splitTimesOfPattern = intConfigVO.getPatternInfo().getSplitTimeOfPattern(plan - 1);
                newPlanStat.setProgrammedSplit(splitTimesOfPattern[chartVO.getPhase() - 1]);
            }
        }
        return newPlanStat;
    }

    private void closeCurrentPlanStat(LocalDateTime toTime) {
        currentPlanStat.setToTime(toTime);
        planStatList.add(currentPlanStat);
        currentPlanStat = null; // Prevent reuse
    }

    /**
     * @param planStat
     * @return
     */
    private PtPlanStatisticsVO createPlanStatVO(PtPlanStatistics planStat) {
        return PtPlanStatisticsVO.builder()
                .planName(planStat.getPlan().getPlanName())
                .fromTime(planStat.getFromTime())
                .toTime(planStat.getToTime())
                .gapOut(planStat.getPtAggr().getGapOut())
                .maxOut(planStat.getPtAggr().getMaxOut())
                .forceOff(planStat.getPtAggr().getForceOff())
                .actualGapOut(planStat.getPtAggr().getActualGapOut())
                .actualMaxOut(planStat.getPtAggr().getActualMaxOut())
                .actualForceOff(planStat.getPtAggr().getActualForceOff())
                .programmedSplit(planStat.getProgrammedSplit())
                .totalPhaseSplit(planStat.getPhaseSplitAggr().getTotalPhaseSplit())
                .phaseSplitNum(planStat.getPhaseSplitAggr().getPhaseSplitNum())
                .build();
    }

}
