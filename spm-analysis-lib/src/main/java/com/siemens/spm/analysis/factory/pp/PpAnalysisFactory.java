package com.siemens.spm.analysis.factory.pp;

import com.siemens.spm.analysis.exception.AnalysisInitializationException;
import com.siemens.spm.analysis.exception.InvalidPerfLogException;
import com.siemens.spm.analysis.factory.AbstractAnalysisFactory;
import com.siemens.spm.analysis.vo.pp.PpAnalysisVO;
import com.siemens.spm.analysis.vo.pp.PpChartVO;
import com.siemens.spm.perflog.vo.PerfLogBundleVO;
import com.siemens.spm.perflog.vo.PerfLogChunkVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;
import com.siemens.spm.perflog.vo.PerfLogGapVO;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.function.Predicate;

/**
 * Factory class to create Preemption/Priority (PP) analysis
 *
 * <AUTHOR> Ng<PERSON>en - <EMAIL>
 */
public class PpAnalysisFactory extends AbstractAnalysisFactory<PpAnalysisVO, PpChartVO> {

    public PpAnalysisVO createAnalysis(LocalDateTime fromTime,
                                       LocalDateTime toTime,
                                       PerfLogBundleVO perfLogBundleVO,
                                       List<PerfLogGapVO> perfLogGapVOList)
            throws InvalidPerfLogException, AnalysisInitializationException {
        return createAnalysis(fromTime, toTime, perfLogBundleVO, perfLogGapVOList, PpAnalysisVO.class, PpChartVO.class);
    }

    @Override
    public void buildCharts(LocalDateTime fromTime,
                            LocalDateTime toTime,
                            PerfLogBundleVO perfLogBundleVO,
                            Class<PpChartVO> ppChartVOClass) {
        List<PerfLogChunkVO> chunkVOList = perfLogBundleVO.getPerfLogChunks();
        if (chunkVOList == null) {
            return;
        }

        List<PpChartBuilder<? extends PpChartVO>> chartBuilders = new ArrayList<>();

        // Build preemption detail chart builders
        List<Integer> preemptionNumbers = scanPreemptionNumbers(perfLogBundleVO);
        for (Integer number : preemptionNumbers) {
            PreemptionDetailChartBuilder chartBuilder = new PreemptionDetailChartBuilder(fromTime, toTime, number);
            chartBuilders.add(chartBuilder);
        }

        // Build priority(TSP) detail chart builders
        List<Integer> priorityNumbers = scanPriorityNumbers(perfLogBundleVO);
        for (Integer number : priorityNumbers) {
            PriorityDetailChartBuilder chartBuilder = new PriorityDetailChartBuilder(fromTime, toTime, number);
            chartBuilders.add(chartBuilder);
        }

        // Build Preemption/Priority request chart builder
        if (!preemptionNumbers.isEmpty() || !priorityNumbers.isEmpty()) {
            PpRequestChartBuilder ppRequestChartBuilder = new PpRequestChartBuilder(fromTime, toTime);
            chartBuilders.add(ppRequestChartBuilder);
        }

        // Do process chart builder to put event data and build chart data
        for (PpChartBuilder<? extends PpChartVO> chartBuilder : chartBuilders) {
            for (PerfLogChunkVO chunkVO : chunkVOList) {
                if (chunkVO != null && chunkVO.getPerfLogEvents() != null) {
                    chunkVO.getPerfLogEvents().forEach(chartBuilder::putEvent);
                }
            }

            PpChartVO chartVO = chartBuilder.build();
            analysisVO.addChart(chartVO);
        }
    }

    private List<Integer> scanPreemptionNumbers(PerfLogBundleVO perfLogBundleVO) {
        return scanParameters(perfLogBundleVO, PerfLogEventVO::isPreemptionEvent);
    }

    private List<Integer> scanPriorityNumbers(PerfLogBundleVO perfLogBundleVO) {
        return scanParameters(perfLogBundleVO, PerfLogEventVO::isPriorityEvent);
    }

    private List<Integer> scanParameters(PerfLogBundleVO perfLogBundleVO, Predicate<PerfLogEventVO> filter) {
        List<PerfLogChunkVO> chunkVOList = perfLogBundleVO.getPerfLogChunks();
        if (chunkVOList == null) {
            return List.of();
        }

        Set<Integer> numbers = new HashSet<>();
        for (PerfLogChunkVO chunkVO : chunkVOList) {
            if (chunkVO == null || chunkVO.getPerfLogEvents() == null) {
                continue;
            }

            numbers.addAll(
                    chunkVO.getPerfLogEvents()
                            .stream()
                            .filter(filter)
                            .map(PerfLogEventVO::getParameter)
                            .map(Long::intValue)
                            .toList()
            );
        }

        return numbers.stream()
                .sorted()
                .toList();
    }

}
