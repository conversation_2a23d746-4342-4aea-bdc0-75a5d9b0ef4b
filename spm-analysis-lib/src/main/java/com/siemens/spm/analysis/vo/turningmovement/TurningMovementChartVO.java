package com.siemens.spm.analysis.vo.turningmovement;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.analysis.vo.BaseChartVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@SuperBuilder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TurningMovementChartVO extends BaseChartVO {

    private static final long serialVersionUID = -5300752510632314707L;

    // TODO: Translate direction name
    @JsonProperty("direction")
    private String direction;

    // TODO: Translate movement name
    @JsonProperty("movements")
    private Map<String, Set<Integer>> detPerMovementMap;

    @JsonProperty("phase_numbers")
    private List<Integer> phaseNumbers;

    @JsonProperty("bins")
    private List<TurningMovementBinVO> binVOList;

    @JsonProperty("plan_statistics")
    private List<TurningMovementPlanStatVO> planStatVOList;

}
