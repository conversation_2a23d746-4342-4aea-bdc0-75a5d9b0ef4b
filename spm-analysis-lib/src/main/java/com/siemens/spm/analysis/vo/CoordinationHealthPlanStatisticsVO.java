package com.siemens.spm.analysis.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CoordinationHealthPlanStatisticsVO implements Serializable, DataBlockVO {

    private static final long serialVersionUID = -5408071965521579553L;

    @JsonProperty("from_time")
    protected LocalDateTime fromTime;

    @JsonProperty("to_time")
    protected LocalDateTime toTime;

    @JsonIgnore
    private Plan plan;

    @JsonIgnore
    protected int insteps;

    @JsonIgnore
    protected int transitions;

    @JsonProperty("cycle")
    protected int cycle;

    @JsonProperty("plan")
    public String getPlanName() {
        return plan == null ? null : plan.getPlanName();
    }

    @JsonProperty("transition_percent")
    public Double perChaTransition() {
        return cycle == 0 ? 0.0 : 100.0 * transitions / cycle;
    }

    @JsonProperty("instep_percent")
    public Double perChaInstep() {
        return cycle == 0 ? 0.0 : 100.0 * insteps / cycle;
    }

    public void addInStep(int number) {
        insteps += number;
        cycle += number;
    }

    public void addTransition(int number) {
        transitions += number;
        cycle += number;
    }

    public CoordinationHealthPlanStatisticsVO(int planNum, LocalDateTime fromTime) {
        this.fromTime = fromTime;
        this.plan = new Plan(planNum);
    }

}
