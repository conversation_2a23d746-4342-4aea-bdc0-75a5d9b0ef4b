package com.siemens.spm.analysis.factory;

import java.lang.reflect.InvocationTargetException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import org.springframework.data.util.Pair;

import com.siemens.spm.analysis.exception.AnalysisInitializationException;
import com.siemens.spm.analysis.exception.ChartBuilderInitializationException;
import com.siemens.spm.analysis.exception.ChartInitializationException;
import com.siemens.spm.analysis.exception.InvalidPerfLogException;
import com.siemens.spm.analysis.exception.MissingDetectorException;
import com.siemens.spm.analysis.factory.topology.DetTypeUsed;
import com.siemens.spm.analysis.util.IntersectionConfigUtils;
import com.siemens.spm.analysis.vo.AbstractAnalysisVO;
import com.siemens.spm.analysis.vo.BaseChartVO;
import com.siemens.spm.perflog.vo.DetectorVO.DetectorType;
import com.siemens.spm.perflog.vo.IntersectionConfigVO;
import com.siemens.spm.perflog.vo.PerfLogBundleVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;
import com.siemens.spm.perflog.vo.PerfLogGapVO;
import com.siemens.spm.perflog.vo.PhaseVO;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class AbstractAnalysisFactory<T_ANALYSIS extends AbstractAnalysisVO<T_CHART>, T_CHART extends BaseChartVO> {

    /**
     * specified detector types that are used for analysis
     */
    @Getter
    protected final List<DetectorType> usedDetTypes;
    
    @Getter
    protected final List<DetectorType> optionalDetTypes;

    @Getter
    protected final DetTypeUsed detTypeUsed;

    @Getter
    protected DetEventFilter detEventFilter;

    /**
     * detector type -> [detector number -> list of phases]
     * <p>
     * This map is used to store the mapping between detector type, detector number
     * and the list of phases that are assigned to that detector.
     */
    protected Map<DetectorType, Map<Integer, List<PhaseVO>>> detToPhaseListMap = Map.of();

    protected T_ANALYSIS analysisVO = null;

    protected AbstractAnalysisFactory() {
        detTypeUsed = getClass().getAnnotation(DetTypeUsed.class);
        
        Pair<List<DetectorType>, List<DetectorType>> detTypes 
            = DetEventFilter.filterDetTypes(detTypeUsed);

        usedDetTypes = detTypes.getFirst();
        optionalDetTypes = detTypes.getSecond();
    }
    
    /**
     * Create analysis from perflog event
     *
     * @param fromTime         start time to create analysis
     * @param toTime           end time to create analysis
     * @param perfLogBundleVO  {@link PerfLogBundleVO} perflog bundle data
     * @param perfLogGapVOList List of perflog gap
     * @param analysisClass    class of analysis
     * @param chartClass       class of chart
     * @return {@code T_ANALYSIS}
     * @throws InvalidPerfLogException         if perflog data is invalid
     * @throws AnalysisInitializationException if got error while creating analysis
     * @throws MissingDetectorException if required detector type(s) are missing
     */
    public T_ANALYSIS createAnalysis(LocalDateTime fromTime,
                                     LocalDateTime toTime,
                                     PerfLogBundleVO perfLogBundleVO,
                                     List<PerfLogGapVO> perfLogGapVOList,
                                     Class<T_ANALYSIS> analysisClass,
                                     Class<T_CHART> chartClass)
            throws InvalidPerfLogException, AnalysisInitializationException, MissingDetectorException {
        try {
            analysisVO = analysisClass.getDeclaredConstructor().newInstance();
        } catch (InstantiationException | IllegalAccessException | InvocationTargetException |
                 NoSuchMethodException e) {
            log.error("Error when creating instance for analysisVO class", e);
            throw new AnalysisInitializationException(e);
        }

        buildBase(fromTime, toTime, perfLogBundleVO, perfLogGapVOList);

        try {
            buildCharts(fromTime, toTime, perfLogBundleVO, chartClass);
        } catch (ChartBuilderInitializationException | ChartInitializationException e) {
            log.error("Error when build charts for analysis", e);
            throw new AnalysisInitializationException(e);
        }

        return analysisVO;
    }

    /**
     * Implement this method by calling {@link #createAnalysis(LocalDateTime, LocalDateTime, PerfLogBundleVO, List, Class, Class)}
     *
     * @param fromTime         start time to create analysis
     * @param toTime           end time to create analysis
     * @param perfLogBundleVO  {@link PerfLogBundleVO} perflog bundle data
     * @param perfLogGapVOList List of perflog gap
     * @return {@code T_ANALYSIS}
     * @throws InvalidPerfLogException         if perflog data is invalid
     * @throws AnalysisInitializationException if got error while creating analysis
     */
    public abstract T_ANALYSIS createAnalysis(LocalDateTime fromTime,
                                              LocalDateTime toTime,
                                              PerfLogBundleVO perfLogBundleVO,
                                              List<PerfLogGapVO> perfLogGapVOList)
            throws InvalidPerfLogException, AnalysisInitializationException;

    /**
     * Set base fields of analysis: intUUID, intName, fromTime, toTime
     *
     * @param fromTime
     * @param toTime
     * @param perfLogBundleVO
     * @param perfLogGapVOList
     * @throws InvalidPerfLogException
     */
    protected void buildBase(LocalDateTime fromTime,
                             LocalDateTime toTime,
                             PerfLogBundleVO perfLogBundleVO,
                             List<PerfLogGapVO> perfLogGapVOList)
            throws InvalidPerfLogException, MissingDetectorException {
        // UUID
        analysisVO.setIntUUID(perfLogBundleVO.getIntUUID());

        try {
            Map<String, IntersectionConfigVO> intConfigVOMap = perfLogBundleVO.getIntConfigs();
            // Get intersection name from any config to set for analysis(here is first config)
            Map.Entry<String, IntersectionConfigVO> entry = intConfigVOMap.entrySet().iterator().next();
            analysisVO.setIntName(entry.getValue().getIntInfo().getName());

            // Detect Approach config
            boolean isFullyApproach = true;
            for (IntersectionConfigVO intConfigVO : intConfigVOMap.values()) {
                if (IntersectionConfigUtils.isEmptyApproachConfig(intConfigVO)) {
                    isFullyApproach = false;
                    break;
                }
            }
            analysisVO.setIsFullyApproach(isFullyApproach);
            
            if (detTypeUsed != null && detTypeUsed.value() != null && detTypeUsed.value().length > 0) {
                // there is no history of intersection topology
                // all intersection configs refer to the latest intersection topology
                detEventFilter = DetEventFilter.createDetEventFilter(detTypeUsed, entry.getValue());
            }

        } catch (MissingDetectorException e) {
            log.error(e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            throw new InvalidPerfLogException(e);
        }

        // fromTime and toTime
        analysisVO.setFromTime(fromTime);
        analysisVO.setToTime(toTime);

        analysisVO.setPerfLogGapList(perfLogGapVOList);
    }

    /**
     * If there are any required detector types, check if the event is valid for further processing.
     * 
     * @param eventVO
     * @return true if the given event is detector on/off event and (1) no detector type is required or (2) there are phases assigned to the detector. Otherwise, false.
     */
    protected boolean isValidDetEventForProcessing(PerfLogEventVO eventVO) {
        return detEventFilter == null || detEventFilter.isValid(eventVO);  
    }

    protected abstract void buildCharts(LocalDateTime fromTime,
                                        LocalDateTime toTime,
                                        PerfLogBundleVO perfLogBundleVO,
                                        Class<T_CHART> chartClass)
            throws InvalidPerfLogException, ChartBuilderInitializationException, ChartInitializationException;

}
