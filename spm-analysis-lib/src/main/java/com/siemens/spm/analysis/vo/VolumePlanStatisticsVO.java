package com.siemens.spm.analysis.vo;

import java.time.Duration;
import java.time.LocalDateTime;

public class VolumePlanStatisticsVO extends AbstractPlanStatVO {

    private static final long serialVersionUID = -7630668940975253815L;
    private Duration duration;

    public VolumePlanStatisticsVO(Integer phaseNum, LocalDateTime fromTime) {
        super(fromTime, fromTime.plusHours(1));
        this.duration = Duration.ZERO;
        this.plan = new Plan(phaseNum);
    }

    @Override
    public void addTime(Duration timeDuration) {
        duration = duration.plus(timeDuration);
    }
}
