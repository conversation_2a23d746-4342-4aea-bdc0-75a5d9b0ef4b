/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : DataBlockVO.java
 * Project     : spm-analysis-service
 */
package com.siemens.spm.analysis.domain;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

@Getter
@Setter
@ToString
public abstract class AbstractDataBlock {

    protected AbstractDataBlock() {
    }

    protected LocalDateTime fromTime;
    protected LocalDateTime toTime;

    protected AbstractDataBlock(LocalDateTime fromTime, LocalDateTime toTime) {
        super();
        if (fromTime == null || toTime == null || !fromTime.isBefore(toTime))
            throw new IllegalArgumentException();

        this.fromTime = fromTime;
        this.toTime = toTime;
    }

}
