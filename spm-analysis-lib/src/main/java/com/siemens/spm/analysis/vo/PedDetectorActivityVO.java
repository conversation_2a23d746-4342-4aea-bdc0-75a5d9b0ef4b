package com.siemens.spm.analysis.vo;

import com.siemens.spm.perflog.vo.PerfLogEventVO;

public class PedDetectorActivityVO extends PhaseEventVO {

    private static final long serialVersionUID = -6723101845060327032L;

    public PedDetectorActivityVO(PerfLogEventVO perfLogEventVO) {
        super(perfLogEventVO);
        if (perfLogEventVO.getEvent() != PerfLogEventVO.Event.PED_DETECTOR_ON
                && perfLogEventVO.getEvent() != PerfLogEventVO.Event.DETECTOR_ON)
            throw new IllegalArgumentException("Unexpected event " + perfLogEventVO.getEvent());
    }

}
