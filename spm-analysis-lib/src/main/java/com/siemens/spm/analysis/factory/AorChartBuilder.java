/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AorChartBuilder.java
 * Project     : SPM Platform
 */
package com.siemens.spm.analysis.factory;

import com.siemens.spm.analysis.aggregator.PhaseCallAggregator;
import com.siemens.spm.analysis.aggregator.PlanStatAggregator;
import com.siemens.spm.analysis.domain.PhaseColor;
import com.siemens.spm.analysis.util.EventValidatorUtils;
import com.siemens.spm.analysis.vo.AorBinVO;
import com.siemens.spm.analysis.vo.AorChartVO;
import com.siemens.spm.analysis.vo.AorPlanStatisticsVO;
import com.siemens.spm.analysis.vo.VolumeBinVO;
import com.siemens.spm.perflog.vo.DetectorInfoVO;
import com.siemens.spm.perflog.vo.IntersectionConfigVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class AorChartBuilder extends PhaseChartBuilder {

    private AorChartVO chartVO;

    private PhaseCallAggregator volumeAggregator;

    private PlanStatAggregator<AorPlanStatisticsVO> planStatAggregator;

    /**
     * DO NOT remove this constructor. This constructor used to create chart builder object via reflection
     *
     * @param chartVO Chart object to be built, must not be null
     * @param binSize binSize in {@link Integer}
     */
    public AorChartBuilder(AorChartVO chartVO, Integer binSize) {
        super(chartVO.getPhase());
        init(chartVO, binSize, null);
    }

    /**
     * @param chartVO       Chart object to be built, must not be null
     * @param binSize       Volume bin size in seconds from {@link AorBinVO}
     * @param detCallPhases Detector configuration from {@link DetectorInfoVO}
     */
    public AorChartBuilder(AorChartVO chartVO, int binSize, int[][] detCallPhases) {
        super(chartVO.getPhase());
        init(chartVO, binSize, detCallPhases);
    }

    /**
     * Initialize states of builder. This method must be called at start of processing and before other processing
     * methods.
     *
     * @param chartVO       {@code AogChartVO}
     * @param binSize       size of bin
     * @param detCallPhases Detector configuration from {@link DetectorInfoVO}
     */
    private void init(AorChartVO chartVO, int binSize, int[][] detCallPhases) {
        this.chartVO = chartVO;

        this.volumeAggregator = new PhaseCallAggregator(chartVO.getPhase(), chartVO.getFromTime(), binSize,
                detCallPhases);
        this.planStatAggregator = new PlanStatAggregator<>(chartVO.getPhase(), PhaseColor.RED,
                AorPlanStatisticsVO.class, chartVO.getFromTime(), chartVO.getToTime());
        this.planStatAggregator.putDetCallPhases(detCallPhases);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void setIntersectionConfig(IntersectionConfigVO configVO) {
        volumeAggregator.setConfig(configVO);
        planStatAggregator.setConfig(configVO);
    }

    @Override
    public void putEvent(PerfLogEventVO eventVO) {
        if (!EventValidatorUtils.isCandidateInRange(eventVO, chartVO.getFromTime(), chartVO.getToTime())) {
            return;
        }

        super.putEvent(eventVO);

        volumeAggregator.putEvent(eventVO);
        planStatAggregator.putEvent(eventVO);
    }

    /**
     * Finalize chart building and output to referenced chartVO
     */
    @Override
    public void build() {
        volumeAggregator.fillEndBins(chartVO.getToTime());
        chartVO.setAorBins(createAorVolumeBinList(volumeAggregator.getBinList()));

        // Add plan statistics to chart
        List<AorPlanStatisticsVO> aorPlanStatisticsVOList = planStatAggregator.getAggregatedPlanStat();
        chartVO.addListPlanStatistics(aorPlanStatisticsVOList);

        // Overall statistics
        chartVO.setTotalDetectorHits(volumeAggregator.getTotalPhaseCalls());
        chartVO.setTotalAorHits(volumeAggregator.getTotalAor());
        chartVO.setTotalAogHits(volumeAggregator.getTotalAor());

        // TODO: Remove unused Aog
    }

    /**
     * Create List of {@link AorBinVO} from list of {@link VolumeBinVO}
     *
     * @param volumeBinList list of {@link VolumeBinVO}
     * @return {@code List<AorBinVO>}
     */
    private List<AorBinVO> createAorVolumeBinList(List<VolumeBinVO> volumeBinList) {
        if (volumeBinList == null) {
            return Collections.emptyList();
        }

        List<AorBinVO> aorBinList = new ArrayList<>();
        for (VolumeBinVO volBin : volumeBinList) {
            if (volBin == null) {
                continue;
            }

            aorBinList.add(new AorBinVO(volBin));
        }
        return aorBinList;
    }

}
