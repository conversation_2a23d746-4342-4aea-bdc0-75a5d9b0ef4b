package com.siemens.spm.analysis.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.perflog.vo.PerfLogEventVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class YellowTrapEventVO implements Serializable {

    private static final long serialVersionUID = 9126785448939786109L;

    @JsonProperty("yellow_trap_event")
    private PerfLogEventVO yellowTrapEvent;

    @JsonProperty("left_turn_phase_events")
    private List<PerfLogEventVO> leftTurnPhaseEvent;

    @JsonProperty("left_turn_phase_num")
    private Long leftTurnPhaseNum;

    @JsonProperty("opposing_through_phase_events")
    private List<PerfLogEventVO> opposingThroughPhaseEvent;

    @JsonProperty("opposing_through_phase_num")
    private Long opposingThroughPhaseNum;

}
