package com.siemens.spm.analysis.vo.moe;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import static com.siemens.spm.analysis.util.Constants.PROGRAMMED_RED_LENGTH;
import static com.siemens.spm.analysis.util.Constants.PROGRAMMED_YELLOW_LENGTH;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProgrammedVehiclePhaseLengthVO extends BaseAvgSignalLengthVO implements Serializable {

    @Serial private static final long serialVersionUID = -2462657755944463370L;

    @JsonIgnore
    private List<Integer> greenLengthList;

    @JsonIgnore
    public void addGreenLength(Integer greenLength) {
        if (greenLengthList == null) {
            greenLengthList = new ArrayList<>();
        }
        greenLengthList.add(greenLength);
    }

    @JsonIgnore
    public void addGreenLengthList(List<Integer> greenLength) {
        if (greenLengthList == null) {
            greenLengthList = new ArrayList<>();
        }
        greenLengthList.addAll(greenLength);
    }

    @JsonIgnore
    private void calAvgGreen() {
        if (greenLengthList == null) {
            return;
        }
        this.averageGreen = greenLengthList.stream().reduce(0, Integer::sum) / (greenLengthList.size() * 1.0);
    }

    @JsonIgnore
    public void calAndFillVO() {
        if (greenLengthList == null) {
            return;
        }
        calAvgGreen();
        this.averageYellow = (double) PROGRAMMED_YELLOW_LENGTH;
        this.averageRed = (double) PROGRAMMED_RED_LENGTH;
    }
}
