/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AorPlanStatisticsVO.java
 * Project     : SPM Platform
 */
package com.siemens.spm.analysis.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PtPlanStatisticsVO implements Serializable, DataBlockVO {

    private static final long serialVersionUID = -1037595367139604181L;

    @JsonProperty("plan")
    protected String planName;

    @JsonProperty("from_time")
    protected LocalDateTime fromTime;

    @JsonProperty("to_time")
    protected LocalDateTime toTime;

    @JsonProperty("gap_out")
    protected int gapOut;

    @JsonProperty("max_out")
    protected int maxOut;

    @JsonProperty("force_off")
    protected int forceOff;

    @JsonProperty("actual_gap_out")
    protected int actualGapOut;

    @JsonProperty("actual_max_out")
    protected int actualMaxOut;

    @JsonProperty("actual_force_off")
    protected int actualForceOff;

    @JsonProperty("programmed_split")
    protected int programmedSplit;

    @JsonIgnore
    protected long totalPhaseSplit; // Total green time of phase during plan

    @JsonIgnore
    protected long phaseSplitNum; // Number of phase green terminations

    public PtPlanStatisticsVO(String planName, LocalDateTime fromTime) {
        this.planName = planName;
        this.fromTime = fromTime;

        gapOut = 0;
        maxOut = 0;
        forceOff = 0;
        actualGapOut = 0;
        actualMaxOut = 0;
        actualForceOff = 0;
        programmedSplit = 0;
        totalPhaseSplit = 0;
        phaseSplitNum = 0;
    }

    @JsonProperty("gap_out_percent")
    public double getGapOutPercent() {
        if (totalPt() == 0)
            return 0;
        return (double) gapOut / (double) totalPt() * 100;
    }

    @JsonProperty("max_out_percent")
    public double getMaxOutPercent() {
        if (totalPt() == 0)
            return 0;
        return (double) maxOut / (double) totalPt() * 100;
    }

    @JsonProperty("force_off_percent")
    public double getForceOffPercent() {
        if (totalPt() == 0)
            return 0;
        return (double) forceOff / (double) totalPt() * 100;
    }

    @JsonProperty("actual_gap_out_percent")
    public double getActualGapOutPercent() {
        if (totalActualPt() == 0)
            return 0;
        return (double) actualGapOut / (double) totalActualPt() * 100;
    }

    @JsonProperty("actual_max_out_percent")
    public double getActualMaxOutPercent() {
        if (totalActualPt() == 0)
            return 0;
        return (double) actualMaxOut / (double) totalActualPt() * 100;
    }

    @JsonProperty("actual_force_off_percent")
    public double getActualForceOffPercent() {
        if (totalActualPt() == 0)
            return 0;
        return (double) actualForceOff / (double) totalActualPt() * 100;
    }

    @JsonProperty("average_split")
    public double getAverageSplit() {
        if (phaseSplitNum == 0)
            return 0; // Avoid division by zero
        return (double) totalPhaseSplit / (double) phaseSplitNum;
    }

    protected int totalPt() {
        return gapOut + maxOut + forceOff;
    }

    protected int totalActualPt() {
        return actualGapOut + actualMaxOut + actualForceOff;
    }

}
