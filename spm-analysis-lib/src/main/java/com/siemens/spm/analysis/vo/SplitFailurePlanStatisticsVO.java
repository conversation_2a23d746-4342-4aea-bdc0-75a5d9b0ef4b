package com.siemens.spm.analysis.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SplitFailurePlanStatisticsVO implements Serializable, DataBlockVO {

    private static final long serialVersionUID = -6601396320802401891L;

    @JsonProperty("from_time")
    protected LocalDateTime fromTime;

    @JsonProperty("to_time")
    protected LocalDateTime toTime;

    @JsonIgnore
    private Plan plan;

    @JsonProperty("plan")
    public String getPlanName() {
        return plan.getPlanName();
    }

    @JsonProperty("SF")
    protected int totalSF;

    @JsonProperty("EGU")
    protected int totalEGU;

    @JsonProperty("PC")
    protected int totalPC;

    @JsonProperty("EC")
    protected int totalEC;

    public SplitFailurePlanStatisticsVO(int planNumber, LocalDateTime fromTime) {
        this.fromTime = fromTime;
        this.plan = new Plan(planNumber);
    }
}
