package com.siemens.spm.analysis.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MultipleDayAnalysisVO<T_CHART extends BaseChartVO> {

    @JsonProperty("int_uuid")
    private String intUUID;

    @JsonProperty("int_name")
    private String intName;

    @JsonProperty("from_time")
    private LocalTime fromTime;

    @JsonProperty("to_time")
    private LocalTime toTime;

    @JsonProperty("from_date")
    private LocalDate fromDate;

    @JsonProperty("to_date")
    private LocalDate toDate;

    @JsonProperty("days")
    private List<DayAnalysisVO<T_CHART>> days;

    public void addAnalysisDay(DayAnalysisVO<T_CHART> dayAnalysisVO) {
        if (days == null) {
            days = new ArrayList<>();
        }

        days.add(dayAnalysisVO);
    }

}
