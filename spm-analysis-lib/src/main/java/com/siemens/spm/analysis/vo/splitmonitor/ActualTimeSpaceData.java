package com.siemens.spm.analysis.vo.splitmonitor;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Set;
import java.util.TreeSet;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
@AllArgsConstructor
public class ActualTimeSpaceData {

    @JsonProperty("start_time")
    private LocalDateTime startTime;

    @JsonProperty("end_time")
    private LocalDateTime endTime;

    @JsonProperty("duration")
    private Integer duration;

    @JsonProperty("plan")
    private int plan;

    @JsonProperty("ring1")
    private RingInformationVO ring1;

    @JsonProperty("ring2")
    private RingInformationVO ring2;

    @JsonProperty("events")
    private Set<TimeLineEventVO> timeLineEvents;

    @JsonIgnore
    private int noSkippedPhase;

    @JsonIgnore
    private double skippedPhaseDuration;

    public ActualTimeSpaceData() {
        timeLineEvents = new TreeSet<>();
        ring1 = RingInformationVO.builder().ringNo(1).lights(new ArrayList<>()).skippedPhases(new ArrayList<>())
                .build();
        ring2 = RingInformationVO.builder().ringNo(2).lights(new ArrayList<>()).skippedPhases(new ArrayList<>())
                .build();
    }

    @JsonIgnore
    private boolean doneRing1 = false;
    @JsonIgnore
    private boolean doneRing2 = false;

    @JsonIgnore
    private LocalDateTime endTimeLastCycle;

    @JsonIgnore
    public boolean isEmpty() {
        return ring1.isEmpty() || ring2.isEmpty();
    }

    @JsonIgnore
    public boolean isAllEmpty() {
        return ring1.isEmpty() && ring2.isEmpty();
    }

    @JsonIgnore
    public boolean isNotAllEmpty() {
        return !isAllEmpty();
    }

}
