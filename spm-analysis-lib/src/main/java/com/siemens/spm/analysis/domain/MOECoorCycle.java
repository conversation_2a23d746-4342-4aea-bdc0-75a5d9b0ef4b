package com.siemens.spm.analysis.domain;

import com.siemens.spm.analysis.util.DateTimeUtil;
import com.siemens.spm.perflog.vo.PerfLogEventVO;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.time.LocalDateTime;

@Slf4j
public class MOECoorCycle {

    public MOECoorCycle(LocalDateTime beginTime, int phaseNum) {
        initCycle(beginTime, phaseNum);
    }

    private int phaseNum;

    @Getter
    private LocalDateTime beginCycleTime;
    @Getter
    private LocalDateTime endCycleTime;

    private LocalDateTime beginGreenTime;
    private LocalDateTime beginYellowTime;
    private LocalDateTime beginRedTime;

    private LocalDateTime endGreenTime;
    private LocalDateTime endYellowTime;
    private LocalDateTime endRedTime;

    private boolean isCycleCompleted;

    public void updateState(PerfLogEventVO eventVO) {
        if (eventVO == null || !eventVO.isPhaseEvent()) {
            throw new IllegalArgumentException();
        }

        // Skip not relevant event
        if (phaseNum != Phase.UNSPECIFIED_PHASE_NUM && eventVO.getParameter() != phaseNum) {
            return;
        }

        switch (eventVO.getEvent()) {
            case PHASE_BEGIN_GREEN -> initCycle(eventVO.getDateTime(), (int) eventVO.getParameter());
            case PHASE_GREEN_TERMINATION -> endGreenTime = eventVO.getDateTime();
            case PHASE_BEGIN_YELLOW_CLEARANCE -> beginYellowTime = eventVO.getDateTime();
            case PHASE_END_YELLOW_CLEARANCE -> endYellowTime = eventVO.getDateTime();
            case PHASE_BEGIN_RED_CLEARANCE -> beginRedTime = eventVO.getDateTime();
            case PHASE_END_RED_CLEARANCE -> endRedTime = eventVO.getDateTime();
            default -> {
                // Do nothing
            }
        }
    }

    private void initCycle(LocalDateTime beginTime, int phaseNum) {
        this.phaseNum = phaseNum;

        beginCycleTime = beginTime;
        endCycleTime = null;

        beginGreenTime = beginTime;
        beginYellowTime = null;
        beginRedTime = null;

        endGreenTime = null;
        endYellowTime = null;
        endRedTime = null;

        isCycleCompleted = false;
    }

    public Duration greenDuration() {
        if (isCycleCompleted) {
            return DateTimeUtil.getDuration(beginGreenTime, endGreenTime);
        } else {
            return Duration.ZERO;
        }
    }

    public Duration yellowDuration() {
        if (isCycleCompleted) {
            return DateTimeUtil.getDuration(beginYellowTime, endYellowTime);
        } else {
            return Duration.ZERO;
        }
    }

    public Duration redDuration() {
        if (isCycleCompleted) {
            return DateTimeUtil.getDuration(beginRedTime, endRedTime);
        } else {
            return Duration.ZERO;
        }
    }

    public Duration cycleDuration(boolean withDelay) {
        if (!isCycleCompleted) {
            return Duration.ZERO;
        }

        if (withDelay) {
            return DateTimeUtil.getDuration(beginCycleTime, endCycleTime);
        } else {
            return greenDuration().plus(yellowDuration()).plus(redDuration());
        }
    }

    public void endCycle(LocalDateTime endTime) {
        endCycleTime = endTime;
        isCycleCompleted = true;

        if (beginGreenTime == null) {
            beginGreenTime = endYellowTime;
        }

        if (endGreenTime == null) {
            endGreenTime = beginCycleTime;
        }

        if (beginYellowTime == null) {
            beginYellowTime = beginCycleTime;
        }

        if (endYellowTime == null) {
            endYellowTime = beginCycleTime;
        }

        if (beginRedTime == null) {
            beginRedTime = endYellowTime;
        }

        if (endRedTime == null) {
            endRedTime = beginCycleTime;
        }
    }

}
