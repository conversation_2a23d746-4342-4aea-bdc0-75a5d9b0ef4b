package com.siemens.spm.analysis.util.appdelay;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Queue;

import com.siemens.spm.analysis.domain.Phase;
import com.siemens.spm.analysis.util.AppDelayEstimationResult;
import com.siemens.spm.analysis.util.AppDelayUtil;
import com.siemens.spm.analysis.util.DateTimeUtil;
import com.siemens.spm.analysis.util.IntersectionConfigUtils;
import com.siemens.spm.analysis.vo.AppDelayPlanStatisticsVO;
import com.siemens.spm.analysis.vo.AppDelayVehicleVO;
import com.siemens.spm.perflog.domaintype.ApproachMovement;
import com.siemens.spm.perflog.vo.ApproachVO;
import com.siemens.spm.perflog.vo.IntersectionConfigVO;
import com.siemens.spm.perflog.vo.LaneVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;
import lombok.extern.slf4j.Slf4j;

/**
 * Calculation class for approach delay if 1 detector is available at the approach.
 */
@Slf4j
public class SingleDetectorAppDelayCalculator extends AppDelayCalculator {

    private Map<Integer, Double> timeSimulates;
    private final List<PerfLogEventVO> waitedEventInPlan;
    private final Queue<PerfLogEventVO> detectorDeactivationEvents;

    private int cycleVolume;
    private double cycleGreenDuration;
    private boolean cycleDetectorOccupiedDuringGreen = false;
    private double cycleSpeedSum;

    private LocalDateTime greenBeginTime;

    private double saturationFlow;

    private final int detectorId;

    public SingleDetectorAppDelayCalculator(LocalDateTime fromTime, int binSize, int laneIdx, int detectorId) {
        super(fromTime, binSize, laneIdx);

        this.timeSimulates = new HashMap<>();
        this.waitedEventInPlan = new ArrayList<>();
        this.detectorDeactivationEvents = new ArrayDeque<>();
        this.greenBeginTime = null;
        this.lastGreenBeginTime = null;

        this.detectorId = detectorId;
    }

    @Override
    public void setConfig(IntersectionConfigVO configVO, Phase phase) {
        ApproachVO approachVO = AppDelayUtil.identifyApproach(configVO, phase);
        if (approachVO == null) {
            log.debug("No approach found for phase {} Using default saturation flow.", phase.getPhaseNum());
            return;
        }
        // check if approach speed is set
        if (Double.isNaN(approachVO.getApproachSpeed())) {
            timeSimulates = new HashMap<>();
            log.debug("No speed limit set.");

        } else {
            // Set time simulate for advance detector
            timeSimulates = IntersectionConfigUtils.identifyAdvanceDetectorProjectedTime(configVO, phase);
            speedLimitMph = approachVO.getApproachSpeed();
        }

        LaneVO lane = AppDelayUtil.identifyLane(approachVO, phase, laneIdx);

        double saturation = configVO.getSaturation(ApproachMovement.resolve(lane.getMovement()));
        this.saturationFlow = saturation / ChronoUnit.HOURS.getDuration().toSeconds();
    }

    @Override
    public void putEvent(PerfLogEventVO eventVO) {

        fillBins(eventVO.getDateTime());

        switch (eventVO.getEvent()) {
        case DETECTOR_ON, DETECTOR_OFF, PHASE_BEGIN_GREEN:
            handleEventPlan(eventVO);
            break;
        case PHASE_BEGIN_YELLOW_CLEARANCE:
            handleEventPlan(eventVO);
            calculateDelayForCycle(eventVO.getDateTime());
            break;

        case COORD_PATTERN_CHANGE:
            putAppDelayPatternChange(eventVO);
            break;
        default:
            break;
        }
    }

    /**
     * Calculates the delay in a cycle (interval between 2 green ends/yellow
     * starts).
     *
     * @param toTime end time of interval
     */
    @Override
    public void calculateDelayForCycle(LocalDateTime toTime) {

        // remove waiting detector activations if possible
        removeWaitingEvents(toTime);

        // check if it is green right now
        if (greenBeginTime != null) {
            // if so take green duration in this interval into consideration
            cycleGreenDuration += Duration.between(greenBeginTime, toTime).getSeconds();
            greenBeginTime = toTime;
        }

        double avgSpeed = cycleSpeedSum / cycleVolume;
        boolean oversaturation = cycleVolume > 0 && avgSpeed / speedLimitMph < 0.5;

        // calculate cycle duration
        double duration = DateTimeUtil.calculateDurationTime(lastPhaseYellowBegin, toTime);
        // estimate app delay using webster formula
        AppDelayEstimationResult appDelayEstimationResult = AppDelayUtil
                .websterAppDelayEstimation(cycleVolume, duration,
                        cycleGreenDuration, OVERSATURATION_BORDER, saturationFlow);

        // calculate how much of cycle is part of the current plan interval
        double currentPlanDurationShare = calculateCurrentIntervalShare(toTime, currentPlanStat.getFromTime());
        double lastPlanDurationShare = 1 - currentPlanDurationShare;

        // create a new delay bin for this cycle and store it
        // the share of the cycle being in the current plan is taken into consideration
        if (currentPlanDurationShare > 0) {

            storePartOfCycleInCurrentInterval(toTime, planCycleDelays, currentPlanDurationShare,
                    appDelayEstimationResult.getDelay(), cycleVolume,
                    appDelayEstimationResult.isOversaturated() || oversaturation, cycleDetectorOccupiedDuringGreen);
        }

        // if part of the cycle is in the last plan, add it there
        if (lastPlanDurationShare > 0 && !planStats.isEmpty()) {

            int vehicleCount = (int) (cycleVolume * lastPlanDurationShare);
            AppDelayPlanStatisticsVO lastPlan = planStats.get(planStats.size() - 1);
            lastPlan.setTotalTimeDelay(
                    lastPlan.getTotalTimeDelay()
                            + appDelayEstimationResult.getDelay() * vehicleCount);
            lastPlan.setTotalVolume(lastPlan.getTotalVolume() + vehicleCount);
        }

        // calculate how much of cycle is part of the current bin interval
        double currentBinDurationShare = calculateCurrentIntervalShare(toTime, currentDelayVehicleBin.getFromTime());
        double lastBinDurationShare = 1 - currentBinDurationShare;

        // create a new delay bin for this cycle and store it
        // the share of the cycle being in the current bin is taken into consideration
        if (currentBinDurationShare > 0) {

            storePartOfCycleInCurrentInterval(toTime, binCycleDelays, currentBinDurationShare,
                    appDelayEstimationResult.getDelay(), cycleVolume,
                    appDelayEstimationResult.isOversaturated() || oversaturation, cycleDetectorOccupiedDuringGreen);
        }

        // if part of the cycle is in the last bin, add it there
        if (lastBinDurationShare > 0) {

            int vehicleCount = (int) (cycleVolume * lastBinDurationShare);

            AppDelayVehicleVO lastBin = binVehicleList.get(binVehicleList.size() - 2);
            lastBin.setDelayDuration(
                    lastBin.getDelayDuration()
                            + appDelayEstimationResult.getDelay() * vehicleCount);
            lastBin.setBinVolume(lastBin.getBinVolume() + vehicleCount);
        }

        // reset cycle data
        cycleVolume = 0;
        cycleGreenDuration = 0;
        cycleSpeedSum = 0;
        cycleDetectorOccupiedDuringGreen = false;
        lastPhaseYellowBegin = toTime;
    }

    /**
     * check events until end_time of current plan statistic. Events eligible will
     * be calculated
     *
     * @param endTime time up until events can be removed from waiting list
     */
    private void removeWaitingEvents(LocalDateTime endTime) {
        int waitedEventInPlanSize = waitedEventInPlan.size();
        for (int i = 0; i < waitedEventInPlanSize; i++) {

            // remove waiting event and obtain time
            LocalDateTime detectorActivationTime = getDetectorActivation(endTime);
            LocalDateTime detectorDeactivationTime = getDetectorDeactivationForDetectorActivation(
                    detectorActivationTime);

            // if there is a detector activation increase vehicle count
            if (detectorActivationTime != null && detectorDeactivationTime != null) {
                cycleVolume++;
            }

            // if either detector activation or deactivation is missing
            if (detectorActivationTime == null) {
                break;
            }

            // if there is no deactivation yet, take current time as deactivation time
            if (detectorDeactivationTime == null) {
                detectorDeactivationTime = endTime;
            }

            // check if the detector was occupied after green start
            cycleDetectorOccupiedDuringGreen = cycleDetectorOccupiedDuringGreen || checkDetectorOccupiedDuringGreen(
                    detectorActivationTime, detectorDeactivationTime);

            //
            double detectorOccupationTime = DateTimeUtil
                    .calculateDurationTime(detectorActivationTime, detectorDeactivationTime);
            double vehicleLengthEstimation = 4.5;
            double speedEstimationMps = vehicleLengthEstimation / detectorOccupationTime;
            double speedEstimationMph = speedEstimationMps * 2.237;
            this.cycleSpeedSum += speedEstimationMph;
        }
    }

    /**
     * Get next detector activation if it is not after end time of interval
     *
     * @param endTime end time of interval
     * @return {@link LocalDateTime} if detector activation exists, null otherwise
     */
    private LocalDateTime getDetectorActivation(LocalDateTime endTime) {
        PerfLogEventVO earliest = waitedEventInPlan.get(0);

        if (earliest.getDateTime().isAfter(endTime)) {
            return null;
        }

        // remove waiting event if there is a detector deactivation
        if (!detectorDeactivationEvents.isEmpty()) {
            waitedEventInPlan.remove(0);
        }

        return earliest.getDateTime();
    }

    /**
     * From the detector deactivation events this function gets the detector deactivation matching the detector activation time
     *
     * @param detectorActivationTime {@link LocalDateTime} of detector activation
     * @return {@link LocalDateTime} of detector deactivation, null if none is available
     */
    private LocalDateTime getDetectorDeactivationForDetectorActivation(LocalDateTime detectorActivationTime) {
        // check if there already is a detector deactivation for the activation
        if (detectorActivationTime != null && !detectorDeactivationEvents.isEmpty()) {

            LocalDateTime detectorDeactivationTime = detectorDeactivationEvents.poll().getDateTime();

            // poll until detector deactivation time is after detector activation time
            while (!detectorDeactivationTime.isAfter(detectorActivationTime)) {
                PerfLogEventVO detectorDeactivationEvent = detectorDeactivationEvents.poll();

                if (detectorDeactivationEvent == null) {
                    return null;
                }

                detectorDeactivationTime = detectorDeactivationEvent.getDateTime();
            }
            return detectorDeactivationTime;
        }
        return null;
    }

    /**
     * Handles event (DETECTOR_ON, PHASE_BEGIN_GREEN, PHASE_BEGIN_YELLOW_CLEARANCE)
     * depending on type.
     *
     * @param eventVO {@link PerfLogEventVO} to handle
     */
    private void handleEventPlan(PerfLogEventVO eventVO) {
        LocalDateTime eventTime = eventVO.getDateTime();

        switch (eventVO.getEvent()) {

        // detector activations need to be stored
        case DETECTOR_ON:
            if (eventVO.getParameter() == detectorId) {
                Double projectedTime = timeSimulates.get((int) eventVO.getParameter());
                if (projectedTime != null) {
                    long projectedTimeInNanos = Math.round(projectedTime * DateTimeUtil.NANOS_PER_SECOND);
                    LocalDateTime arrivalTime = eventTime.plusNanos(projectedTimeInNanos);

                    PerfLogEventVO newEvent = new PerfLogEventVO(arrivalTime, eventVO.getEvent(),
                            eventVO.getParameter());
                    waitedEventInPlan.add(newEvent);
                } else {
                    waitedEventInPlan.add(eventVO);
                }

                // Sort all waited event to ensure event ordering
                waitedEventInPlan.sort(Comparator.comparing(PerfLogEventVO::getDateTime));
            }
            break;

        // store detector deactivation events
        case DETECTOR_OFF:
            if (eventVO.getParameter() == detectorId) {
                detectorDeactivationEvents.add(eventVO);
            }
            break;

        // begin of green needs to be stored to calculate green duration
        case PHASE_BEGIN_GREEN:

            // store the beginning green
            if (greenBeginTime == null) {
                greenBeginTime = eventVO.getDateTime();
            } else {
                // there are 2 following green begins
                log.debug("there are 2 following green begins @" + eventTime);
            }
            break;

        // begin of yellow marks end of green
        case PHASE_BEGIN_YELLOW_CLEARANCE:

            // calculate green duration
            if (greenBeginTime != null) {
                cycleGreenDuration += DateTimeUtil.calculateDurationTime(greenBeginTime, eventTime);
                lastGreenBeginTime = greenBeginTime;
                greenBeginTime = null;
            } else {
                log.debug("There is no green start to the green end  @" + eventTime);
            }
            break;

        default:
            break;
        }
    }

}
