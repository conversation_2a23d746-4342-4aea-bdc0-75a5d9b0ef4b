/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AoyBinVO.java
 * Project     : SPM Platform
 */
package com.siemens.spm.analysis.vo;

import java.time.LocalDateTime;

public class AoyBinVO extends VolumeBinVO {

    private static final long serialVersionUID = -6951273395062240973L;

    public AoyBinVO(LocalDateTime fromTime, int binSize) {
        this(fromTime, fromTime.plusSeconds(binSize));
    }

    public AoyBinVO(LocalDateTime fromTime, LocalDateTime toTime) {
        super(fromTime, toTime);
    }

    public AoyBinVO(VolumeBinVO volumeBinVO) {
        super(volumeBinVO);
    }

    public double getAoyVolume() {
        return aoyVB.getVolumePerHour();
    }

    public Double getAoyPercent() {
        int totalDetHits = getTotalDetHits();
        return totalDetHits != 0 ? (double) aoyVB.getCount() / totalDetHits * 100 : null;
    }

}
