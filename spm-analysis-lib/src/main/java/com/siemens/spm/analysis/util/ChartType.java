package com.siemens.spm.analysis.util;

import java.util.HashMap;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum ChartType {

    VOLUME_ONE_PHASE("volume_one_phase"),
    VOLUME_ALL_PHASE("volume_all_phase"),
    APP_DELAY_PHASE("app_delay_phase"),
    APP_DELAY_LANE("app_delay_lane"),
    QUEUE_LENGTH_PHASE("queue_length_phase"),
    QUEUE_LENGTH_LANE("queue_length_lane"),
    PREEMPTION_DETAIL("preemption_detail"),
    PRIORITY_DETAIL("priority_detail"),
    PREEMPTION_PRIORITY_REQUEST("pp_request");

    private static final Map<String, ChartType> chartTypes = new HashMap<>();

    private final String chartName;

    static {
        for (ChartType chartType : ChartType.values()) {
            chartTypes.put(chartType.getChartName().toLowerCase(), chartType);
        }
    }

    public static ChartType resolve(String chartName) {
        return chartName != null ? chartTypes.get(chartName.toLowerCase()) : null;
    }

    public boolean matches(String chartName) {
        return this == resolve(chartName);
    }

}
