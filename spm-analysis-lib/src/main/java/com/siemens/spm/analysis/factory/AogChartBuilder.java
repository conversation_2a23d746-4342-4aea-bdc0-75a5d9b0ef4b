package com.siemens.spm.analysis.factory;

import com.siemens.spm.analysis.aggregator.PhaseCallAggregator;
import com.siemens.spm.analysis.aggregator.PlanStatAggregator;
import com.siemens.spm.analysis.domain.Phase;
import com.siemens.spm.analysis.domain.PhaseColor;
import com.siemens.spm.analysis.util.EventValidatorUtils;
import com.siemens.spm.analysis.vo.AogBinVO;
import com.siemens.spm.analysis.vo.AogChartVO;
import com.siemens.spm.analysis.vo.AogPlanStatisticsVO;
import com.siemens.spm.analysis.vo.AorBinVO;
import com.siemens.spm.analysis.vo.VolumeBinVO;
import com.siemens.spm.perflog.vo.DetectorInfoVO;
import com.siemens.spm.perflog.vo.IntersectionConfigVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class AogChartBuilder extends PhaseChartBuilder {

    private AogChartVO chartVO;

    private PhaseCallAggregator volumeAggregator;

    private PlanStatAggregator<AogPlanStatisticsVO> planStatAggregator;

    private int upstreamAogHits;
    private int upstreamAogVolume;

    /**
     * DO NOT remove this constructor. This constructor used to create chart builder object via reflection
     *
     * @param chartVO Chart object to be built, must not be null
     * @param binSize binSize in {@link Integer}
     */
    public AogChartBuilder(AogChartVO chartVO, Integer binSize) {
        super(chartVO.getPhase());
        init(chartVO, binSize, null);
    }

    /**
     * For Testing only
     * 
     * @param chartVO       Chart object to be built, must not be null
     * @param binSize       Volume bin size in seconds from {@link AorBinVO}
     * @param detCallPhases Detector configuration from {@link DetectorInfoVO}
     */
    public AogChartBuilder(AogChartVO chartVO, int binSize, int[][] detCallPhases) {
        super(chartVO.getPhase());
        init(chartVO, binSize, detCallPhases);
    }

    /**
     * Initialize states of builder. This method must be called at start of processing and before other processing
     * methods.
     *
     * @param chartVO       {@code AogChartVO}
     * @param binSize       size of bin
     * @param detCallPhases Detector configuration from {@link DetectorInfoVO}
     */
    private void init(AogChartVO chartVO, int binSize, int[][] detCallPhases) {
        this.chartVO = chartVO;

        this.volumeAggregator = new PhaseCallAggregator(chartVO.getPhase(), chartVO.getFromTime(), binSize,
                detCallPhases);
        this.planStatAggregator = new PlanStatAggregator<>(chartVO.getPhase(), PhaseColor.GREEN,
                AogPlanStatisticsVO.class, chartVO.getFromTime(), chartVO.getToTime());
        this.planStatAggregator.putDetCallPhases(detCallPhases);

        this.upstreamAogHits = 0;
        this.upstreamAogVolume = 0;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void setIntersectionConfig(IntersectionConfigVO configVO) {
        planStatAggregator.setConfig(configVO);
        volumeAggregator.setConfig(configVO);
    }

    @Override
    public void putEvent(PerfLogEventVO eventVO) {
        super.putEvent(eventVO);
        if (!EventValidatorUtils.isCandidateInRange(eventVO, chartVO.getFromTime(), chartVO.getToTime())) {
            return;
        }
        planStatAggregator.putEvent(eventVO);
        volumeAggregator.putEvent(eventVO);

        // TODO: Create Aggregator for upstream
        if (eventVO.getEvent() == PerfLogEventVO.Event.DETECTOR_ON
                && phase.getColor() == PhaseColor.GREEN
                && phase.getPhaseApproach() == Phase.Approach.UPSTREAM) {
            upstreamAogHits += 1;
        }
    }

    @Override
    public void build() {
        volumeAggregator.fillEndBins(chartVO.getToTime());
        chartVO.setAogBins(createAogVolumeBinList(volumeAggregator.getBinList()));

        // Add plan statistics to chart
        List<AogPlanStatisticsVO> aogPlanStatisticsVOList = planStatAggregator.getAggregatedPlanStat();
        chartVO.addListPlanStatistics(aogPlanStatisticsVOList);

        // Overall statistics
        chartVO.setTotalDetectorHits(volumeAggregator.getTotalPhaseCalls());
        chartVO.setTotalAogHits(volumeAggregator.getTotalAog());
        chartVO.setTotalAorHits(volumeAggregator.getTotalAor());

        // TODO: Remove unused Aor

        chartVO.setUpstreamAogHits(upstreamAogHits);
        chartVO.setUpstreamAogVolumn(upstreamAogVolume);
    }

    /**
     * Create List of {@link AogBinVO} from list of {@link VolumeBinVO}
     *
     * @param volumeBinList list of {@link VolumeBinVO}
     * @return {@code List<AogBinVO>}
     */
    private List<AogBinVO> createAogVolumeBinList(List<VolumeBinVO> volumeBinList) {
        if (volumeBinList == null) {
            return Collections.emptyList();
        }

        List<AogBinVO> aogBinList = new ArrayList<>();
        for (VolumeBinVO volBin : volumeBinList) {
            if (volBin == null) {
                continue;
            }

            aogBinList.add(new AogBinVO(volBin));

            if (Phase.Approach.UPSTREAM.equals(phase.getPhaseApproach())) {
                upstreamAogVolume += volBin.getAogVB().getCount();
            }
        }

        return aogBinList;
    }

}
