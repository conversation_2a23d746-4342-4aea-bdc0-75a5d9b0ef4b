/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : VolumeBinVO.java
 * Project     : SPM Platform
 */
package com.siemens.spm.analysis.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.analysis.domain.PhaseColor;
import com.siemens.spm.analysis.domain.VolumeBlock;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.Duration;
import java.time.LocalDateTime;

@Getter
@Setter
public class VolumeBinVO implements Serializable, DataBlockVO {

    private static final long serialVersionUID = 1954543326643612837L;

    private static final int SECONDS_PER_HOUR = 3600;

    public static final int BIN_SIZE_300 = 300;
    public static final int BIN_SIZE_900 = 900;
    public static final int BIN_SIZE_3600 = 3600;
    public static final int DEFAULT_BIN_SIZE = BIN_SIZE_900;

    @JsonProperty("from_time")
    protected LocalDateTime fromTime;
    @JsonProperty("to_time")
    protected LocalDateTime toTime;

    @JsonIgnore
    protected VolumeBlock aogVB; // Arrivals on green
    @JsonIgnore
    protected VolumeBlock aoyVB; // Arrivals on yellow
    @JsonIgnore
    protected VolumeBlock aorVB; // Arrivals on red
    @JsonIgnore
    protected VolumeBlock unknownVB; // Arrivals on unknown color

    public VolumeBinVO(LocalDateTime fromTime, int binSize) {
        this(fromTime, fromTime.plusSeconds(binSize));
    }

    public VolumeBinVO(LocalDateTime fromTime, LocalDateTime toTime) {
        this.fromTime = fromTime;
        this.toTime = toTime;

        aogVB = new VolumeBlock(fromTime, toTime);
        aoyVB = new VolumeBlock(fromTime, toTime);
        aorVB = new VolumeBlock(fromTime, toTime);
        unknownVB = new VolumeBlock(fromTime, toTime);
    }

    public VolumeBinVO(VolumeBinVO volumeBinVO) {
        this.fromTime = volumeBinVO.fromTime;
        this.toTime = volumeBinVO.toTime;

        this.aogVB = volumeBinVO.aogVB;
        this.aoyVB = volumeBinVO.aoyVB;
        this.aorVB = volumeBinVO.aorVB;
        this.unknownVB = volumeBinVO.unknownVB;
    }

    public void incDetHits(PhaseColor color) {
        switch (color) {
        case GREEN:
            aogVB.incCount();
            break;
        case YELLOW:
            aoyVB.incCount();
            break;
        case RED:
            aorVB.incCount();
            break;
        default:
            unknownVB.incCount();
            break;
        }
    }

    public void mergeUnknownHitsAsAog() {
        aogVB.addCount(unknownVB.getCount());
        unknownVB.resetCount();
    }

    public void mergeUnknownHitsAsAoy() {
        aoyVB.addCount(unknownVB.getCount());
        unknownVB.resetCount();
    }

    public void mergeUnknownHitsAsAor() {
        aorVB.addCount(unknownVB.getCount());
        unknownVB.resetCount();
    }

    @JsonIgnore
    public int getAogDetHits() {
        return aogVB.getCount();
    }

    @JsonIgnore
    public int getAoyDetHits() {
        return aoyVB.getCount();
    }

    @JsonIgnore
    public int getAorDetHits() {
        return aorVB.getCount();
    }

    @JsonIgnore
    public int getTotalDetHits() {
        return aogVB.getCount() + aoyVB.getCount() + aorVB.getCount() + unknownVB.getCount();
    }

    @JsonProperty("volume")
    public Double getVolume() {
        int totalDetHits = getTotalDetHits();
        Duration duration = Duration.between(fromTime, toTime);
        if (duration.getSeconds() > 0) {
            return (double) totalDetHits / duration.getSeconds() * SECONDS_PER_HOUR;
        } else {
            return null;
        }
    }

    @Override
    public void setFromTime(LocalDateTime fromTime) {
        this.fromTime = fromTime;
        aogVB.setFromTime(fromTime);
        aoyVB.setFromTime(fromTime);
        aorVB.setFromTime(fromTime);
        unknownVB.setFromTime(fromTime);
    }

    @Override
    public void setToTime(LocalDateTime toTime) {
        this.toTime = toTime;
        aogVB.setToTime(toTime);
        aoyVB.setToTime(toTime);
        aorVB.setToTime(toTime);
        unknownVB.setToTime(toTime);
    }

}
