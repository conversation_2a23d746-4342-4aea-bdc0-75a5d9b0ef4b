package com.siemens.spm.analysis.vo.moe;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PhaseCycleMOEVO implements Serializable {

    @Serial private static final long serialVersionUID = 5287843395329459565L;

    @JsonIgnore
    private String[] phaseStatusArray = new String[2];

    @JsonProperty("phase")
    private int phase;

    @JsonProperty("programmed_length")
    private int programmedLength;

    @JsonProperty("actual_length")
    private int actualLength;

    @JsonIgnore
    private LocalDateTime fromTime;

    @JsonIgnore
    private LocalDateTime toTime;

    @JsonGetter("status")
    public List<String> phaseStatus() {
        List<String> status = new ArrayList<>();
        for (String phaseStatus : phaseStatusArray) {
            if (phaseStatus != null) {
                status.add(phaseStatus);
            }
        }
        return status;
    }
}
