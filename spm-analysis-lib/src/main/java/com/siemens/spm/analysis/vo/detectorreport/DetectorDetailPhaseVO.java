package com.siemens.spm.analysis.vo.detectorreport;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DetectorDetailPhaseVO implements Serializable {

    @Serial
    private static final long serialVersionUID = -3722671282144356457L;

    @JsonProperty("total_activation")
    private Long totalActivation;

    @JsonProperty("occupancy")
    private Double occupancy;

    @JsonProperty("avg_volume")
    private Double avgVolume;

    @JsonProperty("detector")
    private int detector;
}
