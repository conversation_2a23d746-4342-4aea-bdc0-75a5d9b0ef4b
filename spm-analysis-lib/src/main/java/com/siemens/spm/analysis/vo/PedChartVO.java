package com.siemens.spm.analysis.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@AllArgsConstructor
@JsonInclude(Include.NON_NULL)
public class PedChartVO extends PhaseChartVO {

    private static final long serialVersionUID = -1609768782481521915L;

    @JsonProperty("ped_changes")
    private List<PedChangeVO> pedChangeList;

    @JsonProperty("det_acts")
    private List<PedDetectorActivityVO> pedDetActList;

    @JsonProperty("ped_delays")
    private List<PedDelayVO> pedDelayVOList;

    @JsonProperty("plan_statistics")
    private List<PedPlanStatisticsVO> planStatisticsList;

    public PedChartVO() {
        pedChangeList = new ArrayList<>();
        pedDetActList = new ArrayList<>();
        pedDelayVOList = new ArrayList<>();
        planStatisticsList = new ArrayList<>();
    }

    public void addPedChange(PedChangeVO pedChangeVO) {
        if (pedChangeVO == null) {
            throw new IllegalArgumentException("PedChangeVO must not be null");
        }

        pedChangeList.add(pedChangeVO);
    }

    public void addPedDetectorActivity(PedDetectorActivityVO pedDetectorActivityVO) {
        if (pedDetectorActivityVO == null) {
            throw new IllegalArgumentException("PedDetectorActivityVO must not be null");
        }

        pedDetActList.add(pedDetectorActivityVO);
    }

    public void addPedDelay(PedDelayVO pedDelayVO) {
        if (pedDelayVO == null) {
            throw new IllegalArgumentException("PedDelayVO must not be null");
        }

        pedDelayVOList.add(pedDelayVO);
    }

    public void addPlanStatistic(PedPlanStatisticsVO planStatisticsVO) {
        if (planStatisticsVO == null) {
            throw new IllegalArgumentException("PedPlanStatisticsVO must not be null");
        }

        planStatisticsList.add(planStatisticsVO);
    }

    @JsonIgnore
    public long getSumPedDelayTime() {
        long sumPedDelayTime = 0;
        for (PedDelayVO pedDelayVO : pedDelayVOList) {
            sumPedDelayTime += pedDelayVO.getPedDelayDuration();
        }

        return sumPedDelayTime;
    }

    @JsonIgnore
    public int getPedDelayHit() {
        int pedDelayHit = 0;
        if (pedDelayVOList != null) {
            pedDelayHit = pedDelayVOList.size();
        }

        return pedDelayHit;
    }

}
