package com.siemens.spm.analysis.vo.splitmonitor;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.analysis.util.DateTimeUtil;
import com.siemens.spm.analysis.vo.LightBlockVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RingInformationVO {

    @JsonProperty("ring_no")
    private int ringNo;

    @JsonProperty("start_time")
    private LocalDateTime startTime;

    @JsonProperty("end_time")
    private LocalDateTime endTime;

    @JsonProperty("light")
    private List<LightBlockVO> lights;

    @JsonProperty("skipped_phases")
    private List<SkippedPhaseVO> skippedPhases;

    @JsonProperty("duration")
    public Integer getDuration() {
        return DateTimeUtil.duration(startTime, endTime)
                .map(Duration::toSeconds)
                .map(Math::toIntExact)
                .orElse(0);
    }

    @JsonProperty("no_skipped_phase")
    public int getNoSkippedPhase() {
        return skippedPhases == null ? 0 : skippedPhases.size();
    }

    @JsonProperty("skipped_duration")
    public int getSkippedDuration() {
        return skippedPhases == null ? 0 : skippedPhases.stream()
                .mapToInt(SkippedPhaseVO::getDuration)
                .sum();
    }

    @JsonIgnore
    public boolean isEmpty() {
        return CollectionUtils.isEmpty(lights);
    }

    public static RingInformationVO of(int ringNo,
                                       List<LightBlockVO> actualLights,
                                       List<LightBlockVO> programedLights) {
        LocalDateTime startTime = CollectionUtils.isEmpty(actualLights)
                ? null
                : Objects.requireNonNull(CollectionUtils.firstElement(actualLights)).getStartTime();

        LocalDateTime endTime = CollectionUtils.isEmpty(actualLights)
                ? null
                : Objects.requireNonNull(CollectionUtils.lastElement(actualLights)).getEndTime();

        List<SkippedPhaseVO> skippedPhases = getSkippedPhases(actualLights, programedLights);

        return RingInformationVO.builder()
                .ringNo(ringNo)
                .lights(actualLights)
                .startTime(startTime)
                .endTime(endTime)
                .skippedPhases(skippedPhases)
                .build();
    }

    private static List<SkippedPhaseVO> getSkippedPhases(List<LightBlockVO> actualLights,
                                                         List<LightBlockVO> programedLights) {
        List<Integer> actualPhases = actualLights.stream()
                .map(LightBlockVO::getPhase)
                .distinct()
                .toList();

        return programedLights.stream()
                .filter(lights -> !actualPhases.contains(lights.getPhase()))
                .collect(Collectors.groupingBy(LightBlockVO::getPhase))
                .entrySet()
                .stream()
                .map(entry -> {
                    int duration = entry.getValue()
                            .stream()
                            .mapToInt(LightBlockVO::getDuration)
                            .sum();
                    return new SkippedPhaseVO(entry.getKey(), duration);
                })
                .toList();
    }

}
