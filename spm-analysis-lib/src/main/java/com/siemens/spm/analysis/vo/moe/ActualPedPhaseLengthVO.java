package com.siemens.spm.analysis.vo.moe;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ActualPedPhaseLengthVO implements Serializable {

    @Serial private static final long serialVersionUID = -1668592483205056737L;

    @JsonProperty("avg_ped_walk")
    private Double avgPedWalk;

    @JsonProperty("avg_ped_clear")
    private Double avgPedClear;

    @JsonIgnore
    private List<Integer> actualPedWalkList;

    @JsonIgnore
    private List<Integer> actualPedClearList;

    @JsonIgnore
    public void addActualPedWalk(int actualPedWalk) {
        if (this.actualPedWalkList == null) {
            this.actualPedWalkList = new ArrayList<>();
        }
        actualPedWalkList.add(actualPedWalk);
    }

    @JsonIgnore
    public void addActualPedClear(int actualPedClear) {
        if (this.actualPedClearList == null) {
            this.actualPedClearList = new ArrayList<>();
        }
        actualPedClearList.add(actualPedClear);
    }

    @JsonIgnore
    public void addActualPedWalkList(List<Integer> actualPedWalkList) {
        if (this.actualPedWalkList == null) {
            this.actualPedWalkList = new ArrayList<>();
        }
        this.actualPedWalkList.addAll(actualPedWalkList);
    }

    @JsonIgnore
    public void addActualPedClearList(List<Integer> actualPedClearList) {
        if (this.actualPedClearList == null) {
            this.actualPedClearList = new ArrayList<>();
        }
        this.actualPedClearList.addAll(actualPedClearList);
    }

    @JsonIgnore
    private void calAvgPedWalk() {
        if (actualPedWalkList == null) {
            return;
        }
        avgPedWalk = actualPedWalkList.stream().reduce(0, Integer::sum) / (actualPedWalkList.size() * 1.0);
    }

    private void calAvgPedClear() {
        if (actualPedClearList == null) {
            return;
        }
        avgPedClear = actualPedClearList.stream().reduce(0, Integer::sum) / (actualPedClearList.size() * 1.0);
    }

    public void calAndFillVO() {
        calAvgPedClear();
        calAvgPedWalk();
    }
}
