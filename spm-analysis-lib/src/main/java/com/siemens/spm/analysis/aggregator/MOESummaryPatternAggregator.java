package com.siemens.spm.analysis.aggregator;

import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.DETECTOR_ON;
import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.PEDESTRIAN_BEGIN_CLEARANCE;
import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.PEDESTRIAN_BEGIN_NOT_WALK;
import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.PEDESTRIAN_BEGIN_WALK;
import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.PEDESTRIAN_CALL_REGISTERED;
import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.PHASE_BEGIN_GREEN;
import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.PHASE_BEGIN_RED_CLEARANCE;
import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.PHASE_BEGIN_YELLOW_CLEARANCE;
import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.PHASE_END_RED_CLEARANCE;
import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.PHASE_END_YELLOW_CLEARANCE;
import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.PHASE_FORCE_OFF;
import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.PHASE_GAP_OUT;
import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.PHASE_GREEN_TERMINATION;
import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.PHASE_MAX_OUT;
import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.PHASE_OMIT_ON;
import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.PHASE_ON;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import com.siemens.spm.analysis.vo.moe.PatternSummaryMOEVO;
import com.siemens.spm.common.shared.domaintype.alarm.trafficmetric.MetricSpecifier;
import com.siemens.spm.common.shared.domaintype.alarm.trafficmetric.TrafficMetric;
import com.siemens.spm.perflog.vo.IntersectionConfigVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;
import com.siemens.spm.perflog.vo.PhaseVO;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class MOESummaryPatternAggregator implements PerfLogEventAggregator {

    // detector number -> list of phases from intersection topology
    private Map<Integer, List<PhaseVO>> detPhaseMap = Map.of();

    private IntersectionConfigVO configVO;

    private final int pattern;

    @Getter
    private Map<Integer, MOESummaryPhaseAggregator> moeSummaryPhaseAggregatorMap;

    private PatternSummaryMOEVO patternSummaryMOEVO;

    private static final List<PerfLogEventVO.Event> PATTERN_EVENT = List.of(
            PHASE_ON,
            PHASE_BEGIN_GREEN,
            PHASE_GREEN_TERMINATION,
            PHASE_BEGIN_YELLOW_CLEARANCE,
            PHASE_END_YELLOW_CLEARANCE,
            PHASE_BEGIN_RED_CLEARANCE,
            PHASE_END_RED_CLEARANCE,
            PHASE_FORCE_OFF,
            PHASE_GAP_OUT,
            PHASE_MAX_OUT,
            PHASE_OMIT_ON,
            DETECTOR_ON,
            PEDESTRIAN_CALL_REGISTERED,
            PEDESTRIAN_BEGIN_CLEARANCE,
            PEDESTRIAN_BEGIN_WALK,
            PEDESTRIAN_BEGIN_NOT_WALK
    );

    private static final List<PerfLogEventVO.Event> PHASE_EVENT = List.of(
            PHASE_ON,
            PHASE_BEGIN_GREEN,
            PHASE_GREEN_TERMINATION,
            PHASE_BEGIN_YELLOW_CLEARANCE,
            PHASE_END_YELLOW_CLEARANCE,
            PHASE_BEGIN_RED_CLEARANCE,
            PHASE_END_RED_CLEARANCE
    );

    public MOESummaryPatternAggregator(int pattern, IntersectionConfigVO configVO) {
        this.configVO = configVO;
        this.pattern = pattern;
        this.moeSummaryPhaseAggregatorMap = new LinkedHashMap<>();
        this.patternSummaryMOEVO = new PatternSummaryMOEVO();
        detPhaseMap = configVO.groupPhasesByDetNum();
    }

    @Override
    public void setConfig(IntersectionConfigVO configVO) {
        this.configVO = configVO;
        detPhaseMap = configVO.groupPhasesByDetNum();
        moeSummaryPhaseAggregatorMap.forEach((k, v) -> v.setConfig(configVO));
    }

    @Override
    public void putEvent(PerfLogEventVO eventVO) {
        PerfLogEventVO.Event event = eventVO.getEvent();
        if (PATTERN_EVENT.contains(event)) {
            if (PHASE_EVENT.contains(event)) {
                log.info("=========== We receive event {} in phase: {} at {}", event, eventVO.getParameter(), eventVO.getDateTime());
            }
            if (event.equals(DETECTOR_ON)) {
                int detectorNum = (int) eventVO.getParameter();

                // int[][] detCallPhases = configVO.getDetInfo().getDetCallPhases();
                // List<Integer> phaseListByDet = Arrays.stream(detCallPhases[detectorNum - 1]).boxed().toList();
                // for (Integer currentPhase : phaseListByDet) {
                //     putEventToMap(currentPhase, eventVO);
                // }

                List<PhaseVO> phaseList = detPhaseMap.get(detectorNum);
                if (phaseList != null) {
                    for (PhaseVO phase : phaseList) {
                        int currentPhase = phase.getPhaseNumber();
                        putEventToMap(currentPhase, eventVO);
                    }
                }

            } else {
                int currentPhase = (int) eventVO.getParameter();
                putEventToMap(currentPhase, eventVO);
            }
        }
    }

    public void fillPatternSummaryMOEVO() {
        moeSummaryPhaseAggregatorMap = moeSummaryPhaseAggregatorMap.entrySet()
                .stream()
                .sorted(Map.Entry.comparingByKey())
                .collect(
                        LinkedHashMap::new,
                        (acc, entry) -> acc.put(entry.getKey(), entry.getValue()),
                        LinkedHashMap::putAll
                );
        moeSummaryPhaseAggregatorMap.forEach((k, v) -> {
            v.aggregateAndFillVO();
            log.debug("(fillPatternSummaryMOEVO)k : {}", k);
            if (!v.getPhaseSummaryMOEVO().isAllPropertyNull()) {
                patternSummaryMOEVO.addPhaseSummaryMOEVOElement(k, v.getPhaseSummaryMOEVO());
                patternSummaryMOEVO.addPhaseSplitTimeAggregationVOElement(k,
                        v.getPhaseSplitTimeAggregationVO());
            }
        });
    }

    private void putEventToMap(int currentPhase, PerfLogEventVO eventVO) {
        moeSummaryPhaseAggregatorMap.putIfAbsent(currentPhase,
                new MOESummaryPhaseAggregator(currentPhase, pattern, configVO));
        moeSummaryPhaseAggregatorMap.get(currentPhase).putEvent(eventVO);
    }

    public PatternSummaryMOEVO getPatternSummaryMOEVO() {
        fillPatternSummaryMOEVO();
        return this.patternSummaryMOEVO;
    }

    @Override
    public List<TrafficMetric> getSupportedMetric() {
        return Collections.emptyList();
    }

    @Override
    public Double getAggregatedValue(MetricSpecifier metricSpecifier) {
        return null;
    }

    @Override
    public void setTargetIdentifier(String targetIdentifier) {
        // not use
    }

    @Override
    public void setBinSize(int binSize) {
        // not use
    }

    @Override
    public void setFromTime(LocalDateTime fromTime) {
        // not use
    }

    @Override
    public void setToTime(LocalDateTime toTime) {
        // not use
    }
}
