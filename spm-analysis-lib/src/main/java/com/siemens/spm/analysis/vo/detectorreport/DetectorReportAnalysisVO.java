package com.siemens.spm.analysis.vo.detectorreport;

import com.siemens.spm.analysis.vo.AbstractAnalysisVO;
import com.siemens.spm.common.shared.domaintype.analysis.AnalysisType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class DetectorReportAnalysisVO extends AbstractAnalysisVO<DetectorReportChartVO> {
    @Override
    protected String getAnalysisType() {
        return AnalysisType.DETECTOR_REPORT.getId();
    }
}
