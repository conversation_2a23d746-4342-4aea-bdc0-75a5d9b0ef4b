package com.siemens.spm.analysis.domain;

import com.siemens.spm.perflog.vo.PerfLogEventVO;

import java.time.Duration;
import java.time.LocalDateTime;

public class PedCycle {

    private int phaseNum;

    private boolean isCycleCompleted;

    private LocalDateTime beginWalkTime;

    private LocalDateTime beginClearTime;

    private LocalDateTime endWalkTime;

    private LocalDateTime endClearTime;

    public PedCycle(int phaseNum, LocalDateTime beginWalkTime) {
        this.phaseNum = phaseNum;
        this.beginWalkTime = beginWalkTime;
    }

    public void updateState(PerfLogEventVO eventVO) {
        if (phaseNum != Phase.UNSPECIFIED_PHASE_NUM && eventVO.getParameter() != phaseNum) {
            return;
        }
        PerfLogEventVO.Event event = eventVO.getEvent();
        switch (event) {
        case PEDESTRIAN_BEGIN_WALK -> initCycle((int) eventVO.getParameter(), eventVO.getDateTime());
        case PEDESTRIAN_BEGIN_CLEARANCE -> {
            this.endWalkTime = eventVO.getDateTime();
            this.beginClearTime = eventVO.getDateTime();
        }
        case PEDESTRIAN_BEGIN_NOT_WALK -> this.endClearTime = eventVO.getDateTime();
        default -> {
            // not use
        }
        }
    }

    private void initCycle(int phaseNum, LocalDateTime beginWalkTime) {
        this.phaseNum = phaseNum;
        this.beginWalkTime = beginWalkTime;
        this.endWalkTime = null;
        this.beginClearTime = null;
        this.endClearTime = null;
        this.isCycleCompleted = false;
    }

    public Duration calWalkTime() {
        return isCycleCompleted &&
                this.beginWalkTime != null &&
                this.endWalkTime != null ?
                Duration.between(this.beginWalkTime, this.endWalkTime) : Duration.ZERO;
    }

    public Duration calClearTime() {
        return isCycleCompleted &&
                this.beginClearTime != null &&
                this.endClearTime != null ?
                Duration.between(this.beginClearTime, this.endClearTime) : Duration.ZERO;
    }

    public void endCycle() {
        this.isCycleCompleted = true;
    }
}
