package com.siemens.spm.analysis.algorithm;

import com.siemens.spm.analysis.domain.DetectorMetric;
import com.siemens.spm.analysis.domain.DetectorMetricCondition;
import com.siemens.spm.analysis.domain.DetectorOperator;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

/**
 * Utility class for evaluating metrics against calculated values.
 * This class provides functionality to check if a calculated metric value
 * satisfies the condition specified in a metric.
 *
 * <AUTHOR> Admin
 * @version : 1.0
 * @since : 20/5/2025
 */
@Slf4j
public final class ReportMetricEvaluation {

    private ReportMetricEvaluation() {
    }

    /**
     * Evaluates whether a calculated value satisfies the condition specified in a detector metric.
     *
     * @param detectorMetric  The detector metric containing the condition to evaluate
     * @param calculatedValue The calculated value to check against the condition
     * @return true if the calculated value satisfies the condition, false otherwise
     * @throws NullPointerException if detectorMetric is null
     */
    public static boolean evaluateDetectorMetric(DetectorMetric detectorMetric, Double calculatedValue) {
        Objects.requireNonNull(detectorMetric, "Detector metric cannot be null");

        if (calculatedValue == null) {
            log.debug("Calculated value is null, returning false");
            return false;
        }

        DetectorMetricCondition metricCondition = detectorMetric.getCondition();
        if (metricCondition == null) {
            log.debug("Metric condition is null, returning false");
            return false;
        }

        String operator = metricCondition.getOperator();
        Double conditionValue = metricCondition.getValue();

        if (operator == null || conditionValue == null) {
            log.debug("Operator or condition value is null, returning false");
            return false;
        }

        DetectorOperator detectorOperator = DetectorOperator.resolve(operator);
        if (detectorOperator == null) {
            log.debug("Invalid operator: {}, returning false", operator);
            return false;
        }

        log.debug("Evaluating metric: operator={}, condition={}, calculated={}",
                operator, conditionValue, calculatedValue);

        return switch (detectorOperator) {
            case EQ -> Objects.equals(conditionValue, calculatedValue);
            case GT -> calculatedValue > conditionValue;
            case LT -> calculatedValue < conditionValue;
            case GTE -> calculatedValue >= conditionValue;
            case LTE -> calculatedValue <= conditionValue;
            case NONE -> false;
        };
    }

}
