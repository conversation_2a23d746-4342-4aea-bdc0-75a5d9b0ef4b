package com.siemens.spm.analysis.factory;

import java.lang.reflect.InvocationTargetException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.siemens.spm.analysis.domain.Phase;
import com.siemens.spm.analysis.exception.ChartBuilderInitializationException;
import com.siemens.spm.analysis.factory.topology.DetTypeUsed;
import com.siemens.spm.analysis.util.EventValidatorUtils;
import com.siemens.spm.analysis.vo.AbstractAnalysisVO;
import com.siemens.spm.analysis.vo.PhaseChartVO;
import com.siemens.spm.perflog.vo.DetectorVO.DetectorType;
import com.siemens.spm.perflog.vo.IntersectionConfigVO;
import com.siemens.spm.perflog.vo.PerfLogChunkVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;
import com.siemens.spm.perflog.vo.PhaseVO;

import lombok.extern.slf4j.Slf4j;

/**
 * Analysis Factory for create AoX analysis as AoR, AoG, AoY.
 * <p>
 * This class is not {@code abstract} class, then it can create concrete object
 * from this class. But for convenient, should create corresponding deliver
 * class for each analysis
 *
 * @param <T_ANALYSIS>      Analysis VO object as
 *                          {@link com.siemens.spm.analysis.vo.AorAnalysisVO},
 *                          {@link com.siemens.spm.analysis.vo.AogAnalysisVO},
 *                          or {@code AoYAnalysisVO}
 * @param <T_CHART>         Analysis Chart VO object as
 *                          {@link com.siemens.spm.analysis.vo.AorChartVO},
 *                          {@link com.siemens.spm.analysis.vo.AogChartVO} or
 *                          {@code AoYCharVO}
 * @param <T_CHART_BUILDER> Chart Builder corresponding to analysis as
 *                          {@link AorChartBuilder}, {@link AogChartBuilder} or
 *                          {@code AoyChartBuilder}
 * <AUTHOR> Nguyen
 */
@Slf4j
@DetTypeUsed({ DetectorType.ADVANCE })
public abstract class ArrivalOnAnalysisFactory<T_ANALYSIS extends AbstractAnalysisVO<T_CHART>, T_CHART extends PhaseChartVO, T_CHART_BUILDER extends PhaseChartBuilder>
        extends AbstractPhaseAnalysisFactory<T_ANALYSIS, T_CHART> {

    public static final int BIN_SIZE_300 = 300;
    public static final int BIN_SIZE_900 = 900;
    public static final int BIN_SIZE_1800 = 1800;
    public static final int BIN_SIZE_3600 = 3600;

    protected Class<T_CHART_BUILDER> builderClass;
    protected Class<T_CHART> chartClass;
    protected int binSize;

    // Map the config id -> list of upstream phases
    private Map<String, List<Integer>> upstreamPhasesInConfig = new HashMap<>();

    protected ArrivalOnAnalysisFactory(int binSize, Class<T_CHART> chartClass, Class<T_CHART_BUILDER> builderClass) {
        this.binSize = binSize;
        this.chartClass = chartClass;
        this.builderClass = builderClass;
    }

    @Override
    protected void scanPerfLog(LocalDateTime fromTime,
                               LocalDateTime toTime,
                               List<PerfLogChunkVO> perfLogChunkVOList,
                               Map<String, IntersectionConfigVO> intConfigVOMap,
                               Map<Integer, T_CHART> chartVOMap)
            throws ChartBuilderInitializationException {
        // [1] Prepare builders for charts: phase number -> chart builder
        Map<Integer, T_CHART_BUILDER> chartBuilderMap = new HashMap<>();
        for (Map.Entry<Integer, T_CHART> entry : chartVOMap.entrySet()) {
            T_CHART_BUILDER chartBuilder;
            try {
                chartBuilder = builderClass.getDeclaredConstructor(chartClass, Integer.class)
                        .newInstance(entry.getValue(), binSize);
            } catch (InstantiationException | IllegalAccessException
                     | InvocationTargetException | NoSuchMethodException e) {
                log.error("Error occur when creating instance for chart builder", e);
                throw new ChartBuilderInitializationException(e);
            }
            chartBuilderMap.put(entry.getKey(), chartBuilder);
        }

        // [2] Read chunks one by one
        for (PerfLogChunkVO chunkVO : perfLogChunkVOList) {
            IntersectionConfigVO intConfigVO = intConfigVOMap.get(chunkVO.getConfigID());
            List<Integer> upstreamPhases = upstreamPhasesInConfig.get(chunkVO.getConfigID());

            for (Map.Entry<Integer, T_CHART_BUILDER> chartBuilderEntry : chartBuilderMap.entrySet()) {
                Integer phaseNum = chartBuilderEntry.getKey();
                T_CHART_BUILDER chartBuilder = chartBuilderEntry.getValue();
                chartBuilder.setIntersectionConfig(intConfigVO);
                // check if the phase is upstream/downstream or not?
                if (upstreamPhases != null && upstreamPhases.contains(phaseNum)) {
                    chartBuilder.setPhaseApproach(Phase.Approach.UPSTREAM);
                }
            }

            // Scan through each event and put relevant events to builders
            for (PerfLogEventVO eventVO : chunkVO.getPerfLogEvents()) {
                processEvent(chartBuilderMap, eventVO);
            }
        }

        // Finalize chart building and output to chartVOMap
        for (T_CHART_BUILDER chartBuilder : chartBuilderMap.values()) {
            chartBuilder.build();
        }
    }

    /**
     * @param chartBuilderMap  {@code Map<Integer, AogChartBuilder>}
     * @param eventVO          {@link PerfLogEventVO} object
     * @param detNumByPhaseNum {@code Map<Integer, Integer>}
     */
    private void processEvent(Map<Integer, T_CHART_BUILDER> chartBuilderMap,
                              PerfLogEventVO eventVO) {
        if (!EventValidatorUtils.isValid(eventVO)) {
            // Should not happen
            log.debug("Ignore invalid event: {}", eventVO);
            return;
        }

        if (eventVO.getEvent() == PerfLogEventVO.Event.COORD_PATTERN_CHANGE) {
            putEventVOToAllBuilders(eventVO, chartBuilderMap);
        }

        if (eventVO.isPhaseEvent()) {
            int phaseNumber = (int) eventVO.getParameter();
            putEventVOToBuilder(eventVO, phaseNumber, chartBuilderMap);
        }

        if (eventVO.getEvent() == PerfLogEventVO.Event.DETECTOR_ON) {
            int detNumber = (int) eventVO.getParameter();
            List<PhaseVO> phaseList = detEventFilter.getPhaseListFromTopo(detNumber);
            if (!phaseList.isEmpty()) {
                PhaseVO lastPhase = phaseList.get(phaseList.size() - 1);
                putEventVOToBuilder(eventVO, lastPhase.getPhaseNumber(), chartBuilderMap);
            }
        }
    }

    private void putEventVOToAllBuilders(PerfLogEventVO eventVO,
                                         Map<Integer, T_CHART_BUILDER> chartBuilderMap) {
        for (T_CHART_BUILDER chartBuilder : chartBuilderMap.values()) {
            chartBuilder.putEvent(eventVO);
        }
    }

    private void putEventVOToBuilder(PerfLogEventVO eventVO,
                                     int phaseNum,
                                     Map<Integer, T_CHART_BUILDER> chartBuilderMap) {
        T_CHART_BUILDER chartBuilder = chartBuilderMap.get(phaseNum);
        if (chartBuilder != null) {
            chartBuilder.putEvent(eventVO);
        } else {
            log.error("Unexpected event is happened: {}", eventVO);
        }
    }

}
