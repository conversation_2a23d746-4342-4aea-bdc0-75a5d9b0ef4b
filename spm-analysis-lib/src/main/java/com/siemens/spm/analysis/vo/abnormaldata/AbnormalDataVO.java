package com.siemens.spm.analysis.vo.abnormaldata;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

@Data
@EqualsAndHashCode(callSuper = false)
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AbnormalDataVO implements Serializable {

    private static final long serialVersionUID = -1632913571629569121L;

    @JsonProperty("event_code")
    private String eventCode;

    @JsonProperty("event_name")
    private String eventName;

    @JsonProperty("params")
    private Set<Long> params;

    @JsonProperty("deviation")
    private Double deviation;

    @JsonProperty("avg")
    private Double avg;

    @JsonProperty("chart")
    private List<DateHoursRawCount> chart;

    @JsonProperty("raw_counts")
    private List<DateRawCount> rawCounts;

    @JsonProperty("details")
    private List<AbnormalDataDetailVO> details;

}
