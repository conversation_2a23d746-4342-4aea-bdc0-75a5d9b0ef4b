package com.siemens.spm.analysis.vo.moe;

import java.io.Serial;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.analysis.vo.BaseChartVO;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
public class MOEAnalysisFreePlanDetailViewVO extends BaseChartVO {

    @Serial private static final long serialVersionUID = -1719037420626182756L;

    @JsonProperty("pattern")
    private final int pattern;

    @JsonProperty("phase_num")
    private int phaseNum;

    /**
     * Exactly 2 elements:
     * <ul>
     * <li>0: Phase status, "PHASE_FORCE_OFF", "PHASE_GAP_OUT", "PHASE_MAX_OUT"</li>
     * <li>1: Pedestrian call status, "PEDESTRIAN_CALL_REGISTERED"</li>
     * </ul>
     */
    @JsonIgnore
    private String[] phaseStatusArray = new String[2];

    @JsonProperty("TSP")
    private boolean tsp;

    @JsonProperty("PE")
    private boolean preemption;

    public MOEAnalysisFreePlanDetailViewVO() {
        this.pattern = 254;
    }

    /**
     * @return split time = PHASE_BEGIN_GREEN to PHASE_END_RED_CLEARANCE.
     */
    @JsonGetter("phase_duration")
    public Long phaseDuration() {
        if (fromTime != null && toTime != null) {
            return Duration.between(fromTime, toTime).toSeconds();
        }
        return null;
    }

    @JsonGetter("status")
    public List<String> phaseStatus() {
        List<String> status = new ArrayList<>();
        for (String phaseStatus : phaseStatusArray) {
            if (phaseStatus != null) {
                status.add(phaseStatus);
            }
        }
        return status;
    }
}
