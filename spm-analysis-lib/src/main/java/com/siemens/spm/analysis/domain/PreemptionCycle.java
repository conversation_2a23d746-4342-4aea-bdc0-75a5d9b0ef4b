package com.siemens.spm.analysis.domain;

import lombok.Getter;
import lombok.Setter;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * This class represent 1 preemption cycle, contains events occur in a preemption cycle
 * <AUTHOR> Nguyen
 */
@Getter
@Setter
public class PreemptionCycle {

    private List<LocalDateTime> inputOnTimes;

    private List<LocalDateTime> inputOffTimes;

    private LocalDateTime warningInput;

    private LocalDateTime startInputOn;

    private LocalDateTime cycleStart;

    private LocalDateTime cycleEnd;

    private LocalDateTime gateDown;

    private LocalDateTime entryStarted;

    private LocalDateTime beginTrackClearance;

    private LocalDateTime beginDwellService;

    private LocalDateTime linkActive;

    private LocalDateTime linkInactive;

    private LocalDateTime maxPresenceExceeded;

    private LocalDateTime beginExitInterval;

    private boolean hasDelay;

    public void addInputOn(LocalDateTime inputOnTime) {
        if (inputOnTimes == null) {
            inputOnTimes = new ArrayList<>();
        }

        inputOnTimes.add(inputOnTime);
    }

    public void addInputOff(LocalDateTime inputOffTime) {
        if (inputOffTimes == null) {
            inputOffTimes = new ArrayList<>();
        }

        inputOffTimes.add(inputOffTime);
    }

    public double getDWellDuration() {
        if (beginDwellService != null && cycleEnd != null) {
            return secondBetween(beginDwellService, cycleEnd);
        }

        return 0;
    }

    public double getTrackClearInterval() {
        if (beginTrackClearance != null && beginDwellService != null) {
            return secondBetween(beginTrackClearance, beginDwellService);
        }

        return 0;
    }

    public double getEntryDelayDuration() {
        if (hasDelay && cycleStart != null && entryStarted != null) {
            return secondBetween(cycleStart, entryStarted);
        }

        return 0;
    }

    private double secondBetween(LocalDateTime fromTime, LocalDateTime toTime) {
        long durationInMillis = Duration.between(fromTime, toTime).toMillis();

        return 1.0 * durationInMillis / 1000;
    }

}
