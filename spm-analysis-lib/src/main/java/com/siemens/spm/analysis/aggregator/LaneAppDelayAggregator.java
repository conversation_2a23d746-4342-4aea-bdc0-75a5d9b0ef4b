package com.siemens.spm.analysis.aggregator;

import com.siemens.spm.analysis.util.AppDelayUtil;
import com.siemens.spm.analysis.vo.AppDelayVehicleVO;
import com.siemens.spm.perflog.vo.IntersectionConfigVO;
import com.siemens.spm.perflog.vo.PhaseVO;

import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;

import org.springframework.data.util.Pair;

public class LaneAppDelayAggregator extends AppDelayAggregator {

    private final int laneIdx;

    public LaneAppDelayAggregator(LocalDateTime fromTime, LocalDateTime toTime,
                                  int binSize, int phaseNum, int laneIdx) {
        super(fromTime, toTime, binSize, phaseNum);

        this.laneIdx = laneIdx;
    }

    @Override
    public void setConfig(IntersectionConfigVO configVO) {
        Pair<PhaseVO, Double> phasePair = AppDelayUtil.identifyPhase(configVO, phase.getPhaseNum());
        if (phasePair == null) {
            return;
        }

        PhaseVO phaseVO = phasePair.getFirst();
        if (phaseVO.getLanes() != null && phaseVO.getLanes().size() > laneIdx) {
            createAppDelaySupporter(phaseVO, phasePair.getSecond(), laneIdx).ifPresent(supporters::add);
        }
    }

     /**
     * Get lane delay bins with maximum delay sum
     *
     * @return List of delays for lane with most delay
     */
    @Override
    public List<AppDelayVehicleVO> getAppDelayVehicleList() {
        return supporters.stream()
                .map(AppDelaySupporter::getDelayVehicleList)
                .max(Comparator.comparing(
                        vehicleList -> vehicleList.stream()
                                .mapToDouble(AppDelayVehicleVO::getDelayDuration)
                                .sum()))
                .orElse(List.of());
    }

}
