package com.siemens.spm.analysis.aggregator;

import com.siemens.spm.analysis.domain.MOECoorCycle;
import com.siemens.spm.analysis.vo.Plan;
import com.siemens.spm.analysis.vo.moe.PhaseCycleMOEVO;
import com.siemens.spm.perflog.vo.IntersectionConfigVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

public class CoordinatePhaseTrackerAggregator extends PhaseEventAggregator {

    private MOECoorCycle cycle;

    private final int pattern;

    @Setter
    private IntersectionConfigVO configVO;

    @Getter
    private PhaseCycleMOEVO phaseCycleMOEVO;

    public CoordinatePhaseTrackerAggregator(int phase, int pattern, IntersectionConfigVO configVO) {
        super.init(phase);
        this.pattern = pattern;
        this.configVO = configVO;
    }

    @Override
    public void putEvent(PerfLogEventVO eventVO) {
        PerfLogEventVO.Event event = eventVO.getEvent();
        switch (event) {
            case PHASE_BEGIN_GREEN -> {
                calAndPutCycleLength(eventVO.getDateTime());
                cycle.updateState(eventVO);
            }
            case PHASE_BEGIN_YELLOW_CLEARANCE,
                 PHASE_GREEN_TERMINATION,
                 PHASE_END_YELLOW_CLEARANCE,
                 PHASE_BEGIN_RED_CLEARANCE,
                 PHASE_END_RED_CLEARANCE -> cycle.updateState(eventVO);
            case PHASE_GAP_OUT,
                 PHASE_FORCE_OFF,
                 PHASE_MAX_OUT-> {
                if (phaseCycleMOEVO != null) {
                    phaseCycleMOEVO.getPhaseStatusArray()[0] = event.name();
                }
            }
            case PEDESTRIAN_CALL_REGISTERED -> {
                if (phaseCycleMOEVO != null) {
                    phaseCycleMOEVO.getPhaseStatusArray()[1] = event.name();
                }
            }
            default -> {
                // not use
            }
        }
    }

    private void calAndPutCycleLength(LocalDateTime eventTime) {
        if (phaseCycleMOEVO == null || cycle == null) {
            cycle = new MOECoorCycle(eventTime, phase.getPhaseNum());
            phaseCycleMOEVO = new PhaseCycleMOEVO();
            return;
        }
        cycle.endCycle(eventTime);
        phaseCycleMOEVO.setProgrammedLength(getPhaseProgrammedLength());
        phaseCycleMOEVO.setPhase(phase.getPhaseNum());
        phaseCycleMOEVO.setActualLength((int) cycle.cycleDuration(false).toSeconds());
        phaseCycleMOEVO.setFromTime(cycle.getBeginCycleTime());
        phaseCycleMOEVO.setToTime(cycle.getEndCycleTime());
    }

    private int getPhaseProgrammedLength() {
        if (pattern == Plan.UNKNOWN_PLAN || pattern == Plan.FREE_PLAN) {
            return 0;
        }
        int[] splitTimesOfPattern = configVO.getPatternInfo().getSplitTimeOfPattern(pattern - 1);
        return splitTimesOfPattern[phase.getPhaseNum() - 1];
    }

}
