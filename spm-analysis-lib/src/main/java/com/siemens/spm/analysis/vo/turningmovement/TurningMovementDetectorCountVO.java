package com.siemens.spm.analysis.vo.turningmovement;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
public class TurningMovementDetectorCountVO {
    @JsonProperty("det_key")
    private Integer detectorKey;
    @JsonProperty("det_value")
    private Integer detectorValue;
}
