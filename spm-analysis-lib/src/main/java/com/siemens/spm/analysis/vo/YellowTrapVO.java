package com.siemens.spm.analysis.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.util.Pair;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class YellowTrapVO extends BaseChartVO implements Serializable {

    private static final long serialVersionUID = -5798296934640776688L;

    @JsonProperty("pairs_of_phases")
    private transient Map<String, List<Pair<Long, Long>>> pairsOfPhases;

    @JsonProperty("events")
    private List<YellowTrapEventVO> events;

}
