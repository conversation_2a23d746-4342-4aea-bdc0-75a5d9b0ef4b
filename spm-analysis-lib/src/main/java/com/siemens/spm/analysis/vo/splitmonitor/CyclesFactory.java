package com.siemens.spm.analysis.vo.splitmonitor;

import com.siemens.spm.perflog.vo.PerfLogEventVO;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.COORD_PATTERN_CHANGE;
import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.PHASE_BEGIN_GREEN;
import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.PHASE_BEGIN_RED_CLEARANCE;
import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.PHASE_BEGIN_YELLOW_CLEARANCE;
import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.PHASE_END_YELLOW_CLEARANCE;
import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.PHASE_FORCE_OFF;
import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.PHASE_GAP_OUT;
import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.PHASE_GREEN_TERMINATION;
import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.PHASE_MAX_OUT;

/**
 * <AUTHOR> Admin
 * @version : 1.0
 * @since : 2/7/2025
 **/
public class CyclesFactory {

    private final List<PerfLogEventVO.Event> SPLIT_MONITOR_EVENT_FILTERS = List.of(
            COORD_PATTERN_CHANGE, // 131
            PHASE_GAP_OUT, // 4
            PHASE_MAX_OUT, // 5
            PHASE_FORCE_OFF, // 6
            PHASE_BEGIN_GREEN,  // 1
            PHASE_GREEN_TERMINATION, // 7
            PHASE_BEGIN_YELLOW_CLEARANCE, // 8
            PHASE_END_YELLOW_CLEARANCE, // 9
            PHASE_BEGIN_RED_CLEARANCE // 10
    );

    public List<CycleResult> calculateCycle(List<PerfLogEventVO> events) {
        List<CycleBoundary> boundaries = identifyCycleBoundaries(events);
        List<CycleEvent> cycleEvents = associateEventsWithCycles(events, boundaries);
        return calculateCycleLengths(cycleEvents);
    }

    public List<CycleBoundary> identifyCycleBoundaries(List<PerfLogEventVO> events) {

        List<PerfLogEventVO> greenStartEvents = events.stream()
                .filter(e -> PHASE_BEGIN_GREEN.equals(e.getEvent()))
                .sorted()
                .toList();

        List<CycleBoundary> boundaries = new ArrayList<>();

        Map<Long, List<PerfLogEventVO>> eventsPerPhase = greenStartEvents.stream()
                .collect(Collectors.groupingBy(PerfLogEventVO::getParameter));

        for (Map.Entry<Long, List<PerfLogEventVO>> entry : eventsPerPhase.entrySet()) {
            Long phase = entry.getKey();
            List<PerfLogEventVO> phaseEvents = entry.getValue();

            phaseEvents.sort(Comparator.comparing(PerfLogEventVO::getDateTime));

            for (int i = 0; i < phaseEvents.size(); i++) {
                PerfLogEventVO current = phaseEvents.get(i);
                LocalDateTime nextCycleStart = (i + 1 < phaseEvents.size()) ?
                        phaseEvents.get(i + 1).getDateTime() : null;

                boundaries.add(new CycleBoundary(phase, current.getDateTime(), nextCycleStart));
            }
        }

        return boundaries;
    }

    public List<CycleEvent> associateEventsWithCycles(List<PerfLogEventVO> events,
                                                      List<CycleBoundary> boundaries) {

        List<PerfLogEventVO> relevantEvents = events.stream()
                .filter(e -> SPLIT_MONITOR_EVENT_FILTERS.contains(e.getEvent()))
                .toList();

        List<CycleEvent> cycleEvents = new ArrayList<>();

        for (PerfLogEventVO event : relevantEvents) {
            for (CycleBoundary boundary : boundaries) {
                if (event.getParameter() == boundary.getParam() && boundary.isEventInCycle(event.getDateTime())) {
                    cycleEvents.add(new CycleEvent(
                            event.getParameter(),
                            event.getEvent().getEventNum(),
                            event.getDateTime(),
                            boundary.getCycleStart(),
                            boundary.getNextCycleStart()
                    ));
                    break;
                }
            }
        }

        return cycleEvents;
    }

    public List<CycleResult> calculateCycleLengths(List<CycleEvent> cycleEvents) {

        Map<Long, Map<LocalDateTime, List<CycleEvent>>> groupedEvents = cycleEvents.stream()
                .collect(Collectors.groupingBy(
                        CycleEvent::param,
                        Collectors.groupingBy(CycleEvent::nextCycleStart)
                ));

        List<CycleResult> results = new ArrayList<>();

        for (Map.Entry<Long, Map<LocalDateTime, List<CycleEvent>>> paramEntry : groupedEvents.entrySet()) {
            Long param = paramEntry.getKey();

            for (Map.Entry<LocalDateTime, List<CycleEvent>> cycleEntry : paramEntry.getValue().entrySet()) {
                LocalDateTime nextCycleStart = cycleEntry.getKey();
                List<CycleEvent> eventsInCycle = cycleEntry.getValue();

                LocalDateTime greenStart = findMaxDatetimeForEvent(eventsInCycle, PHASE_BEGIN_GREEN);
                LocalDateTime greenEnd = findMaxDatetimeForEvent(eventsInCycle, PHASE_GREEN_TERMINATION);
                LocalDateTime yellowStart = findMaxDatetimeForEvent(eventsInCycle, PHASE_BEGIN_YELLOW_CLEARANCE);
                LocalDateTime yellowEnd = findMaxDatetimeForEvent(eventsInCycle, PHASE_END_YELLOW_CLEARANCE);
                LocalDateTime redStart = findMaxDatetimeForEvent(eventsInCycle, PHASE_BEGIN_RED_CLEARANCE);

                CycleResult result = new CycleResult(param, greenStart, greenEnd,
                        yellowStart, yellowEnd, redStart, nextCycleStart);

                if (result.getRedEnd() != null) {
                    results.add(result);
                }
            }
        }

        // Sort by green_start, param (ORDER BY clause)
        results.sort(Comparator.comparing(CycleResult::getGreenStart)
                .thenComparing(CycleResult::getPhase));

        return results;
    }

    private LocalDateTime findMaxDatetimeForEvent(List<CycleEvent> events, PerfLogEventVO.Event eventType) {
        return events.stream()
                .filter(e -> e.eventNum() == eventType.getEventNum())
                .map(CycleEvent::datetime)
                .max(LocalDateTime::compareTo)
                .orElse(null);
    }

}
