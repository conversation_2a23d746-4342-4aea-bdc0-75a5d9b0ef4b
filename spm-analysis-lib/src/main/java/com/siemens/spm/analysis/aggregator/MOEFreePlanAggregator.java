package com.siemens.spm.analysis.aggregator;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.siemens.spm.analysis.vo.Plan;
import com.siemens.spm.analysis.vo.moe.MOEAnalysisFreePlanDetailViewVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO.Event;

import lombok.extern.slf4j.Slf4j;

/**
 * Aggregator for events during free plan.
 */
@Slf4j
public class MOEFreePlanAggregator {

    private int currentPattern = -1;

    // phase number -> aggregator
    private final Map<Integer, MOEFreePhaseAggregator> freePhaseTrackerAggregatorMap;

    private final List<MOEAnalysisFreePlanDetailViewVO> moeAnalysisDetailViewVOList;

    public MOEFreePlanAggregator() {
        this.moeAnalysisDetailViewVOList = new ArrayList<>();
        this.freePhaseTrackerAggregatorMap = new HashMap<>();
    }

    public List<MOEAnalysisFreePlanDetailViewVO> getMoeAnalysisDetailViewVOList() {
        this.moeAnalysisDetailViewVOList.sort((Comparator.comparing(MOEAnalysisFreePlanDetailViewVO::getFromTime)));
        return this.moeAnalysisDetailViewVOList;
    }

    public void putEvent(PerfLogEventVO eventVO) {
        PerfLogEventVO.Event event = eventVO.getEvent();

        if (event == Event.COORD_PATTERN_CHANGE) {
            int newPattern = (int) eventVO.getParameter();
            if (currentPattern == Plan.FREE_PLAN && newPattern != Plan.FREE_PLAN) {
                // close the current cycle when free -> non-free plan
                freePhaseTrackerAggregatorMap.values().forEach(aggregator -> {
                    aggregator.putEvent(eventVO);
                });
            }
            this.currentPattern = newPattern;
            return;
        }

        if (this.currentPattern != Plan.FREE_PLAN) {
            // ignore all other events during non-free plan
            return;
        }

        switch (event) {
            case PHASE_BEGIN_GREEN,
                 PHASE_END_RED_CLEARANCE,
                 PHASE_FORCE_OFF,
                 PHASE_GAP_OUT,
                 PHASE_MAX_OUT,
                 PEDESTRIAN_CALL_REGISTERED -> {
                 int currentPhase = (int) eventVO.getParameter();
                 freePhaseTrackerAggregatorMap
                    .computeIfAbsent(currentPhase, k -> 
                        new MOEFreePhaseAggregator(currentPhase, moeAnalysisDetailViewVOList))
                    .putEvent(eventVO);
            }

            default -> handleTspAndPreEvent(eventVO);
        }
    }

    private void handleTspAndPreEvent(PerfLogEventVO eventVO) {
        freePhaseTrackerAggregatorMap.forEach((phaseNum, phaseAgg) -> {
           phaseAgg.putEvent(eventVO);
        });
    }

}
