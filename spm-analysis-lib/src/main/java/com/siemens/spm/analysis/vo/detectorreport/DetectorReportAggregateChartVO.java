package com.siemens.spm.analysis.vo.detectorreport;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.analysis.vo.BaseChartVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@SuperBuilder
public class DetectorReportAggregateChartVO extends BaseChartVO {

    @JsonProperty("detail_chart")
    private List<DetectorReportChartVO> detailChart;

    @JsonProperty("aggregate_chart")
    private DetectorReportChartVO aggregateChart;
}
