/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : PhaseEventAggregator.java
 * Project     : SPM Platform
 */
package com.siemens.spm.analysis.aggregator;

import com.siemens.spm.analysis.domain.Phase;
import com.siemens.spm.common.shared.domaintype.alarm.trafficmetric.MetricSpecifier;
import com.siemens.spm.common.shared.domaintype.alarm.trafficmetric.TrafficMetric;
import com.siemens.spm.perflog.vo.IntersectionConfigVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;
import lombok.Getter;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * Aggregate phase events and update phase state
 */
@Getter
public class PhaseEventAggregator implements PerfLogEventAggregator {

    protected Phase phase;

    public PhaseEventAggregator() {
        init(Phase.UNSPECIFIED_PHASE_NUM);
    }

    public PhaseEventAggregator(int phaseNum) {
        init(phaseNum);
    }

    /**
     * Unused
     */
    @Override
    public void setFromTime(LocalDateTime fromTime) {
        // this function is unused
    }

    /**
     * Unused
     */
    @Override
    public void setToTime(LocalDateTime toTime) {
        // this function is unused
    }

    /**
     * Will reset phase state if phaseNum is different from initialized Phase
     */
    @Override
    public void setTargetIdentifier(String targetIdentifier) {
        int phaseNum = Phase.UNSPECIFIED_PHASE_NUM;
        try {
            phaseNum = Integer.parseInt(targetIdentifier);
        } catch (NumberFormatException e) {
            // Use Phase.UNSPECIFIED_PHASE_NUM
        }

        phase = new Phase(phaseNum);
    }

    /**
     * Unused
     */
    @Override
    public void setBinSize(int binSize) {
        // this function is unused
    }

    /**
     * Unused
     */
    @Override
    public void setConfig(IntersectionConfigVO configVO) {
        // this function is unused
    }

    /**
     * {@inheritDoc}<br/> Accepting events:<br/>
     * <ul>
     * <li>PHASE_ON(0)</li>
     * <li>PHASE_BEGIN_GREEN(1)</li>
     * <li>PHASE_CHECK(2)</li>
     * <li>PHASE_MIN_COMPLETE(3)</li>
     * <li>PHASE_GAP_OUT(4)</li>
     * <li>PHASE_MAX_OUT(5)</li>
     * <li>PHASE_FORCE_OFF(6)</li>
     * <li>PHASE_GREEN_TERMINATION(7)</li>
     * <li>PHASE_BEGIN_YELLOW_CLEARANCE(8)</li>
     * <li>PHASE_END_YELLOW_CLEARANCE(9)</li>
     * <li>PHASE_BEGIN_RED_CLEARANCE(10)</li>
     * <li>PHASE_END_RED_CLEARANCE(11)</li>
     * <li>PHASE_INACTIVE(12)</li>
     * <li>PHASE_SKIP(20)</li>
     * </ul>
     */
    @Override
    public void putEvent(PerfLogEventVO eventVO) {
        if (eventVO == null)
            return;
        if (eventVO.isPhaseEvent()) {
            // Skip irrelevant phase event
            if (phase.getPhaseNum() != Phase.UNSPECIFIED_PHASE_NUM && phase.getPhaseNum() != eventVO.getParameter()) {
                return;
            }

            phase.updateState(eventVO);
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<TrafficMetric> getSupportedMetric() {
        return Collections.emptyList();
    }

    /**
     * No metric is supported
     */
    @Override
    public Double getAggregatedValue(MetricSpecifier metricSpecifier) {
        return null;
    }

    protected void init(int phaseNum) {
        this.phase = new Phase(phaseNum);
    }

}
