package com.siemens.spm.analysis.vo.detectorreport;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.analysis.algorithm.ReportMetricEvaluation;
import com.siemens.spm.analysis.domain.DetectorMetric;
import com.siemens.spm.analysis.domain.DetectorMetricCondition;
import com.siemens.spm.analysis.domain.DetectorOperator;
import com.siemens.spm.common.constant.TimeConstants;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Data
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
@Slf4j
@SuppressWarnings("all")
public class DetectorPhaseDataVO {

    public static final int WEEK_LENGTH = 7;

    @JsonIgnore
    private int phase;

    @JsonProperty("total_activation")
    private Long totalActivation;

    @JsonProperty("occupancy")
    private Double occupancy;

    @JsonProperty("avg_volume")
    private Double avgVolume;

    @JsonProperty("is_total_act_satisfy_condition")
    private boolean isTotalActSatisfyCondition = false;

    @JsonProperty("is_occupancy_satisfy_condition")
    private boolean isOccupancySatisfyCondition = false;

    @JsonProperty("is_avg_volume_satisfy_condition")
    private boolean isAvgVolumeSatisfyCondition = false;

    @JsonIgnore
    private List<Double> occupancyTime;

    @JsonIgnore
    private List<Double> cycleLength;

    @JsonIgnore
    private List<Long> totalActivationList;

    @JsonIgnore
    private List<Double> avgVolumeList;

    public DetectorPhaseDataVO(int phase) {
        this.phase = phase;
    }

    public void increaseActivation() {
        if (this.totalActivation == null) {
            this.totalActivation = 0L;
        }
        this.totalActivation++;
    }

    /**
     * Calculates the occupancy percentage based on detector activation times.
     *
     * <p>Occupancy is defined as the percentage of time that a detector is active (occupied)
     * relative to the total time period. This is a key metric for traffic signal performance
     * as it indicates how busy a particular detector is during the analysis period.</p>
     *
     * <p>The calculation formula is: (sum of occupancy times / total time period) * 100</p>
     *
     * @param fromTime The start time of the analysis period
     * @param toTime   The end time of the analysis period
     */
    public void calculateOccupancy(LocalDateTime fromTime, LocalDateTime toTime) {
        log.debug("Calculating occupancy from {} to {}", fromTime, toTime);

        // Validate inputs
        if (!isValidInputForCalculation(fromTime, toTime)) {
            return;
        }

        // Calculate total time period
        double totalTimePeriod = calculateTotalTimePeriod(fromTime, toTime);
        if (totalTimePeriod <= 0) {
            log.warn("Total time period is zero or negative: {}", totalTimePeriod);
            this.occupancy = 0.0;
            return;
        }

        // Calculate total occupancy time
        double totalOccupancyTime = calculateTotalOccupancyTime();

        // Calculate occupancy percentage
        this.occupancy = calculateOccupancyPercentage(totalOccupancyTime, totalTimePeriod);

        log.debug("Calculated occupancy: {}%, total occupancy time: {} seconds, total time period: {} seconds",
                this.occupancy, totalOccupancyTime, totalTimePeriod);
    }

    /**
     * Validates the input parameters and data for occupancy calculation.
     *
     * @param fromTime The start time of the analysis period
     * @param toTime   The end time of the analysis period
     * @return true if inputs are valid, false otherwise
     */
    private boolean isValidInputForCalculation(LocalDateTime fromTime, LocalDateTime toTime) {
        if (this.occupancyTime == null) {
            log.warn("Cannot calculate occupancy: occupancyTime list is null");
            return false;
        }

        if (this.cycleLength == null) {
            log.warn("Cannot calculate occupancy: cycleLength list is null");
            return false;
        }

        if (fromTime == null || toTime == null) {
            log.warn("Cannot calculate occupancy: fromTime or toTime is null");
            return false;
        }

        if (fromTime.isAfter(toTime)) {
            log.warn("Cannot calculate occupancy: fromTime {} is after toTime {}", fromTime, toTime);
            return false;
        }

        return true;
    }

    /**
     * Calculates the total time period for occupancy calculation.
     * Uses cycle lengths if available, otherwise calculates from time range.
     *
     * @param fromTime The start time of the analysis period
     * @param toTime   The end time of the analysis period
     * @return The total time period in seconds
     */
    private double calculateTotalTimePeriod(LocalDateTime fromTime, LocalDateTime toTime) {
        if (CollectionUtils.isEmpty(cycleLength)) {
            Duration duration = Duration.between(fromTime, toTime);
            double totalSeconds = duration.getSeconds();
            log.debug("Using time range for total period calculation: {} seconds", totalSeconds);
            return totalSeconds;
        } else {
            double totalCycleLength = cycleLength.stream()
                    .filter(Objects::nonNull)
                    .mapToDouble(Double::doubleValue)
                    .sum();
            log.debug("Using cycle lengths for total period calculation: {} seconds", totalCycleLength);
            return totalCycleLength;
        }
    }

    /**
     * Calculates the total occupancy time by summing all recorded occupancy times.
     *
     * @return The total occupancy time in seconds
     */
    private double calculateTotalOccupancyTime() {
        return occupancyTime.stream()
                .filter(Objects::nonNull)
                .mapToDouble(Double::doubleValue)
                .sum();
    }

    /**
     * Calculates the occupancy percentage based on total occupancy time and total time period.
     *
     * @param totalOccupancyTime The total time the detector was occupied (in seconds)
     * @param totalTimePeriod    The total time period for analysis (in seconds)
     * @return The occupancy percentage (0-100)
     */
    private double calculateOccupancyPercentage(double totalOccupancyTime, double totalTimePeriod) {
        if (totalTimePeriod <= 0) {
            return 0.0;
        }

        double percentage = (totalOccupancyTime / totalTimePeriod) * 100;

        // Ensure the percentage is within valid range (0-100)
        return Math.min(100.0, Math.max(0.0, percentage));
    }

    /**
     * Calculates the average volume (traffic flow rate) based on detector activations.
     *
     * <p>Average volume is defined as the number of detector activations per hour
     * over the specified time period. This metric represents the traffic flow rate
     * and is a key indicator for traffic signal performance analysis.</p>
     *
     * <p>The calculation formula is: total activations / time period in hours</p>
     *
     * @param fromTime The start time of the analysis period
     * @param toTime   The end time of the analysis period
     */
    public void calculateAvgVolumeByHour(LocalDateTime fromTime, LocalDateTime toTime) {
        log.debug("Calculating average volume from {} to {}", fromTime, toTime);

        // Validate inputs
        if (!isValidInputForVolumeCalculation(fromTime, toTime)) {
            return;
        }

        // Calculate time duration in hours
        double durationHours = calculateDurationInHours(fromTime, toTime);
        if (durationHours <= 0) {
            log.warn("Duration is zero or negative: {} hours", durationHours);
            this.avgVolume = 0.0;
            return;
        }

        // Calculate average volume
        this.avgVolume = this.totalActivation / durationHours;

        log.debug("Calculated average volume: {} activations/hour, total activations: {}, duration: {} hours",
                this.avgVolume, this.totalActivation, durationHours);
    }

    /**
     * Validates the input parameters for average volume calculation.
     *
     * @param fromTime The start time of the analysis period
     * @param toTime   The end time of the analysis period
     * @return true if inputs are valid, false otherwise
     */
    private boolean isValidInputForVolumeCalculation(LocalDateTime fromTime, LocalDateTime toTime) {
        if (this.totalActivation == null) {
            log.warn("Cannot calculate average volume: totalActivation is null");
            this.avgVolume = 0.0;
            return false;
        }

        if (fromTime == null || toTime == null) {
            log.warn("Cannot calculate average volume: fromTime or toTime is null");
            this.avgVolume = 0.0;
            return false;
        }

        if (fromTime.isAfter(toTime)) {
            log.warn("Cannot calculate average volume: fromTime {} is after toTime {}", fromTime, toTime);
            this.avgVolume = 0.0;
            return false;
        }

        return true;
    }

    /**
     * Calculates the duration between two timestamps in hours with high precision.
     * For very small time periods, uses a minimum threshold to avoid division by zero.
     *
     * @param fromTime The start time of the analysis period
     * @param toTime   The end time of the analysis period
     * @return The duration in hours (as a decimal value)
     */
    private double calculateDurationInHours(LocalDateTime fromTime, LocalDateTime toTime) {
        return Duration.between(fromTime, toTime).toHours();
    }

    public void addOccupancyTime(Double occupancy) {
        if (this.occupancyTime == null) {
            this.occupancyTime = new ArrayList<>();
        }
        this.occupancyTime.add(occupancy);
    }

    public void addOccupancyTime(List<Double> occupancies) {
        if (this.occupancyTime == null) {
            this.occupancyTime = new ArrayList<>();
        }
        this.occupancyTime.addAll(occupancies);
    }

    public void addTotalActivation(Long totalActivation) {
        if (this.totalActivationList == null) {
            this.totalActivationList = new ArrayList<>();
        }
        this.totalActivationList.add(totalActivation);
    }

    public void addAvgVolume(Double avgVolume) {
        if (this.avgVolumeList == null) {
            this.avgVolumeList = new ArrayList<>();
        }
        this.avgVolumeList.add(avgVolume);
    }

    public void calculateReportWeekly() {
        log.debug("Start calculate report weekly");
        this.totalActivation = this.totalActivationList.stream()
                .filter(Objects::nonNull)
                .mapToLong(Long::longValue)
                .sum();

        this.avgVolume = this.totalActivationList.stream()
                .filter(Objects::nonNull)
                .mapToDouble(Long::doubleValue)
                .sum();

        this.occupancy = this.occupancyTime.stream()
                .filter(Objects::nonNull)
                .mapToDouble(Double::doubleValue)
                .average()
                .orElse(0);
    }

    public void summaryAndCheckCondition(List<DetectorMetric> detectorMetrics,
                                         String aggregationType,
                                         long numberOfDays, LocalTime fromTime, LocalTime toTime) {
        log.debug("start calculate total reports and check metric condition");
        long numberOfHours = Duration.between(fromTime, toTime).toHours();
        for (DetectorMetric detectorMetric : detectorMetrics) {
            DetectorMetric.Metric metric = DetectorMetric.Metric.resolve(detectorMetric.getKeyName(),
                    detectorMetric.getUnit());
            if (!detectorMetric.isEnabled() || metric == null) {
                continue;
            }
            switch (metric) {
                case OCCUPANCY -> {
                    calculateAndCheckOccupancy(detectorMetric, numberOfDays, numberOfHours);
                }
                case VOLUME -> calculateAndCheckAvgVolume(aggregationType, detectorMetric, numberOfDays, numberOfHours);
                case ACTIVATION -> calculateAndCheckTotalActivation(detectorMetric);
                default -> {
                    //not use
                }
            }
        }
    }

    public boolean checkMetricCondition(DetectorMetric detectorMetric, Double calculatedValue) {
        DetectorMetricCondition metricCondition = detectorMetric.getCondition();
        if (Objects.isNull(metricCondition)) {
            return false;
        }
        log.debug("(checkMetricCondition)operator : {}, condition : {}, calculated : {}", metricCondition.getOperator(),
                metricCondition.getValue(), calculatedValue);
        switch (Objects.requireNonNull(DetectorOperator.resolve(metricCondition.getOperator()))) {
            case EQ -> {
                return metricCondition.getValue().equals(calculatedValue);
            }
            case GT -> {
                return calculatedValue > metricCondition.getValue();
            }
            case LT -> {
                return calculatedValue < metricCondition.getValue();
            }
            case GTE -> {
                return calculatedValue >= metricCondition.getValue();
            }
            case LTE -> {
                return calculatedValue <= metricCondition.getValue();
            }
            default -> {
                //not use
            }
        }
        return false;
    }

    public void aggregatePhaseData(LocalDateTime fromTime, LocalDateTime toTime, List<Double> cycleLengths) {
        this.cycleLength = cycleLengths;
        calculateOccupancy(fromTime, toTime);
        calculateAvgVolumeByHour(fromTime, toTime);
    }

    public void aggregateWithData(DetectorPhaseDataVO dailyPhaseData) {
        addTotalActivation(dailyPhaseData.getTotalActivation());
        addOccupancyTime(dailyPhaseData.getOccupancyTime());
        addAvgVolume(dailyPhaseData.getAvgVolume());
    }

    private void calculateAndCheckTotalActivation(DetectorMetric detectorMetric) {
        if (this.totalActivationList != null) {
            this.totalActivation = calculateTotalActivation();

        }
        this.isTotalActSatisfyCondition = ReportMetricEvaluation.evaluateDetectorMetric(detectorMetric,
                Double.valueOf(this.totalActivation));
    }

    private void calculateAndCheckAvgVolume(String aggregationType,
                                            DetectorMetric detectorMetric,
                                            long numberOfDays,
                                            long numberOfHours) {
        if (this.avgVolumeList != null) {
            long totalActivationSum = calculateTotalActivation();
            long delta = 1;
            switch (aggregationType) {
                case "DAILY" -> {
                    numberOfHours = 1;
                    break;
                }
                case "WEEKLY" -> {
                    numberOfHours = 1;
                    delta = WEEK_LENGTH;
                    break;
                }
                default -> {
                    break;
                }
            }
            double avgVolumeSum = Math.round(totalActivationSum / (double) numberOfDays * delta);
            this.avgVolume = (double) Math.round(avgVolumeSum / numberOfHours);
            this.isAvgVolumeSatisfyCondition = ReportMetricEvaluation.evaluateDetectorMetric(detectorMetric,
                    this.avgVolume);
        }
    }

    /**
     * Calculates the occupancy percentage for reporting periods and evaluates it against the detector metric condition.
     *
     * <p>This method computes the average occupancy percentage over a specified reporting period
     * by aggregating occupancy time data across multiple days and hours. The calculation formula is:
     * (total occupancy time in seconds / total time period in seconds) * 100</p>
     *
     * <p>The occupancy percentage represents the proportion of time that the detector was active
     * during the analysis period, expressed as a percentage (0-100%).</p>
     *
     * @param detectorMetric The detector metric configuration containing the condition to evaluate against
     * @param numberOfDays   The number of days in the reporting period (must be positive)
     * @param numberOfHours  The number of hours per day in the reporting period (must be positive)
     * @throws IllegalArgumentException if numberOfDays or numberOfHours is not positive
     */
    private void calculateAndCheckOccupancy(DetectorMetric detectorMetric, long numberOfDays, long numberOfHours) {
        log.debug("Calculating occupancy for reporting period: {} days, {} hours per day", numberOfDays, numberOfHours);

        if (!isValidReportingPeriod(numberOfDays, numberOfHours)) {
            log.warn("Invalid reporting period parameters: numberOfDays={}, numberOfHours={}", numberOfDays,
                    numberOfHours);
            return;
        }

        if (detectorMetric == null) {
            log.warn("Detector metric is null, cannot calculate occupancy");
            return;
        }

        if (CollectionUtils.isEmpty(this.occupancyTime)) {
            log.debug("No occupancy time data available, setting occupancy to 0.0");
            this.occupancy = 0.0;
            this.isOccupancySatisfyCondition = ReportMetricEvaluation.evaluateDetectorMetric(detectorMetric,
                    this.occupancy);
            return;
        }

        double totalOccupancyTimeSeconds = calculateTotalOccupancyTimeFromList();
        double totalTimePeriodSeconds = calculateReportingPeriodTotalTime(numberOfDays, numberOfHours);

        this.occupancy = calculateReportingOccupancyPercentage(totalOccupancyTimeSeconds, totalTimePeriodSeconds);
        this.isOccupancySatisfyCondition = ReportMetricEvaluation.evaluateDetectorMetric(detectorMetric,
                this.occupancy);

        log.debug(
                "Calculated reporting occupancy: {}%, total occupancy time: {} seconds, total period: {} seconds, condition satisfied: {}",
                this.occupancy, totalOccupancyTimeSeconds, totalTimePeriodSeconds, this.isOccupancySatisfyCondition);
    }

    /**
     * Validates the reporting period parameters.
     *
     * @param numberOfDays  The number of days (must be positive)
     * @param numberOfHours The number of hours (must be positive)
     * @return true if parameters are valid, false otherwise
     */
    private boolean isValidReportingPeriod(long numberOfDays, long numberOfHours) {
        if (numberOfDays <= 0) {
            log.error("Number of days must be positive, got: {}", numberOfDays);
            return false;
        }
        if (numberOfHours <= 0) {
            log.error("Number of hours must be positive, got: {}", numberOfHours);
            return false;
        }
        return true;
    }

    /**
     * Calculates the total occupancy time from the occupancy time list.
     *
     * @return The sum of all non-null occupancy times in seconds
     */
    private double calculateTotalOccupancyTimeFromList() {
        return this.occupancyTime.stream().filter(Objects::nonNull).mapToDouble(Double::doubleValue).sum();
    }

    /**
     * Calculates the total time period for the reporting period in seconds.
     *
     * @param numberOfDays  The number of days in the reporting period
     * @param numberOfHours The number of hours per day
     * @return The total time period in seconds
     */
    private double calculateReportingPeriodTotalTime(long numberOfDays, long numberOfHours) {
        return (double) numberOfDays * numberOfHours * TimeConstants.NUMBER_OF_SECONDS_IN_1_HOUR;
    }

    /**
     * Calculates the occupancy percentage for reporting periods.
     *
     * @param totalOccupancyTimeSeconds The total occupancy time in seconds
     * @param totalTimePeriodSeconds    The total time period in seconds
     * @return The occupancy percentage (0-100), clamped to valid range
     */
    private double calculateReportingOccupancyPercentage(double totalOccupancyTimeSeconds,
                                                         double totalTimePeriodSeconds) {
        if (totalTimePeriodSeconds <= 0) {
            log.warn("Total time period is zero or negative: {}, returning 0.0", totalTimePeriodSeconds);
            return 0.0;
        }

        double percentage = (totalOccupancyTimeSeconds / totalTimePeriodSeconds) * 100.0;
        return Math.min(100.0, Math.max(0.0, percentage));
    }

    public Long calculateTotalActivation() {
        return this.totalActivationList.stream().filter(Objects::nonNull)
                .reduce(0L, Long::sum);
    }

}
