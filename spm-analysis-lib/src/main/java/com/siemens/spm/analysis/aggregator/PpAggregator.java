package com.siemens.spm.analysis.aggregator;

import com.siemens.spm.common.shared.domaintype.alarm.trafficmetric.MetricSpecifier;
import com.siemens.spm.common.shared.domaintype.alarm.trafficmetric.TrafficMetric;
import com.siemens.spm.perflog.vo.IntersectionConfigVO;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * Aggregator used to aggregate preemption/priority events
 *
 * <AUTHOR> Nguyen - <EMAIL>
 */
public abstract class PpAggregator implements PerfLogEventAggregator {

    @Override
    public void setTargetIdentifier(String targetIdentifier) {
        // Do not use
    }

    @Override
    public void setBinSize(int binSize) {
        // Do not use
    }

    @Override
    public void setFromTime(LocalDateTime fromTime) {
        // Do not use
    }

    @Override
    public void setToTime(LocalDateTime toTime) {
        // Do not use
    }

    @Override
    public void setConfig(IntersectionConfigVO configVO) {
        // Do not use
    }

    @Override
    public List<TrafficMetric> getSupportedMetric() {
        return Collections.emptyList();
    }

    @Override
    public Double getAggregatedValue(MetricSpecifier metricSpecifier) {
        return null;
    }

}
