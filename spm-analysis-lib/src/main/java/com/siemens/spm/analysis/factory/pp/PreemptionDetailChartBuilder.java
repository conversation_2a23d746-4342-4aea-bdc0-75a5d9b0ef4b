package com.siemens.spm.analysis.factory.pp;

import com.siemens.spm.analysis.aggregator.PreemptionDetailAggregator;
import com.siemens.spm.analysis.vo.pp.PreemptionDetailChartVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;

import java.time.LocalDateTime;

/**
 * Chart builder for {@link PreemptionDetailChartVO}. Used to build Preemption Detail
 * Chart
 *
 * <AUTHOR> Nguyen - <EMAIL>
 */
public class PreemptionDetailChartBuilder extends PpChartBuilder<PreemptionDetailChartVO> {

    private final PreemptionDetailChartVO chartVO;

    private final PreemptionDetailAggregator aggregator;

    public PreemptionDetailChartBuilder(LocalDateTime fromTime,
                                        LocalDateTime toTime,
                                        int preemptionNumber) {
        chartVO = PreemptionDetailChartVO.builder()
                .fromTime(fromTime)
                .toTime(toTime)
                .number(preemptionNumber)
                .build();

        aggregator = new PreemptionDetailAggregator(preemptionNumber);
    }

    @Override
    public void putEvent(PerfLogEventVO eventVO) {
        if (eventVO != null && eventVO.isPreemptionEvent()) {
            aggregator.putEvent(eventVO);
        }
    }

    @Override
    public PreemptionDetailChartVO build() {
        chartVO.setCycles(aggregator.getCycleVOList());

        return chartVO;
    }

}
