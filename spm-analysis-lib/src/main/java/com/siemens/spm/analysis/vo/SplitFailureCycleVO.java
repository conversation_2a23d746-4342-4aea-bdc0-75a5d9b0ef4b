package com.siemens.spm.analysis.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SplitFailureCycleVO implements DataBlockVO, Serializable {

    private static final long serialVersionUID = 8439664583117360325L;

    // status for List of indicator base on %GOR and %ROR5 values
    public static final int DATA_NULL = 0;
    public static final int SPLIT_FAILURES = 1;
    public static final int EXCELLENT_GREEN_UTILIZATION = 2;
    public static final int POOR_COORDINATION = 3;
    public static final int EXCESS_CAPACITY = 4;
    public static final int OTHER = 5;

    @JsonIgnore
    private double totalGreenTime;
    @JsonIgnore
    private double totalRedTime;
    @JsonIgnore
    private boolean nonData;
    @JsonIgnore
    private double greenTime;
    @JsonIgnore
    private double redTime;

    @JsonProperty("from_time")
    private LocalDateTime fromTime;

    @JsonProperty("to_time")
    private LocalDateTime toTime;

    @JsonProperty("gor")
    public double perGor() {
        return totalGreenTime == 0 ? 0.0 : 100 * greenTime / totalGreenTime;
    }

    @JsonProperty("ror5")
    public double perRor5() {
        return totalRedTime == 0 ? 0 : 100 * redTime / totalRedTime;
    }

    @JsonProperty("indicator")
    public int indicator() {
        double gorPercent = perGor();
        double ror5Percent = perRor5();

        if (gorPercent == 0 && nonData) {
            return DATA_NULL;
        } else if (gorPercent > 80 && ror5Percent > 80) {
            return SPLIT_FAILURES;
        } else if (gorPercent > 80 && ror5Percent < 60) {
            return EXCELLENT_GREEN_UTILIZATION;
        } else if (gorPercent < 60 && ror5Percent > 80) {
            return POOR_COORDINATION;
        } else if (gorPercent < 60 && ror5Percent < 60) {
            return EXCESS_CAPACITY;
        } else {
            return OTHER;
        }
    }

    public void update(LocalDateTime toTime,
                       double totalGreenTime,
                       double totalRedTime,
                       double greenTime,
                       double redTime) {
        this.totalGreenTime = totalGreenTime;
        this.totalRedTime = totalRedTime;
        this.greenTime = greenTime;
        this.redTime = redTime;
        this.toTime = toTime;
    }

    public SplitFailureCycleVO(LocalDateTime fromTime) {
        this.fromTime = fromTime;
    }

}
