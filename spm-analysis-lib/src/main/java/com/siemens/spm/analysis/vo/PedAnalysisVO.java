package com.siemens.spm.analysis.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.siemens.spm.common.shared.domaintype.analysis.AnalysisType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@JsonInclude(Include.NON_NULL)
public class PedAnalysisVO extends AbstractAnalysisVO<PedChartVO> {

    private static final long serialVersionUID = 6248013169960092377L;

    @Override
    protected String getAnalysisType() {
        return AnalysisType.PEDESTRIAN.getId();
    }

}
