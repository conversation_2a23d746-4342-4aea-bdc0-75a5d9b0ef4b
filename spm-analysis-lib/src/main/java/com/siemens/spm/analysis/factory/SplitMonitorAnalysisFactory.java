package com.siemens.spm.analysis.factory;

import com.siemens.spm.analysis.aggregator.SplitFailureAggregator;
import com.siemens.spm.analysis.exception.AnalysisInitializationException;
import com.siemens.spm.analysis.exception.InvalidPerfLogException;
import com.siemens.spm.analysis.vo.splitmonitor.CycleBoundary;
import com.siemens.spm.analysis.vo.splitmonitor.CycleEvent;
import com.siemens.spm.analysis.vo.splitmonitor.CycleResult;
import com.siemens.spm.analysis.vo.splitmonitor.SplitMonitorAnalysisVO;
import com.siemens.spm.analysis.vo.splitmonitor.SplitMonitorChartVO;
import com.siemens.spm.perflog.vo.IntersectionConfigVO;
import com.siemens.spm.perflog.vo.PerfLogBundleVO;
import com.siemens.spm.perflog.vo.PerfLogChunkVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;
import com.siemens.spm.perflog.vo.PerfLogGapVO;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.tuple.Pair;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.COORD_PATTERN_CHANGE;
import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.PHASE_BEGIN_GREEN;
import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.PHASE_BEGIN_RED_CLEARANCE;
import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.PHASE_BEGIN_YELLOW_CLEARANCE;
import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.PHASE_END_YELLOW_CLEARANCE;
import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.PHASE_FORCE_OFF;
import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.PHASE_GAP_OUT;
import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.PHASE_GREEN_TERMINATION;
import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.PHASE_MAX_OUT;

/**
 * During FREE_PLAN, a phase is considered as skipped if the duration between 2 consecutive greens > 900 seconds.
 *
 * @see SplitFailureAggregator#putEvent List of events processed
 */
public class SplitMonitorAnalysisFactory extends AbstractAnalysisFactory<SplitMonitorAnalysisVO, SplitMonitorChartVO> {

    // Events used for split monitor analysis
    // TODO: It will be supported in the future when request to datasource
    private final List<PerfLogEventVO.Event> SPLIT_MONITOR_EVENT_FILTERS = List.of(
            COORD_PATTERN_CHANGE, // 131
            PHASE_GAP_OUT, // 4
            PHASE_MAX_OUT, // 5
            PHASE_FORCE_OFF, // 6
            PHASE_BEGIN_GREEN,  // 1
            PHASE_GREEN_TERMINATION, // 7
            PHASE_BEGIN_YELLOW_CLEARANCE, // 8
            PHASE_END_YELLOW_CLEARANCE, // 9
            PHASE_BEGIN_RED_CLEARANCE // 10
    );

    @Getter
    @Setter
    protected Long initialPattern;

    @Override
    public SplitMonitorAnalysisVO createAnalysis(LocalDateTime fromTime,
                                                 LocalDateTime toTime,
                                                 PerfLogBundleVO perfLogBundleVO,
                                                 List<PerfLogGapVO> perfLogGapVOList)
            throws InvalidPerfLogException, AnalysisInitializationException {
        return createAnalysis(fromTime, toTime,
                perfLogBundleVO, perfLogGapVOList,
                SplitMonitorAnalysisVO.class, SplitMonitorChartVO.class);
    }

    @Override
    protected void buildCharts(LocalDateTime fromTime,
                               LocalDateTime toTime,
                               PerfLogBundleVO perfLogBundleVO,
                               Class<SplitMonitorChartVO> splitMonitorChartVOClass)
            throws InvalidPerfLogException {
        List<PerfLogChunkVO> perfLogChunkVOList = perfLogBundleVO.getPerfLogChunks();
        Map<String, IntersectionConfigVO> intConfigMap = perfLogBundleVO.getIntConfigs();
        if (perfLogChunkVOList == null || intConfigMap == null) {
            throw new InvalidPerfLogException("Missing PerfLog chunk or intersection config");
        }

        SplitMonitorChartVO chartVO = new SplitMonitorChartVO();
        chartVO.setFromTime(fromTime);
        chartVO.setToTime(toTime);

        List<PerfLogGapVO> perfLogGaps = analysisVO.getPerfLogGapList();
        List<Pair<LocalDateTime, LocalDateTime>> gapsRanges = perfLogGaps.stream()
                .map(g -> Pair.of(g.getFromTime(), g.getToTime())).toList();

        SplitMonitorChartBuilder builder = new SplitMonitorChartBuilder(chartVO, initialPattern, gapsRanges);
        for (PerfLogChunkVO chunkVO : perfLogChunkVOList) {
            IntersectionConfigVO intConfigVO = intConfigMap.get(chunkVO.getConfigID());
            if (intConfigVO == null) {
                throw new InvalidPerfLogException("Missing intersection config for chunk " + chunkVO.getConfigID());
            }
            builder.setIntersectionConfig(intConfigVO);
            List<PerfLogEventVO> filterEvents = chunkVO.getPerfLogEvents().stream()
                    .filter(p -> SPLIT_MONITOR_EVENT_FILTERS.contains(p.getEvent())).toList();
            for (PerfLogEventVO perfLogEvent : filterEvents) {
                if (!isEventInGap(perfLogEvent, perfLogGaps)) {
                    builder.putEvent(perfLogEvent);
                }

            }
        }
        builder.build();

        analysisVO.addChart(chartVO);
    }

    private boolean isEventInGap(PerfLogEventVO event, List<PerfLogGapVO> gaps) {
        return gaps.stream()
                .anyMatch(gap -> event.getDateTime().isAfter(gap.getFromTime()) &&
                        event.getDateTime().isBefore(gap.getToTime()));
    }

    public List<CycleBoundary> identifyCycleBoundaries(List<PerfLogEventVO> events,
                                                       LocalDateTime startTime,
                                                       LocalDateTime endTime) {

        List<PerfLogEventVO> greenStartEvents = events.stream()
                .filter(e -> PHASE_BEGIN_GREEN.equals(e.getEvent()))
                .filter(e -> !e.getDateTime().isBefore(startTime) && !e.getDateTime().isAfter(endTime))
                .sorted()
                .toList();

        List<CycleBoundary> boundaries = new ArrayList<>();

        Map<Long, List<PerfLogEventVO>> eventsPerPhase = greenStartEvents.stream()
                .collect(Collectors.groupingBy(PerfLogEventVO::getParameter));

        for (Map.Entry<Long, List<PerfLogEventVO>> entry : eventsPerPhase.entrySet()) {
            Long phase = entry.getKey();
            List<PerfLogEventVO> phaseEvents = entry.getValue();

            phaseEvents.sort(Comparator.comparing(PerfLogEventVO::getDateTime));

            for (int i = 0; i < phaseEvents.size(); i++) {
                PerfLogEventVO current = phaseEvents.get(i);
                LocalDateTime nextCycleStart = (i + 1 < phaseEvents.size()) ?
                        phaseEvents.get(i + 1).getDateTime() : null;

                boundaries.add(new CycleBoundary(phase, current.getDateTime(), nextCycleStart));
            }
        }

        return boundaries;
    }

    public List<CycleEvent> associateEventsWithCycles(List<PerfLogEventVO> events,
                                                      List<CycleBoundary> boundaries,
                                                      LocalDateTime startTime,
                                                      LocalDateTime endTime) {

        List<PerfLogEventVO> relevantEvents = events.stream()
                .filter(e -> SPLIT_MONITOR_EVENT_FILTERS.contains(e.getEvent()))
                .filter(e -> !e.getDateTime().isBefore(startTime) && !e.getDateTime().isAfter(endTime))
                .toList();

        List<CycleEvent> cycleEvents = new ArrayList<>();

        for (PerfLogEventVO event : relevantEvents) {
            for (CycleBoundary boundary : boundaries) {
                if (event.getParameter() == boundary.getParam() && boundary.isEventInCycle(event.getDateTime())) {
                    cycleEvents.add(new CycleEvent(
                            event.getParameter(),
                            event.getEvent().getEventNum(),
                            event.getDateTime(),
                            boundary.getCycleStart(),
                            boundary.getNextCycleStart()
                    ));
                    break;
                }
            }
        }

        return cycleEvents;
    }

    public List<CycleResult> calculateCycleLengths(List<CycleEvent> cycleEvents) {

        Map<Long, Map<LocalDateTime, List<CycleEvent>>> groupedEvents = cycleEvents.stream()
                .collect(Collectors.groupingBy(
                        CycleEvent::param,
                        Collectors.groupingBy(CycleEvent::nextCycleStart)
                ));

        List<CycleResult> results = new ArrayList<>();

        for (Map.Entry<Long, Map<LocalDateTime, List<CycleEvent>>> paramEntry : groupedEvents.entrySet()) {
            Long param = paramEntry.getKey();

            for (Map.Entry<LocalDateTime, List<CycleEvent>> cycleEntry : paramEntry.getValue().entrySet()) {
                LocalDateTime nextCycleStart = cycleEntry.getKey();
                List<CycleEvent> eventsInCycle = cycleEntry.getValue();

                LocalDateTime greenStart = findMaxDatetimeForEvent(eventsInCycle, PHASE_BEGIN_GREEN);
                LocalDateTime greenEnd = findMaxDatetimeForEvent(eventsInCycle, PHASE_GREEN_TERMINATION);
                LocalDateTime yellowStart = findMaxDatetimeForEvent(eventsInCycle, PHASE_BEGIN_YELLOW_CLEARANCE);
                LocalDateTime yellowEnd = findMaxDatetimeForEvent(eventsInCycle, PHASE_END_YELLOW_CLEARANCE);
                LocalDateTime redStart = findMaxDatetimeForEvent(eventsInCycle, PHASE_BEGIN_RED_CLEARANCE);

                CycleResult result = new CycleResult(param, greenStart, greenEnd,
                        yellowStart, yellowEnd, redStart, nextCycleStart);

                if (result.getRedEnd() != null) {
                    results.add(result);
                }
            }
        }

        // Sort by green_start, param (ORDER BY clause)
        results.sort(Comparator.comparing(CycleResult::getGreenStart)
                .thenComparing(CycleResult::getPhase));

        return results;
    }

    /**
     * Helper method to find max datetime for a specific event type
     * Simulates MAX(CASE WHEN event_description = 'X' THEN datetime END)
     */
    private LocalDateTime findMaxDatetimeForEvent(List<CycleEvent> events, PerfLogEventVO.Event eventDescription) {
        return events.stream()
                .filter(e -> e.getEventDescription().equals(eventDescription))
                .map(CycleEvent::datetime)
                .max(LocalDateTime::compareTo)
                .orElse(null);
    }

}
