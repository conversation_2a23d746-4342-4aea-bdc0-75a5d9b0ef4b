package com.siemens.spm.analysis.factory.redlightviolation;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.siemens.spm.analysis.exception.AnalysisInitializationException;
import com.siemens.spm.analysis.exception.InvalidPerfLogException;
import com.siemens.spm.analysis.factory.AbstractAnalysisFactory;
import com.siemens.spm.analysis.factory.topology.DetTypeUsed;
import com.siemens.spm.analysis.util.IntersectionConfigUtils;
import com.siemens.spm.analysis.vo.redlightviolation.RedLightViolationAnalysisVO;
import com.siemens.spm.analysis.vo.redlightviolation.RedLightViolationChartVO;
import com.siemens.spm.perflog.vo.DetectorVO.DetectorType;
import com.siemens.spm.perflog.vo.IntersectionConfigVO;
import com.siemens.spm.perflog.vo.PerfLogBundleVO;
import com.siemens.spm.perflog.vo.PerfLogChunkVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;
import com.siemens.spm.perflog.vo.PerfLogGapVO;

@DetTypeUsed({ DetectorType.RED_LIGHT_VIOLATION, DetectorType.STOP_BAR })
public class RedLightViolationAnalysisFactory
        extends AbstractAnalysisFactory<RedLightViolationAnalysisVO, RedLightViolationChartVO> {

    public static final int BIN_SIZE_900 = 900;

    private final int binSize;

    public RedLightViolationAnalysisFactory(int binSize) {
        this.binSize = binSize;
    }

    public RedLightViolationAnalysisVO createAnalysis(LocalDateTime fromTime,
                                                      LocalDateTime toTime,
                                                      PerfLogBundleVO perfLogBundleVO,
                                                      List<PerfLogGapVO> perfLogGapVOList)
            throws InvalidPerfLogException, AnalysisInitializationException {
        return createAnalysis(fromTime, toTime,
                perfLogBundleVO, perfLogGapVOList,
                RedLightViolationAnalysisVO.class,
                RedLightViolationChartVO.class);
    }

    @Override
    protected void buildCharts(LocalDateTime fromTime,
                               LocalDateTime toTime,
                               PerfLogBundleVO perfLogBundleVO,
                               Class<RedLightViolationChartVO> redLightViolationChartVOClass)
            throws InvalidPerfLogException {
        if (analysisVO == null || perfLogBundleVO == null) {
            throw new IllegalArgumentException();
        }

        Map<String, IntersectionConfigVO> intConfigVOMap = perfLogBundleVO.getIntConfigs();
        if (intConfigVOMap == null) {
            throw new InvalidPerfLogException("No intersection config");
        }

        List<PerfLogChunkVO> perfLogChunkVOList = perfLogBundleVO.getPerfLogChunks();
        if (perfLogChunkVOList == null) {
            throw new InvalidPerfLogException("No PerfLog chunk");
        }

        Set<String> directions = IntersectionConfigUtils.scanDirections(intConfigVOMap.values());

        List<RedLightViolationChartBuilder> chartBuilders = directions.stream()
                .map(direction -> new RedLightViolationChartBuilder(fromTime, toTime, direction, binSize))
                .toList();

        for (PerfLogChunkVO perfLogChunkVO : perfLogChunkVOList) {
            IntersectionConfigVO configVO = intConfigVOMap.get(perfLogChunkVO.getConfigID());
            chartBuilders.forEach(chartBuilder -> chartBuilder.setIntersectionConfig(configVO));

            List<PerfLogEventVO> eventVOList = perfLogChunkVO.getPerfLogEvents();
            if (eventVOList == null) {
                continue;
            }

            for (PerfLogEventVO eventVO : eventVOList) {
                boolean putEvent = true;

                if (eventVO.getEvent() == PerfLogEventVO.Event.DETECTOR_ON) {
                    putEvent = isValidDetEventForProcessing(eventVO);
                }
                
                if (putEvent) {
                    chartBuilders.forEach(chartBuilder -> chartBuilder.putEvent(eventVO));
                }
            }
        }

        List<RedLightViolationChartVO> chartVOList = chartBuilders.stream()
                .map(RedLightViolationChartBuilder::build)
                .toList();
        analysisVO.addCharts(chartVOList);

    }
}
