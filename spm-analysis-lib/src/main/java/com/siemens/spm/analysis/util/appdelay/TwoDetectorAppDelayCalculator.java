package com.siemens.spm.analysis.util.appdelay;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayDeque;
import java.util.Queue;

import com.siemens.spm.analysis.domain.Phase;
import com.siemens.spm.analysis.util.AppDelayUtil;
import com.siemens.spm.analysis.vo.AppDelayPlanStatisticsVO;
import com.siemens.spm.analysis.vo.AppDelayVehicleVO;
import com.siemens.spm.perflog.vo.ApproachVO;
import com.siemens.spm.perflog.vo.IntersectionConfigVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;
import lombok.extern.slf4j.Slf4j;

/**
 * Calculation class for approach delay if 2 detectors are available at the approach.
 */
@Slf4j
public class TwoDetectorAppDelayCalculator extends AppDelayCalculator {

    private final int firstDetectorId;
    private final int secondDetectorId;

    private final Queue<PerfLogEventVO> firstDetectorActivations;

    private LocalDateTime lastSecondDetectorActivation;

    private double optimalTravelTimeInSeconds;
    private double detectorDistance;

    private int cycleVolume;
    private double cycleDelayDuration;
    private double cycleSpeedSum;
    private boolean cycleDetectorOccupiedDuringGreen;

    public TwoDetectorAppDelayCalculator(LocalDateTime fromTime,
                                         int binSize,
                                         int laneIdx,
                                         int firstDetectorId,
                                         int secondDetectorId) {
        super(fromTime, binSize, laneIdx);

        this.firstDetectorId = firstDetectorId;
        this.secondDetectorId = secondDetectorId;

        this.firstDetectorActivations = new ArrayDeque<>();
    }

    @Override
    public void putEvent(PerfLogEventVO eventVO) {

        fillBins(eventVO.getDateTime());

        switch (eventVO.getEvent()) {
        case DETECTOR_ON:
            handleDetectorActivation(eventVO);
            break;
        case DETECTOR_OFF:
            handleDetectorDeactivation(eventVO);
            break;
        case PHASE_BEGIN_GREEN:
            handleGreenStart(eventVO);
            break;
        case PHASE_BEGIN_YELLOW_CLEARANCE:
            handleGreenEnd(eventVO);
            break;
        case COORD_PATTERN_CHANGE:
            putAppDelayPatternChange(eventVO);
            break;
        default:
            break;
        }
    }

    @Override
    public void setConfig(IntersectionConfigVO configVO, Phase phase) {
        ApproachVO approach = AppDelayUtil.identifyApproach(configVO, phase);

        if (approach == null) {
            log.error("No approach found for phase " + phase.getPhaseNum());
            return;
        }

        double detector1Position = AppDelayUtil.getDetectorPosition(approach, firstDetectorId);
        double detector2Position = AppDelayUtil.getDetectorPosition(approach, secondDetectorId);

        if (Double.isNaN(detector1Position)) {
            log.error("No detector with id {}", firstDetectorId);
            return;
        }

        if (Double.isNaN(detector2Position)) {
            log.error("No detector with id {}", secondDetectorId);
            return;
        }

        detectorDistance = Math.abs(detector1Position - detector2Position);

        // check if approach speed is set
        if (Double.isNaN(approach.getApproachSpeed())) {
            log.debug("No speed limit set, using default speed of {} mph.", speedLimitMph);
        } else {
            speedLimitMph = approach.getApproachSpeed();
        }

        // calculate optimal travel time
        optimalTravelTimeInSeconds = Math.abs(detector1Position - detector2Position) / (speedLimitMph / 2.237);
    }

    /**
     * Calculates the delay in a cycle (interval between 2 green ends/yellow
     * starts).
     *
     * @param toTime end time of interval
     */
    @Override
    public void calculateDelayForCycle(LocalDateTime toTime) {

        double cycleDelay;
        boolean oversaturated;
        if (cycleVolume == 0) {
            cycleDelay = 0;
            oversaturated = false;
        } else {
            cycleDelay = cycleDelayDuration / cycleVolume;
            double cycleAvgSpeed = cycleSpeedSum / cycleVolume;
            oversaturated = cycleAvgSpeed / speedLimitMph < 0.5;
        }

        double currentPlanDurationShare = calculateCurrentIntervalShare(toTime, currentPlanStat.getFromTime());
        double lastPlanDurationShare = 1 - currentPlanDurationShare;

        // create a new delay bin for this cycle and store it
        // the share of the cycle being in the current plan is taken into consideration
        if (currentPlanDurationShare > 0) {
            storePartOfCycleInCurrentInterval(toTime, planCycleDelays, currentPlanDurationShare, cycleDelay,
                    cycleVolume, oversaturated, cycleDetectorOccupiedDuringGreen);
        }

        // if part of the cycle is in the last plan, add it there
        if (lastPlanDurationShare > 0 && !planStats.isEmpty()) {

            int vehicleCount = (int) (cycleVolume * lastPlanDurationShare);
            AppDelayPlanStatisticsVO lastPlan = planStats.get(planStats.size() - 1);
            lastPlan.setTotalTimeDelay(
                    lastPlan.getTotalTimeDelay()
                            + cycleDelay * vehicleCount);
            lastPlan.setTotalVolume(lastPlan.getTotalVolume() + vehicleCount);
        }

        double currentBinDurationShare = calculateCurrentIntervalShare(toTime, currentDelayVehicleBin.getFromTime());
        double lastBinDurationShare = 1 - currentBinDurationShare;

        // create a new delay bin for this cycle and store it
        // the share of the cycle being in the current bin is taken into consideration
        if (currentBinDurationShare > 0) {
            storePartOfCycleInCurrentInterval(toTime, binCycleDelays, currentBinDurationShare, cycleDelay, cycleVolume,
                    oversaturated, cycleDetectorOccupiedDuringGreen);
        }

        // if part of the cycle is in the last bin, add it there
        if (lastBinDurationShare > 0) {

            int vehicleCount = (int) (cycleVolume * lastBinDurationShare);

            AppDelayVehicleVO lastBin = binVehicleList.get(binVehicleList.size() - 2);
            lastBin.setDelayDuration(
                    lastBin.getDelayDuration()
                            + cycleDelay * vehicleCount);
            lastBin.setBinVolume(lastBin.getBinVolume() + vehicleCount);
        }

        // reset cycle data
        cycleSpeedSum = 0;
        cycleVolume = 0;
        cycleDelayDuration = 0;
        cycleDetectorOccupiedDuringGreen = false;
        lastPhaseYellowBegin = toTime;
    }

    /**
     * Handles detector activation by either storing it or calculating travel time, depending on detector
     *
     * @param eventVO {@link PerfLogEventVO} detector activation event
     */
    private void handleDetectorActivation(PerfLogEventVO eventVO) {
        if (eventVO.getParameter() == firstDetectorId) {
            firstDetectorActivations.add(eventVO);
        } else if (eventVO.getParameter() == secondDetectorId) {
            PerfLogEventVO firstDetectorActivation = firstDetectorActivations.poll();

            if (firstDetectorActivation == null) {
                log.debug("There is no activation of first detector for the activation of second detector @{}",
                        eventVO.getDateTime());
                return;
            }

            double travelTime = Duration.between(firstDetectorActivation.getDateTime(), eventVO.getDateTime())
                    .getSeconds();
            double delay = travelTime > optimalTravelTimeInSeconds ? travelTime - optimalTravelTimeInSeconds : 0;

            double speed = travelTime / detectorDistance;

            cycleSpeedSum += speed;
            cycleVolume++;
            cycleDelayDuration += delay;

            lastSecondDetectorActivation = eventVO.getDateTime();
        }
    }

    /**
     * Handles detector deactivation by checking whether the vehicle was occupying the detector while it was green
     *
     * @param eventVO {@link PerfLogEventVO} event of detector deactivation
     */
    private void handleDetectorDeactivation(PerfLogEventVO eventVO) {
        if (eventVO.getParameter() == secondDetectorId) {
            LocalDateTime secondDetectorDeactivation = eventVO.getDateTime();
            cycleDetectorOccupiedDuringGreen = cycleDetectorOccupiedDuringGreen || checkDetectorOccupiedDuringGreen(
                    lastSecondDetectorActivation, secondDetectorDeactivation);
        }
    }

    /**
     * Handles green start by storing it
     *
     * @param eventVO {@link PerfLogEventVO} event of green start
     */
    private void handleGreenStart(PerfLogEventVO eventVO) {
        lastGreenBeginTime = eventVO.getDateTime();
    }

    /**
     * Handles green end by ending the cycle and storing data for it
     *
     * @param eventVO {@link PerfLogEventVO} event of green end
     */
    private void handleGreenEnd(PerfLogEventVO eventVO) {
        if (lastSecondDetectorActivation != null) {
            // act as if the current time is the detector deactivation
            LocalDateTime secondDetectorDeactivation = eventVO.getDateTime();
            cycleDetectorOccupiedDuringGreen = cycleDetectorOccupiedDuringGreen || checkDetectorOccupiedDuringGreen(
                    lastSecondDetectorActivation, secondDetectorDeactivation);
        }

        calculateDelayForCycle(eventVO.getDateTime());
    }
}
