package com.siemens.spm.analysis.factory;

import com.siemens.spm.analysis.aggregator.AppDelayAggregator;
import com.siemens.spm.analysis.util.EventValidatorUtils;
import com.siemens.spm.analysis.vo.AppDelayChartVO;
import com.siemens.spm.perflog.vo.IntersectionConfigVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;
import lombok.Getter;

/**
 * Chart builder for approach delay and calculation of approach delay per plan.
 *
 * <AUTHOR> (<EMAIL>)
 */
public abstract class AppDelayChartBuilder extends PhaseChartBuilder {

    @Getter
    protected AppDelayChartVO chartVO;

    protected AppDelayAggregator aggregator;

    protected AppDelayChartBuilder(AppDelayChartVO chartVO, int binSize) {
        super(chartVO.getPhase());
        this.chartVO = chartVO;

        int phase = chartVO.getPhase();
        this.aggregator = new AppDelayAggregator(chartVO.getFromTime(), chartVO.getToTime(), binSize, phase);
    }

    @Override
    public void build() {
        aggregator.fillEndBins();

        // get results from aggregator
        chartVO.setAppDelayVehicleList(aggregator.getAppDelayVehicleList());
        chartVO.setAppDelayPlanStatisticsList(aggregator.getAppDelayPlanStats());
    }

    @Override
    public void setIntersectionConfig(IntersectionConfigVO configVO) {
        aggregator.setConfig(configVO);
    }

    @Override
    public void putEvent(PerfLogEventVO eventVO) {
        if (!EventValidatorUtils.isCandidateInRange(eventVO, chartVO.getFromTime(), chartVO.getToTime())) {
            return; // Skip event with invalid datetime
        }

        super.putEvent(eventVO);
        aggregator.putEvent(eventVO);
    }

}
