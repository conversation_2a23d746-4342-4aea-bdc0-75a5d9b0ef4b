package com.siemens.spm.analysis.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PriorityCycle {

    private LocalDateTime tspCheckIn;

    private LocalDateTime tspAdjEarlyGreen;

    private LocalDateTime tspAdjExtendGreen;

    private LocalDateTime tspCheckOut;

}
