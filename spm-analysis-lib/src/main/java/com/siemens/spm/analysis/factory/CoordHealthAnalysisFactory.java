package com.siemens.spm.analysis.factory;

import com.siemens.spm.analysis.exception.AnalysisInitializationException;
import com.siemens.spm.analysis.exception.InvalidPerfLogException;
import com.siemens.spm.analysis.vo.CoordinationHealthAnalysisVO;
import com.siemens.spm.analysis.vo.CoordinationHealthChartVO;
import com.siemens.spm.perflog.vo.PerfLogBundleVO;
import com.siemens.spm.perflog.vo.PerfLogChunkVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;
import com.siemens.spm.perflog.vo.PerfLogGapVO;

import java.time.LocalDateTime;
import java.util.List;

public class CoordHealthAnalysisFactory
        extends AbstractAnalysisFactory<CoordinationHealthAnalysisVO, CoordinationHealthChartVO> {

    public static final int BIN_SIZE_900 = 900;

    private final int binSize;

    public CoordHealthAnalysisFactory(int binSize) {
        this.binSize = binSize;
    }

    public CoordinationHealthAnalysisVO createAnalysis(LocalDateTime fromTime,
                                                       LocalDateTime toTime,
                                                       PerfLogBundleVO perfLogBundleVO,
                                                       List<PerfLogGapVO> perfLogGapVOList)
            throws InvalidPerfLogException, AnalysisInitializationException {
        return createAnalysis(fromTime, toTime,
                perfLogBundleVO, perfLogGapVOList,
                CoordinationHealthAnalysisVO.class,
                CoordinationHealthChartVO.class);
    }

    // with Coordination Health Analysis, using all phases in one chart
    // add to all phase
    @Override
    protected void buildCharts(LocalDateTime fromTime,
                               LocalDateTime toTime,
                               PerfLogBundleVO perfLogBundleVO,
                               Class<CoordinationHealthChartVO> coordinationHealthChartVOClass)
            throws InvalidPerfLogException {
        if (perfLogBundleVO == null || analysisVO == null)
            throw new IllegalArgumentException();
        List<PerfLogChunkVO> perfLogChunkVOList = perfLogBundleVO.getPerfLogChunks();
        if (perfLogChunkVOList == null)
            throw new InvalidPerfLogException("No PerfLog chunk");

        // Set time range to each chart
        CoordinationHealthChartVO chartVO = new CoordinationHealthChartVO();
        chartVO.setFromTime(fromTime);
        chartVO.setToTime(toTime);

        // Scan PerfLog to build other chart data
        scanPerfLog(perfLogChunkVOList, chartVO);

        analysisVO.addChart(chartVO);
    }

    private void scanPerfLog(List<PerfLogChunkVO> perfLogChunkVOList,
                             CoordinationHealthChartVO chartVO) {
        if (perfLogChunkVOList == null || chartVO == null) {
            throw new IllegalArgumentException();
        }
        // Prepare builder for charts
        CoordHealthChartBuilder chartBuilder = new CoordHealthChartBuilder(chartVO, binSize);

        // read chunks one by one
        for (PerfLogChunkVO chunkVO : perfLogChunkVOList) {
            for (PerfLogEventVO eventVO : chunkVO.getPerfLogEvents()) {
                processEvent(chartBuilder, eventVO);
            }
        }
        // Finalize finish chart building and output to chartVOMap
        chartBuilder.build();
    }

    // check event and add to chart builder
    private void processEvent(CoordHealthChartBuilder builder,
                              PerfLogEventVO eventVO) {
        if (builder == null) {
            throw new IllegalArgumentException();
        }

        // put plan with Event 131: COORD_PATTERN_CHANGE
        if (eventVO.getEvent() == PerfLogEventVO.Event.COORD_PATTERN_CHANGE) {
            // add data to plan
            builder.putEvent(eventVO);
        }
        // get data with event 150: COORD_CYCLE_STATE_CHANGE
        if (eventVO.getEvent() == PerfLogEventVO.Event.COORD_CYCLE_STATE_CHANGE) {
            builder.putEvent(eventVO);
        }
    }

}
