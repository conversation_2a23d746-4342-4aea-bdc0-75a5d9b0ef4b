package com.siemens.spm.analysis.vo.splitmonitor;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * Represents an event that occurs within a cycle.
 */
@Getter
@Setter
@AllArgsConstructor
public class CycleEvent {

    private final String param;

    private final LocalDateTime datetime;

    private final LocalDateTime cycleStart;

    private final LocalDateTime nextCycleStart;

    private int eventNum;

}
    