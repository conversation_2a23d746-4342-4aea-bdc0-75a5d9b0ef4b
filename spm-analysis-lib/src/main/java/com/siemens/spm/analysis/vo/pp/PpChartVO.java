package com.siemens.spm.analysis.vo.pp;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.analysis.vo.BaseChartVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@SuperBuilder
@JsonInclude(JsonInclude.Include.NON_NULL)
public abstract class PpChartVO extends BaseChartVO {

    private static final long serialVersionUID = -3030732380364231839L;

    @JsonProperty("chart_type")
    public abstract String chartType();

}
