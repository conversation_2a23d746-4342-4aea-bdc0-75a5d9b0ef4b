package com.siemens.spm.analysis.util;

import java.util.List;

import com.siemens.spm.analysis.domain.StatisticBlock;
import com.siemens.spm.common.util.ListUtil;
import com.siemens.spm.common.util.MathUtil;

public final class StatisticBlockUtil {

    private StatisticBlockUtil() {
    }

    public static double calculateAvgPedDelay(List<StatisticBlock> unitBlockList) {
        if (ListUtil.hasNoItem(unitBlockList)) {
            return 0;
        }

        long totalPedDelayTime = 0;
        int totalPedDelayHit = 0;
        for (StatisticBlock unitBlock : unitBlockList) {
            totalPedDelayTime = MathUtil.plus(totalPedDelayTime, unitBlock.getSumPedDelayTime());
            totalPedDelayHit += unitBlock.getPedActivation();
        }

        return totalPedDelayHit != 0 ? (double) totalPedDelayTime / totalPedDelayHit : 0;
    }

}
