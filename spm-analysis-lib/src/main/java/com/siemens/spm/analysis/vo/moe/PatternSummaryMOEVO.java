package com.siemens.spm.analysis.vo.moe;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PatternSummaryMOEVO implements Serializable {

    @Serial private static final long serialVersionUID = -589230373693864842L;

    @JsonProperty("phase_summary")
    private Map<Integer, PhaseSummaryMOEVO> phaseSummaryMOEVOMap = new LinkedHashMap<>();

    @JsonProperty("phase_split_time")
    private Map<Integer, PhaseSplitTimeAggregationVO> phaseSplitTimeAggregationVOMap = new LinkedHashMap<>();

    @JsonIgnore
    public void addPhaseSummaryMOEVOElement(Integer key, PhaseSummaryMOEVO value) {
        phaseSummaryMOEVOMap.put(key, value);
    }

    @JsonIgnore
    public void addPhaseSplitTimeAggregationVOElement(Integer key, PhaseSplitTimeAggregationVO value) {
        phaseSplitTimeAggregationVOMap.put(key, value);
    }
}
