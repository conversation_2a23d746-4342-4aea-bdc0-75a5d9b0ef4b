package com.siemens.spm.analysis.vo.queuelength;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.analysis.vo.DataBlockVO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class QueueLengthBinVO implements Serializable, DataBlockVO {

    private static final long serialVersionUID = -5737141016390858613L;

    public QueueLengthBinVO(LocalDateTime fromTime) {
        this.fromTime = fromTime;

        cycles = new ArrayList<>();
    }

    public QueueLengthBinVO(LocalDateTime fromTime, LocalDateTime toTime) {
        this.fromTime = fromTime;
        this.toTime = toTime;

        cycles = new ArrayList<>();
    }

    @JsonProperty("from_time")
    protected LocalDateTime fromTime;

    @JsonProperty("to_time")
    protected LocalDateTime toTime;

    /**
     * List of queue length of cycle in this bin
     */
    @JsonIgnore
    private List<Double> cycles;

    @JsonProperty("avg_queue_length")
    public Double average() {

        if (cycles == null || cycles.isEmpty()) {
            return 0D;
        }

        return cycles.stream()
                .filter(Objects::nonNull)
                .mapToDouble(Double::doubleValue)
                .sum() / cycles.size();
    }

    @JsonProperty("num_of_cycles")
    public Integer numOfCycles() {
        return cycles == null ? 0 : cycles.size();
    }

    public void addCycleQueueLength(double length) {
        if (cycles == null) {
            cycles = new ArrayList<>();
        }

        cycles.add(length);
    }

}
