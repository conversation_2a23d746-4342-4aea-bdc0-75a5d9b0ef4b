/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : PhaseDetectorActivityVO.java
 * Project     : SPM Platform
 */
package com.siemens.spm.analysis.vo;

import com.siemens.spm.perflog.vo.PerfLogEventVO;

public class PhaseDetectorActivityVO extends PhaseEventVO {

    private static final long serialVersionUID = 995293926639159357L;

    /**
     * @param perfLogEventVO
     */
    public PhaseDetectorActivityVO(PerfLogEventVO perfLogEventVO) {
        super(perfLogEventVO);
        if (perfLogEventVO.getEvent() != PerfLogEventVO.Event.DETECTOR_ON)
            throw new IllegalArgumentException("Unexpected event " + perfLogEventVO.getEvent());
    }

}
