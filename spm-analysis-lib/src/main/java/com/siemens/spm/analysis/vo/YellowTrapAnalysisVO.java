package com.siemens.spm.analysis.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.siemens.spm.common.shared.domaintype.analysis.AnalysisType;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class YellowTrapAnalysisVO extends AbstractAnalysisVO<YellowTrapVO> {

    @Override
    protected String getAnalysisType() {
        return AnalysisType.YELLOW_TRAP_OCCURRENCES.getId();
    }
}
