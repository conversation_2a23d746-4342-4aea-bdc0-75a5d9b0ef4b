package com.siemens.spm.analysis.vo.splitmonitor;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR> Admin
 * @version : 1.0
 * @since : 2/7/2025
 **/
@Getter
@Setter
@AllArgsConstructor
public class CycleBoundary {

    private final long param;

    private final LocalDateTime cycleStart;

    private final LocalDateTime nextCycleStart;

    public boolean isEventInCycle(LocalDateTime eventTime) {
        return !eventTime.isBefore(cycleStart) && (nextCycleStart == null || eventTime.isBefore(nextCycleStart));
    }

}
