package com.siemens.spm.analysis.factory.redlightviolation;

import com.siemens.spm.analysis.aggregator.RedLightViolationDirectionAggregator;
import com.siemens.spm.analysis.util.EventValidatorUtils;
import com.siemens.spm.analysis.vo.redlightviolation.RedLightViolationChartVO;
import com.siemens.spm.perflog.vo.IntersectionConfigVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;

import java.time.LocalDateTime;

public class RedLightViolationChartBuilder {

    private final RedLightViolationChartVO chartVO;

    private final RedLightViolationDirectionAggregator directionAggregator;

    public RedLightViolationChartBuilder(LocalDateTime fromTime, LocalDateTime toTime, String direction, int binSize) {
        chartVO = RedLightViolationChartVO.builder()
                .fromTime(fromTime)
                .toTime(toTime)
                .direction(direction)
                .build();

        directionAggregator = new RedLightViolationDirectionAggregator(fromTime, toTime, binSize, direction);
    }

    public void setIntersectionConfig(IntersectionConfigVO configVO) {
        directionAggregator.setConfig(configVO);
    }

    public void putEvent(PerfLogEventVO eventVO) {
        if (!EventValidatorUtils.isCandidateInRange(eventVO, chartVO.getFromTime(), chartVO.getToTime())) {
            return; // Skip event with invalid datetime
        }

        directionAggregator.putEvent(eventVO);
    }

    public RedLightViolationChartVO build() {
        chartVO.setBinVOList(directionAggregator.getBinVOList());
        chartVO.setTotalViolationCount(directionAggregator.getTotalViolationCount());
        chartVO.setPlanStatVOList(directionAggregator.getPlanStatVOList());

        return chartVO;
    }

}
