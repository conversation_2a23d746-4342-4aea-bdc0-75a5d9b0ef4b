package com.siemens.spm.analysis.vo.queuelength;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

@Data
@EqualsAndHashCode(callSuper = false)
@SuperBuilder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class QueueLengthLaneChartVO extends QueueLengthChartVO {

    private static final long serialVersionUID = -6493567635333903027L;

    @JsonProperty("lane_no")
    public int getLaneNo() {
        return laneIdx + 1;
    }

    @JsonProperty("movement")
    private String movement;

    @JsonIgnore
    private int laneIdx;

}
