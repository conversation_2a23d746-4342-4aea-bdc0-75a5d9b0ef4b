package com.siemens.spm.analysis.factory;

import com.siemens.spm.analysis.aggregator.SplitFailureAggregator;
import com.siemens.spm.analysis.util.DateTimeUtil;
import com.siemens.spm.analysis.vo.Plan;
import com.siemens.spm.analysis.vo.SplitFailureChartVO;
import com.siemens.spm.analysis.vo.SplitFailureCycleVO;
import com.siemens.spm.analysis.vo.SplitFailurePlanStatisticsVO;
import com.siemens.spm.perflog.vo.IntersectionConfigVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

public class SplitFailureChartBuilder extends PhaseChartBuilder {

    private SplitFailureChartVO chartVO;

    private SplitFailureAggregator aggregator;

    private SplitFailurePlanStatisticsVO currentPlanStat;

    private List<SplitFailurePlanStatisticsVO> planStats;

    public SplitFailureChartBuilder(SplitFailureChartVO chartVO) {
        super(chartVO.getPhase());
        init(chartVO);
    }

    private void init(SplitFailureChartVO chartVO) {
        this.chartVO = chartVO;
        this.aggregator = new SplitFailureAggregator(chartVO.getPhase(), chartVO.getFromTime(), chartVO.getToTime());
        this.currentPlanStat = new SplitFailurePlanStatisticsVO(Plan.UNKNOWN_PLAN, chartVO.getFromTime());
        this.planStats = new ArrayList<>();
    }

    @Override
    public void build() {
        // fill data to cycles
        aggregator.closeCurrentCycle(chartVO.getToTime());
        chartVO.setSplitFailureCycleList(aggregator.getCycleList());
        // close phan with to_time
        closeCurrentPlanStat(chartVO.getToTime());
        for (SplitFailurePlanStatisticsVO planStatisticsVO : planStats) {
            chartVO.addSplitFailurePlanStatistics(planStatisticsVO);
        }
    }

    @Override
    protected void setIntersectionConfig(IntersectionConfigVO configVO) {
        aggregator.setConfig(configVO);
    }

    @Override
    public void putEvent(PerfLogEventVO eventVO) {
        if (!DateTimeUtil.eventDateTimeGuard(eventVO, chartVO.getFromTime())) {
            return; // Skip event with invalid datetime
        }

        super.putEvent(eventVO);

        aggregator.putEvent(eventVO);

        if (eventVO.getEvent() == PerfLogEventVO.Event.COORD_PATTERN_CHANGE) {
            // Add new plan with plan name = evenVO.getParameter()
            putSplitFailurePatternChange(eventVO);
        }

    }

    private void putSplitFailurePatternChange(PerfLogEventVO eventVO) {
        closeCurrentPlanStat(eventVO.getDateTime());
        // Start new planStatistics
        currentPlanStat = new SplitFailurePlanStatisticsVO((int) eventVO.getParameter(), eventVO.getDateTime());
    }

    private void closeCurrentPlanStat(LocalDateTime toTime) {
        currentPlanStat.setToTime(toTime);
        Map<Integer, AtomicInteger> indicatorAgg = aggregator.getIndicator(currentPlanStat.getFromTime(), toTime);
        // get total split failure from aggregator
        currentPlanStat.setTotalSF(indicatorAgg.get(SplitFailureCycleVO.SPLIT_FAILURES).get());
        currentPlanStat.setTotalEGU(indicatorAgg.get(SplitFailureCycleVO.EXCELLENT_GREEN_UTILIZATION).get());
        currentPlanStat.setTotalEC(indicatorAgg.get(SplitFailureCycleVO.EXCESS_CAPACITY).get());
        currentPlanStat.setTotalPC(indicatorAgg.get(SplitFailureCycleVO.POOR_COORDINATION).get());
        // Close current plan stat and add to list
        planStats.add(currentPlanStat);
        currentPlanStat = null;
    }

}
