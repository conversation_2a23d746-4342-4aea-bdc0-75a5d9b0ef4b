package com.siemens.spm.analysis.factory.moe;

import com.siemens.spm.analysis.aggregator.MOECycleRingAggregator;
import com.siemens.spm.analysis.aggregator.MOEFreePlanAggregator;
import com.siemens.spm.analysis.aggregator.MOESummaryAggregator;
import com.siemens.spm.analysis.util.Constants;
import com.siemens.spm.analysis.vo.moe.CycleLengthVO;
import com.siemens.spm.analysis.vo.moe.CycleMOEVO;
import com.siemens.spm.analysis.vo.moe.MOEAnalysisDetailViewVO;
import com.siemens.spm.analysis.vo.moe.MOEAnalysisFreePlanDetailViewVO;
import com.siemens.spm.analysis.vo.moe.PatternSummaryMOEVO;
import com.siemens.spm.analysis.vo.moe.PhaseCycleMOEVO;
import com.siemens.spm.perflog.vo.ApproachVO;
import com.siemens.spm.perflog.vo.IntersectionConfigVO;
import com.siemens.spm.perflog.vo.LaneVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;
import com.siemens.spm.perflog.vo.PhaseVO;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.siemens.spm.analysis.util.Constants.RING_KEY;

@Slf4j
public class MOEAnalysisDetailChartBuilder {

    private static final Integer MAX_RING = 2;

    private Map<String, MOECycleRingAggregator> moeCycleRingAggregatorMap;

    private final MOEFreePlanAggregator moeFreePlanPhaseAggregator;

    private final MOESummaryAggregator moeSummaryAggregator;

    @Getter
    private final Map<Integer, Set<String>> approachMap;

    @Getter
    private List<MOEAnalysisDetailViewVO> moeDetailViewList;

    @Getter
    private List<MOEAnalysisFreePlanDetailViewVO> moeFreePlanDetailViewList;

    @Getter
    private CycleLengthVO cycleLengthVO;

    private static final String RING_1 = "Ring 1";

    private static final String RING_2 = "Ring 2";

    public MOEAnalysisDetailChartBuilder() {
        this.moeSummaryAggregator = new MOESummaryAggregator();
        this.moeFreePlanPhaseAggregator = new MOEFreePlanAggregator();
        this.approachMap = new HashMap<>();
        this.cycleLengthVO = new CycleLengthVO();
        this.moeDetailViewList = new ArrayList<>();
        this.moeFreePlanDetailViewList = new ArrayList<>();
        initMOECycleRingAggregatorMap();
    }

    private void initMOECycleRingAggregatorMap() {
        this.moeCycleRingAggregatorMap = new HashMap<>();
        for (int i = 1; i <= MAX_RING; i++) {
            moeCycleRingAggregatorMap.put(RING_KEY + i, new MOECycleRingAggregator(i));
        }
    }

    public void putEvent(PerfLogEventVO eventVO) {
        this.moeCycleRingAggregatorMap.forEach((k, v) -> v.putEvent(eventVO));
        this.moeSummaryAggregator.putEvent(eventVO);
        this.moeFreePlanPhaseAggregator.putEvent(eventVO);
    }

    public void setConfig(IntersectionConfigVO configVO) {
        this.moeCycleRingAggregatorMap.forEach((k, v) -> v.setConfig(configVO));
        this.moeSummaryAggregator.setConfig(configVO);
        detectApproach(configVO);
    }

    public Map<Integer, PatternSummaryMOEVO> getMOESummaryView() {
        this.moeSummaryAggregator.aggregatePatternSummaryMOEVO();
        return this.moeSummaryAggregator.getPatternSummaryMOEVOMap();
    }

    public void generateMOEDetailView() {
        Map<String, List<MOEAnalysisDetailViewVO>> ringMOEDetailView = new HashMap<>();
        this.moeCycleRingAggregatorMap.forEach((k, v) -> ringMOEDetailView.put(k, v.getMoeAnalysisDetailViewVOList()));
        mergeRing(ringMOEDetailView);
        calCycleLengthVO();

        this.moeFreePlanDetailViewList = this.moeFreePlanPhaseAggregator.getMoeAnalysisDetailViewVOList();
    }

    public void calCycleLengthVO() {
        List<Integer> cycleLengthList;
        cycleLengthList = moeDetailViewList.stream()
                .map(moeAnalysisDetailViewVO ->
                        moeAnalysisDetailViewVO.getCycle().getActualLength()
        ).toList();
        cycleLengthVO.setNumberOfCycles(cycleLengthList.size());
        cycleLengthVO.setMinimumCycleLength(cycleLengthList.stream().min(Integer::compareTo).orElse(0));
        cycleLengthVO.setMaxCycleLength(cycleLengthList.stream().max(Integer::compareTo).orElse(0));
        cycleLengthVO.setActualAverageCycle(cycleLengthList.stream().reduce(0, Integer::sum) / (cycleLengthList.size() * 1.0));
    }

    private void mergeRing(Map<String, List<MOEAnalysisDetailViewVO>> ringMOEDetailView) {
        List<MOEAnalysisDetailViewVO> ring1ViewList = ringMOEDetailView.get(RING_1);
        List<MOEAnalysisDetailViewVO> ring2ViewList = ringMOEDetailView.get(RING_2);
        int indexRing1 = 0;
        int indexRing2 = 0;
        int sizeRing1 = ring1ViewList.size();
        int sizeRing2 = ring2ViewList.size();
        while (sizeRing1 > 0 && sizeRing2 > 0) {
            MOEAnalysisDetailViewVO moeDetailView;
            MOEAnalysisDetailViewVO ring1View = ring1ViewList.get(indexRing1);
            MOEAnalysisDetailViewVO ring2View = ring2ViewList.get(indexRing2);
            boolean isRing1AfterOrEqual = ring1View.getFromTime().isAfter(ring2View.getToTime())
                    || ring1View.getFromTime().isEqual(ring2View.getToTime());
            boolean isRing2AfterOrEqual = ring2View.getFromTime().isAfter(ring1View.getToTime())
                    || ring2View.getFromTime().isEqual(ring1View.getToTime());
            if (isRing1AfterOrEqual) {
                moeDetailView = mergeMOEDetailView(ring2View);
                moeDetailViewList.add(moeDetailView);
                indexRing2++;
                sizeRing2--;
            } else if (isRing2AfterOrEqual) {
                moeDetailView = mergeMOEDetailView(ring1View);
                moeDetailViewList.add(moeDetailView);
                indexRing1++;
                sizeRing1--;
            } else {
                moeDetailView = mergeMOEDetailView(ring1View, ring2View);
                moeDetailViewList.add(moeDetailView);
                indexRing1++;
                indexRing2++;
                sizeRing1--;
                sizeRing2--;
            }
        }
        while (sizeRing1 > 0) {
            moeDetailViewList.add(ring1ViewList.get(indexRing1));
            indexRing1++;
            sizeRing1--;
        }
        while (sizeRing2 > 0) {
            moeDetailViewList.add(ring2ViewList.get(indexRing2));
            indexRing2++;
            sizeRing2--;
        }
    }

    private MOEAnalysisDetailViewVO mergeMOEDetailView(MOEAnalysisDetailViewVO ringView) {
        MOEAnalysisDetailViewVO moeDetailView = new MOEAnalysisDetailViewVO();
        moeDetailView.setPattern(ringView.getPattern());
        moeDetailView.setTransition(ringView.getTransition());
        moeDetailView.setTsp(ringView.isTsp());
        moeDetailView.setPreemption(ringView.isPreemption());
        moeDetailView.setFromTime(ringView.getFromTime());
        moeDetailView.setToTime(ringView.getToTime());
        CycleMOEVO cycleMOEVO = getCycleMOEVO(ringView.getCycle());
        moeDetailView.setCycle(cycleMOEVO);
        List<PhaseCycleMOEVO> phaseCycleMOEVOS = ringView.getPhaseCycle();
        phaseCycleMOEVOS = phaseCycleMOEVOS.stream()
                .filter(p -> p.getPhase() > 0)
                .sorted(Comparator.comparing(PhaseCycleMOEVO::getPhase))
                .toList();
        moeDetailView.setPhaseCycle(phaseCycleMOEVOS);
        return moeDetailView;
    }

    private MOEAnalysisDetailViewVO mergeMOEDetailView(MOEAnalysisDetailViewVO ring1View, MOEAnalysisDetailViewVO ring2View) {
        MOEAnalysisDetailViewVO moeDetailView = new MOEAnalysisDetailViewVO();
        moeDetailView.setPattern(ring1View.getPattern());
        moeDetailView.setTransition(ring1View.getTransition() != null && ring2View.getTransition() != null ? ring1View.getTransition() : null);
        moeDetailView.setTsp(ring1View.isTsp() || ring2View.isTsp());
        moeDetailView.setPreemption(ring1View.isPreemption() || ring2View.isPreemption());
        moeDetailView.setFromTime(ring1View.getFromTime().isBefore(ring2View.getFromTime()) ? ring1View.getFromTime() : ring2View.getFromTime());
        moeDetailView.setToTime(ring1View.getToTime().isBefore(ring2View.getToTime()) ? ring2View.getToTime() : ring1View.getToTime());
        CycleMOEVO cycleMOEVO = getCycleMOEVO(ring1View.getCycle(), ring2View.getCycle());
        moeDetailView.setCycle(cycleMOEVO);
        List<PhaseCycleMOEVO> phaseCycleMOEVOS = ring1View.getPhaseCycle();
        phaseCycleMOEVOS.addAll(ring2View.getPhaseCycle());
        phaseCycleMOEVOS = phaseCycleMOEVOS.stream()
                .filter(p -> p.getPhase() > 0)
                .sorted(Comparator.comparing(PhaseCycleMOEVO::getPhase))
                .toList();
        moeDetailView.setPhaseCycle(phaseCycleMOEVOS);
        return moeDetailView;
    }

    private CycleMOEVO getCycleMOEVO(CycleMOEVO cycleRingView) {
        CycleMOEVO cycleMOEVO = new CycleMOEVO();
        cycleMOEVO.setActualLength(cycleRingView.getActualLength());
        cycleMOEVO.setProgrammedLength(cycleRingView.getProgrammedLength());
        return cycleMOEVO;
    }

    private CycleMOEVO getCycleMOEVO(CycleMOEVO cycleRing1View, CycleMOEVO cycleRing2View) {
        CycleMOEVO cycleMOEVO = new CycleMOEVO();
        cycleMOEVO.setActualLength(
                Math.max(cycleRing1View.getActualLength(), cycleRing2View.getActualLength()));
        cycleMOEVO.setProgrammedLength(
                Math.max(cycleRing1View.getProgrammedLength(), cycleRing2View.getProgrammedLength()));
        return cycleMOEVO;
    }

    private void detectApproach(IntersectionConfigVO configVO) {
        List<ApproachVO> approachVOList = configVO.getApproaches();
        List<PhaseVO> phaseVOList;
        List<LaneVO> laneVOList;
        for (ApproachVO approachVO : approachVOList) {
            phaseVOList = approachVO.getPhases();
            for (PhaseVO phaseVO : phaseVOList) {
                approachMap.putIfAbsent(phaseVO.getPhaseNumber(), new HashSet<>());
                laneVOList = phaseVO.getLanes();
                for (LaneVO laneVO : laneVOList) {
                    approachMap.get(phaseVO.getPhaseNumber()).add(Constants.DIRECTION_MAP.get(approachVO.getDirection())
                            + Constants.MOVEMENT_MAP.get(laneVO.getMovement()));
                }
            }
        }
    }
}
