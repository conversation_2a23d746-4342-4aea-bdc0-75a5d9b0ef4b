package com.siemens.spm.analysis.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.analysis.domain.PhaseColor;
import com.siemens.spm.analysis.util.DateTimeUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Duration;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LightBlockVO implements Comparable<LightBlockVO>, Serializable {

    private static final long serialVersionUID = 6035485120269517167L;

    @JsonProperty("phase")
    private int phase;

    @JsonProperty("color")
    private String color;

    @JsonIgnore
    private int plan;

    @JsonIgnore
    private String configId;

    @JsonIgnore
    private int ring;

    @JsonProperty("is_coord_phase")
    private boolean isCoordPhase;
    @JsonProperty("start_time")
    private LocalDateTime startTime;

    @JsonProperty("end_time")
    private LocalDateTime endTime;

    public LightBlockVO(LightBlockVO lightBlockVO) {
        this.phase = lightBlockVO.phase;
        this.color = lightBlockVO.color;
        this.plan = lightBlockVO.plan;
        this.ring = lightBlockVO.ring;
        this.configId = lightBlockVO.configId;
        this.isCoordPhase = lightBlockVO.isCoordPhase;
        this.startTime = lightBlockVO.startTime;
        this.endTime = lightBlockVO.endTime;
    }

    @Override
    public int compareTo(LightBlockVO o) {
        int compare = this.startTime.compareTo(o.getStartTime());
        if (compare == 0) {
            return Integer.compare(this.phase, o.phase);
        }
        return compare;
    }

    @JsonIgnore
    public int getDuration() {
        return DateTimeUtil.duration(startTime, endTime)
                .map(Duration::toSeconds)
                .map(Math::toIntExact)
                .orElse(0);
    }

    @JsonIgnore
    public LightBlockVO cloneObject() {
        return LightBlockVO.builder()
                .phase(phase)
                .color(color)
                .isCoordPhase(isCoordPhase)
                .plan(plan)
                .startTime(startTime)
                .endTime(endTime)
                .build();
    }

    @JsonIgnore
    public boolean isGreenCoordPhase() {
        return isCoordPhase && PhaseColor.GREEN.name().equalsIgnoreCase(color);
    }

    @JsonIgnore
    public boolean isGreen() {
        return PhaseColor.GREEN.name().equalsIgnoreCase(color);
    }

}
