package com.siemens.spm.analysis.vo.queuelength;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

@Data
@EqualsAndHashCode(callSuper = false)
@SuperBuilder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class QueueLengthPhaseChartVO extends QueueLengthChartVO {

    private static final long serialVersionUID = 5163682103637612370L;

}
