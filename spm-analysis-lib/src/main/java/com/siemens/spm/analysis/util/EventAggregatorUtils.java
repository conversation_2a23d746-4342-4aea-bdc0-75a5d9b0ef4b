package com.siemens.spm.analysis.util;

import java.util.Arrays;

import com.siemens.spm.analysis.domain.Phase;
import com.siemens.spm.analysis.vo.VolumeBinVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;

/**
 * Utilities class for event aggregator
 *
 * <AUTHOR>
 */
public final class EventAggregatorUtils {

    private EventAggregatorUtils() {
    }

    /**
     * Merge unknown hit of specific {@link VolumeBinVO} binVO, it can be merge as <PERSON><PERSON>, <PERSON><PERSON> or <PERSON><PERSON> depend on specific
     * phase event
     *
     * @param eventVO eventVO use to determine type or hit should be merge from unknown hits
     * @param binVO   binVO need to merge unknown hits
     */
    public static void mergeUnknownHits(PerfLogEventVO eventVO, VolumeBinVO binVO) {
        switch (eventVO.getEvent()) {
        case PHASE_BEGIN_GREEN,
        PHASE_END_RED_CLEARANCE:
            binVO.mergeUnknownHitsAsAor();
            break;
        case PHASE_BEGIN_YELLOW_CLEARANCE,
        PHASE_GREEN_TERMINATION:
            binVO.mergeUnknownHitsAsAog();
            break;
        case PHASE_BEGIN_RED_CLEARANCE,
        PHASE_END_YELLOW_CLEARANCE:
            binVO.mergeUnknownHitsAsAoy();
            break;
        default:
            break;
        }
    }

    /**
     * Initialize and return an array of {@code boolean} indicate to detector is called in a specific phase or not
     *
     * @param phaseNum      phase number
     * @param maxDetNum     max of detector number
     * @param detCallPhases detCallPhases data used to determine a detector is called in a phase or not
     * @return An array of {@code boolean} indicate to detector is called in a specific phase or not
     */
    public static boolean[] initCallingDetArr(int phaseNum, int maxDetNum, int[][] detCallPhases) {
        // Reset calling flag
        boolean[] callingDetArr = new boolean[maxDetNum + 1];
        Arrays.fill(callingDetArr, false);

        if (detCallPhases == null) {
            return callingDetArr;
        }

        for (int detIdx = 0; detIdx < detCallPhases.length; detIdx++) {
            if (detCallPhases[detIdx] == null)
                continue;

            for (int i = 0; i < detCallPhases[detIdx].length; i++) {
                // Count the detector if targeted phase is unspecified (any phases) or calling
                // phase is the targeted phase
                if (phaseNum == Phase.UNSPECIFIED_PHASE_NUM || detCallPhases[detIdx][i] == phaseNum) {
                    callingDetArr[detIdx] = true;
                    break;
                }
            }
        }

        return callingDetArr;
    }

}
