package com.siemens.spm.analysis.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.siemens.spm.perflog.vo.PerfLogEventVO;

/**
 * Pedestrian change event, such as changes to begin walk, begin clearance,
 * begin don't walk
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PedChangeVO extends PhaseEventVO {

    private static final long serialVersionUID = -3192581646522114060L;

    public PedChangeVO(PerfLogEventVO perfLogEventVO) {
        super(perfLogEventVO);

        if (!perfLogEventVO.isPedestrianEvent()) {
            throw new IllegalArgumentException("Unexpected event " + perfLogEventVO.getEvent());
        }
    }

}
