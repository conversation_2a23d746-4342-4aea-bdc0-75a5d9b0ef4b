package com.siemens.spm.analysis.vo.moe;

import com.siemens.spm.analysis.vo.AbstractAnalysisVO;
import com.siemens.spm.common.shared.domaintype.analysis.AnalysisType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class MOEAnalysisVO extends AbstractAnalysisVO<MOEAnalysisChartVO> {

    @Override
    protected String getAnalysisType() {
        return AnalysisType.MOE_ANALYSIS.getId();
    }
}
