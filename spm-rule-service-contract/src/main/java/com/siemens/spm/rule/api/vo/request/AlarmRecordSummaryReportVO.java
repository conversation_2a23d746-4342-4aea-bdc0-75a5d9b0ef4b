package com.siemens.spm.rule.api.vo.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.siemens.spm.reporting.vo.ReportRequestVO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class AlarmRecordSummaryReportVO {

    private AlarmRecordSummaryRequestVO alarmRecordSummaryRequestVO;

    private ReportRequestVO reportRequestVO;
}
