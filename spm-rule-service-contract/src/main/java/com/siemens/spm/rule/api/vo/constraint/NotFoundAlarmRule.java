/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : NotExistedAlarmRule.java
 * Project     : SPM Platform
 */

package com.siemens.spm.rule.api.vo.constraint;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import jakarta.validation.Constraint;
import jakarta.validation.Payload;

@Constraint(validatedBy = {})
@Retention(RetentionPolicy.RUNTIME)
@Target({ ElementType.FIELD, ElementType.PARAMETER})
public @interface NotFoundAlarmRule {

	public String message() default "This rule is not existing in the system.";

	public Class<?>[] groups() default {};

	public Class<? extends Payload>[] payload() default {};

}
