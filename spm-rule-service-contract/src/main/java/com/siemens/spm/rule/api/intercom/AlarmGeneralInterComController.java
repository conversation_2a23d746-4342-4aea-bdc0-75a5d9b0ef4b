package com.siemens.spm.rule.api.intercom;

import com.siemens.spm.common.intercom.InterComController;
import com.siemens.spm.common.shared.vo.SimpleResultObject;
import com.siemens.spm.common.util.RestUtils;
import com.siemens.spm.rule.api.vo.request.AgencyIdListRequestVO;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * Internal REST interface providing information to other services. This interface is implemented by the actual running
 * service.
 */
@RequestMapping(AlarmGeneralInterComController.API_ROOT)
public interface AlarmGeneralInterComController extends InterComController {

    String VERSION = "/v1";

    String API_ROOT = INTERNAL_API + VERSION;

    String DELETE_AGENCY_RESOURCE = "/alarm/agency/delete";

    /**
     * Delete Multi Agency by Agency Id
     *
     * @param requestVO AlarmRecordCreateRequestVO
     * @return ResponseEntity<SimpleResultObject>
     */
    @PostMapping(DELETE_AGENCY_RESOURCE)
    ResponseEntity<SimpleResultObject> deleteMultiAgency(@Valid @RequestBody AgencyIdListRequestVO requestVO);

    /**
     * invoke deleteMultiAgency request
     *
     * @param endpoint  endpoint of target service
     * @param requestVO AlarmRuleEvaluationJobUpdateRequestVO
     * @return ResponseEntity<SimpleResultObject>
     */
    static ResponseEntity<SimpleResultObject> invokeDeleteMultiAgency(String endpoint,
                                                                      AgencyIdListRequestVO requestVO) {
        String url = endpoint + API_ROOT + DELETE_AGENCY_RESOURCE;
        return RestUtils.post(url, requestVO, SimpleResultObject.class);
    }

}
