package com.siemens.spm.rule.api.vo.request;

import java.io.Serializable;
import java.util.List;

import jakarta.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.common.shared.vo.AlarmCategoryVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AlarmCategoryUpdateRequestVO implements Serializable {

    @NotNull(message = "alarm_record.agency_id_not_null")
    @JsonProperty("agency_id")
    private Integer agencyId;

    @JsonProperty("alarm_categories")
    private List<AlarmCategoryVO> alarmCategoryVOList;
}
