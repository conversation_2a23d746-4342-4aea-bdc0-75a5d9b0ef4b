/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AlarmAnalysisService.java
 * Project     : SPM Platform
 */

package com.siemens.spm.rule.api.boundary;

import com.siemens.spm.rule.api.vo.response.AnalysisTypeListResponseVO;

public interface AlarmAnalysisService {

    /**
     * Retrieve all analysis types
     *
     * @return AnalysisTypeListResponseVO
     */
    AnalysisTypeListResponseVO getAllAlarmAnalyses();
}
