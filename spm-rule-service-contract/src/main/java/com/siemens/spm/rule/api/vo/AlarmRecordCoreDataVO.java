/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AlarmRecordCoreDataVO.java
 * Project     : SPM Platform
 */

package com.siemens.spm.rule.api.vo;

import java.io.Serializable;
import java.time.OffsetDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.common.util.DateTimeUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class AlarmRecordCoreDataVO implements Serializable {

    private static final long serialVersionUID = 8167942579627980899L;

    @JsonProperty("id")
    private Long id;

    @JsonProperty("name")
    private String name;

    @JsonProperty("category_id")
    private Long categoryId;

    @JsonProperty("category_name")
    private String categoryName;

    @JsonProperty("analysis_id")
    private String analysisId;

    @JsonProperty("alarm_time")
    @JsonFormat(pattern = DateTimeUtils.ISO_8601_OFFSET_DATE_TIME)
    private OffsetDateTime alarmTime;

    @JsonProperty("intersection_id")
    private String intersectionId;

    @JsonProperty("agency_id")
    private Integer agencyId;

    @JsonProperty("content")
    private String content;

}
