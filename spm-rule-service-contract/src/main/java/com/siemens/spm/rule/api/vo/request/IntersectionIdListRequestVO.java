/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : IntersectionIdListVO.java
 * Project     : SPM Platform
 */
package com.siemens.spm.rule.api.vo.request;

import java.io.Serializable;
import java.util.List;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_NULL)
public class IntersectionIdListRequestVO implements Serializable {

    private static final long serialVersionUID = -2135914885512798009L;

    @Schema(description = "A list of intersection ids")
    @NotEmpty(message = "alarm_rule.intersection_ids_not_empty")
    @JsonProperty("intersection_ids")
    private List<String> intersectionIds;

    @Schema(description = "scope is either ALL_INTERSECTIONS OR SPECIFIC_INTERSECTIONS")
    @NotBlank(message = "alarm_rule.scope_not_empty")
    @JsonProperty("scope")
    private String scope;

    @Schema(description = "action is either ADD or REMOVE")
    @NotBlank(message = "alarm_rule.action_not_blank")
    @JsonProperty("action")
    private String action;
}
