/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : IntersectionSearchResultObject.java
 * Project     : spm-analysis-service
 */

package com.siemens.spm.rule.api.vo.response;

import java.io.Serializable;
import java.util.List;

import org.springframework.http.HttpStatus;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.siemens.spm.common.shared.vo.AbstractResultObject;
import com.siemens.spm.rule.api.vo.IntersectionSimpleVO;
import com.siemens.spm.rule.api.vo.response.IntersectionSearchResultObject.ResponseData;
import com.siemens.spm.rule.api.vo.response.IntersectionSearchResultObject.StatusCode;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class IntersectionSearchResultObject extends AbstractResultObject<ResponseData, StatusCode> {

    private static final long serialVersionUID = 3656870235628394739L;

    private ResponseData data;

    private StatusCode statusCode;

    /**
     * Constructor for SUCCESS case
     *
     * @param data
     */
    public IntersectionSearchResultObject(ResponseData data) {
        super(data);
    }

    /**
     * Constructor to set status to other than success
     *
     * @param data
     * @param statusCode
     */
    public IntersectionSearchResultObject(ResponseData data,
                                          StatusCode statusCode) {
        super(data, statusCode);
    }

    @Override
    public HttpStatus getHttpStatus() {
        return statusCode.getHttpStatus();
    }

    @Override
    public StatusCode getStatusCode() {
        return statusCode;
    }

    @Override
    public String getMessage() {
        return statusCode.getMessage();
    }

    @Override
    protected void setData(ResponseData value) {
        data = value;
    }

    @Override
    public StatusCode getSuccessfulStatusValue() {
        return StatusCode.SUCCESS;
    }

    @Override
    public ResponseData getData() {
        return data;
    }

    @Override
    protected StatusCode getErrorStatusValue() {
        return StatusCode.ERROR;
    }

    @Override
    protected void setStatusCode(StatusCode value) {
        statusCode = value;
    }

    @Getter
    @AllArgsConstructor
    public enum StatusCode {
        ERROR("unknown_exception", HttpStatus.INTERNAL_SERVER_ERROR),
        SUCCESS("success", HttpStatus.OK),
        NO_DATA("no_data", HttpStatus.NO_CONTENT),
        RULE_NOT_FOUND("alarm_rule.rule_not_found", HttpStatus.BAD_REQUEST),

        /* intersection status validation */
        INTERSECTION_STATUS_INVALID("invalid_intersection_status", HttpStatus.BAD_REQUEST),
        INTERSECTION_DISABLED("intersection_unavailable", HttpStatus.FORBIDDEN);

        private final String errorField;
        private final String message;
        private final HttpStatus httpStatus;

        StatusCode(String message, HttpStatus httpStatus) {
            this(null, message, httpStatus);
        }
    }

    @Override
    public String getErrorFieldName() {
        return statusCode.getErrorField();
    }

    @Data
    @Builder
    @AllArgsConstructor
    @JsonInclude(Include.NON_NULL)
    @NoArgsConstructor
    public static class ResponseData implements Serializable {

        private static final long serialVersionUID = 1885490891925474586L;

        @JsonProperty("total_count")
        private Long totalCount;

        @JsonProperty("intersections")
        private List<IntersectionSimpleVO> intersections;

        @JsonProperty("scope")
        private String scope;

    }
}
