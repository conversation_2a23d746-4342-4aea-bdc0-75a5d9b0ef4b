/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : ValidTimeRange.java
 * Project     : SPM Platform
 */

package com.siemens.spm.rule.api.vo.constraint;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

@Constraint(validatedBy = {})
@Retention(RetentionPolicy.RUNTIME)
@Target({ ElementType.FIELD })
public @interface ValidTimeRange {
    String message() default "Time range is invalid";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
