/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AlarmGeneralController.java
 * Project     : SPM Platform
 */

package com.siemens.spm.rule.api.controller;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;

import com.siemens.spm.common.api.PublicController;
import com.siemens.spm.common.constant.AgencyConstants;
import com.siemens.spm.rule.api.vo.response.AlarmCategoryListResponseVO;
import com.siemens.spm.rule.api.vo.response.AnalysisTypeListResponseVO;
import com.siemens.spm.rule.api.vo.response.TrafficMetricListResultObject;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;

@RequestMapping(AlarmGeneralControllerV2.URL_ROOT)
public interface AlarmGeneralControllerV2 extends PublicController {

    String VERSION = "/v2";
    String ALARM_RESOURCE = "/alarm";
    String URL_ROOT = PUBLIC_API + VERSION + ALARM_RESOURCE;

    /**
     * Get all analysis types: GET /alarm/analyses
     *
     * @return AnalysisTypeListResponseVO
     */
    @Operation(summary = "Retrieve all analysis types")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "404", description = "Not Found") })
    @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
            description = "Bearer Token type")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping("analyses")
    ResponseEntity<AnalysisTypeListResponseVO> getAllAlarmAnalyses(
            @Parameter(name = AgencyConstants.AGENCY_ID_HEADER_V2, 
                       example = AgencyConstants.AGENCY_ID_HEADER_V2 + "=4246",
                       required = true,
                       in = ParameterIn.HEADER, description = "Agency Id")
            @RequestHeader(AgencyConstants.AGENCY_ID_HEADER_V2) Integer agencyId
    );

    /**
     * Get all alarm categories: GET /alarm/categories
     *
     * @return AlarmCategoryListResponseVO
     */
    @Operation(summary = "Retrieve all alarm categories")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "404", description = "Not Found") })
    @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
            description = "Bearer Token type")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping("categories")
    ResponseEntity<AlarmCategoryListResponseVO> getAllAlarmCategories(
            @Parameter(name = AgencyConstants.AGENCY_ID_HEADER_V2, 
                       example = AgencyConstants.AGENCY_ID_HEADER_V2 + "=4246",
                       required = true,
                       in = ParameterIn.HEADER, description = "Agency Id")
            @RequestHeader(AgencyConstants.AGENCY_ID_HEADER_V2) Integer agencyId
    );

    /**
     * Get all traffic metrics: GET /alarm/traffic-metrics/all
     *
     * @return TrafficMetricListResultObject
     */
    @Operation(summary = "Get all traffic metrics")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "404", description = "Not Found") })
    @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
            description = "Bearer Token type")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping("/traffic-metrics/all")
    ResponseEntity<TrafficMetricListResultObject> getAllTrafficMetrics(
            @Parameter(name = AgencyConstants.AGENCY_ID_HEADER_V2, 
                       example = AgencyConstants.AGENCY_ID_HEADER_V2 + "=4246",
                       required = true,
                       in = ParameterIn.HEADER, description = "Agency Id")
            @RequestHeader(AgencyConstants.AGENCY_ID_HEADER_V2) Integer agencyId
    );

}
