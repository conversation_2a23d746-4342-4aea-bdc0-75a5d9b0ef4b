// /*
//  * Copyright (C) Siemens.  All Rights Reserved.
//  *
//  * Source      : AlarmRuleController.java
//  * Project     : SPM Platform
//  */

// package com.siemens.spm.rule.api.controller;

// import java.time.DayOfWeek;
// import java.time.LocalTime;
// import java.util.List;

// import jakarta.validation.Valid;
// import jakarta.validation.constraints.NotEmpty;
// import jakarta.validation.constraints.NotNull;

// import org.springframework.http.HttpStatus;
// import org.springframework.http.ResponseEntity;
// import org.springframework.web.bind.annotation.DeleteMapping;
// import org.springframework.web.bind.annotation.GetMapping;
// import org.springframework.web.bind.annotation.PatchMapping;
// import org.springframework.web.bind.annotation.PathVariable;
// import org.springframework.web.bind.annotation.PostMapping;
// import org.springframework.web.bind.annotation.PutMapping;
// import org.springframework.web.bind.annotation.RequestBody;
// import org.springframework.web.bind.annotation.RequestMapping;
// import org.springframework.web.bind.annotation.RequestParam;
// import org.springframework.web.bind.annotation.ResponseStatus;

// import com.siemens.spm.common.api.PublicController;
// import com.siemens.spm.rule.api.vo.constraint.NotFoundAlarmRule;
// import com.siemens.spm.rule.api.vo.request.AlarmRuleCreateRequestVO;
// import com.siemens.spm.rule.api.vo.request.AlarmRuleIdListRequestVO;
// import com.siemens.spm.rule.api.vo.request.AlarmRuleUpdateRequestVO;
// import com.siemens.spm.rule.api.vo.request.IntersectionIdListRequestVO;
// import com.siemens.spm.rule.api.vo.request.IntersectionSearchRequestVO;
// import com.siemens.spm.rule.api.vo.request.ManualAlarmRequestVO;
// import com.siemens.spm.rule.api.vo.request.SubscribeRulesCurrentUserRequestVO;
// import com.siemens.spm.rule.api.vo.request.SubscriberIdListRequestVO;
// import com.siemens.spm.rule.api.vo.response.AlarmRuleDetailResultObject;
// import com.siemens.spm.rule.api.vo.response.AlarmRuleManipulateResultObject;
// import com.siemens.spm.rule.api.vo.response.AlarmRuleSearchResponseVO;
// import com.siemens.spm.rule.api.vo.response.IntersectionSearchResultObject;
// import com.siemens.spm.rule.api.vo.response.SubscriberSearchResultObject;
// import io.swagger.v3.oas.annotations.Operation;
// import io.swagger.v3.oas.annotations.Parameter;
// import io.swagger.v3.oas.annotations.enums.ParameterIn;
// import io.swagger.v3.oas.annotations.responses.ApiResponse;
// import io.swagger.v3.oas.annotations.responses.ApiResponses;

// @RequestMapping(AlarmRuleController.URL_ROOT)
// public interface AlarmRuleController extends PublicController {

//     String VERSION = "/v1";
//     String ALARM_RULES_RESOURCE = "/alarm/rules";
//     String URL_ROOT = PUBLIC_API + VERSION + "/agencies/{agency_id}" + ALARM_RULES_RESOURCE;

//     /**
//      * Create new alarm rule: POST /alarm/rules
//      *
//      * @param requestVO
//      * @return AlarmRuleDetailResultObject
//      */
//     @Operation(summary = "Create an alarm rule")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "400", description = "Bad Request") })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true,
//             in = ParameterIn.HEADER, description = "Bearer Token type")
//     @ResponseStatus(HttpStatus.CREATED)
//     @PostMapping
//     ResponseEntity<AlarmRuleDetailResultObject> createRule(
//             @Parameter(description = "Id of an agency that the rule belongs to")
//             @PathVariable("agency_id") Integer agencyId,

//             @Parameter(name = "request_body", description = "Information for an new alarm rule to be created")
//             @Valid
//             @RequestBody
//             AlarmRuleCreateRequestVO requestVO
//     );

//     /**
//      * Get an alarm rule: GET /alarm/rules/{rule_id}
//      *
//      * @param ruleId
//      * @return AlarmRuleDetailResultObject
//      */
//     @Operation(summary = "Retrieve an alarm rule by id")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "404", description = "Not Found") })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true,
//             in = ParameterIn.HEADER, description = "Bearer Token type")
//     @ResponseStatus(HttpStatus.OK)
//     @GetMapping("{rule_id}")
//     ResponseEntity<AlarmRuleDetailResultObject> getRule(
//             @Parameter(description = "Id of an agency that the rule belongs to")
//             @PathVariable("agency_id") Integer agencyId,

//             @Parameter(description = "Id of the alarm rule to be deleted")
//             @Valid @NotNull(message = "alarm_rule.rule_id_not_empty")
//             @PathVariable("rule_id")
//             Long ruleId
//     );

//     /**
//      * Activate multiple rules: PATCH /alarm/rules/active
//      *
//      * @return AlarmRuleManipulateResultObject
//      */
//     @Operation(summary = "Activate multiple alarm rules")
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true,
//             in = ParameterIn.HEADER, description = "Bearer Token type")

//     @ResponseStatus(HttpStatus.NO_CONTENT)
//     @PatchMapping("active")
//     ResponseEntity<AlarmRuleManipulateResultObject> activateRules(
//             @Parameter(description = "Id of an agency that the rule belongs to")
//             @PathVariable("agency_id") Integer agencyId,

//             @Parameter(description = "List of ids of alarm rules to be activated.")
//             @Valid
//             @RequestBody
//             AlarmRuleIdListRequestVO ruleIdListVO);

//     /**
//      * Deactivate multiple rules: PATCH /alarm/rules/inactive
//      *
//      * @return AlarmRuleManipulateResultObject
//      */
//     @Operation(summary = "Deactivate multiple alarm rules")
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true,
//             in = ParameterIn.HEADER, description = "Bearer Token type")

//     @ResponseStatus(HttpStatus.NO_CONTENT)
//     @PatchMapping("inactive")
//     ResponseEntity<AlarmRuleManipulateResultObject> deactivateRules(
//             @Parameter(description = "Id of an agency that the rule belongs to")
//             @PathVariable("agency_id") Integer agencyId,

//             @Parameter(description = "List of id of alarm rules to be deactivated.")
//             @Valid
//             @RequestBody
//             AlarmRuleIdListRequestVO ruleIdListVO);

//     /**
//      * Update a given rule: PUT /alarm/rules/{rule_id}
//      *
//      * @param ruleId
//      * @param ruleUpdateVO
//      * @return AlarmRuleManipulateResultObject
//      */
//     @Operation(summary = "Update a given alarm rule")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "400", description = "Bad Request"),
//             @ApiResponse(responseCode = "404", description = "Not Found") })

//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true,
//             in = ParameterIn.HEADER, description = "Bearer Token type")

//     @ResponseStatus(HttpStatus.NO_CONTENT)
//     @PutMapping("{rule_id}")
//     ResponseEntity<AlarmRuleManipulateResultObject> updateRule(
//             @Parameter(description = "Id of an agency that the rule belongs to")
//             @PathVariable("agency_id") Integer agencyId,

//             @Parameter(description = "Id of alarm rule to be updated")
//             @Valid @NotNull(message = "alarm_rule.rule_id_not_empty")
//             @PathVariable("rule_id") Long ruleId,

//             @Parameter(name = "request_body", description = "Information for alarm rule to be updated")
//             @Valid
//             @RequestBody
//             AlarmRuleUpdateRequestVO ruleUpdateVO
//     );

//     /**
//      * Delete multiple rules: DELETE /alarm/rules?rule_ids=a,b,c
//      *
//      * @param ruleIds
//      * @return AlarmRuleManipulateResultObject
//      */
//     @Operation(summary = "Delete multiple alarm rules")
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true,
//             in = ParameterIn.HEADER, description = "Bearer Token type")

//     @ResponseStatus(HttpStatus.NO_CONTENT)
//     @DeleteMapping
//     ResponseEntity<AlarmRuleManipulateResultObject> deleteRules(
//             @Parameter(description = "Id of an agency that the rule belongs to")
//             @PathVariable("agency_id") Integer agencyId,

//             @Parameter(description = "List of id of alarm rules to be deleted")
//             @Valid @NotEmpty(message = "alarm_rule.rule_ids_not_empty")
//             @RequestParam(value = "rule_ids")
//             List<Long> ruleIds);

//     /**
//      * Search alarm rules GET /alarm/rules?agency_id={agency_id}&...
//      *
//      * @param agencyId
//      * @param text
//      * @param fromDate
//      * @param toDate
//      * @param analysisId
//      * @param categoryId
//      * @param ownerId
//      * @param status
//      * @param orderByColumns
//      * @param page
//      * @param size
//      * @return AlarmRuleSearchListResultObject
//      */
//     @Operation(summary = "Get all alarm rules with filters, pagination and sorting")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "400", description = "Bad Request"),
//             @ApiResponse(responseCode = "404", description = "Not Found") })

//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true,
//             in = ParameterIn.HEADER, description = "Bearer Token type")

//     @ResponseStatus(HttpStatus.OK)
//     @GetMapping
//     ResponseEntity<AlarmRuleSearchResponseVO> searchRules(
//             @Parameter(description = "Id of an agency that the rule belongs to")
//             @PathVariable("agency_id") Integer agencyId,

//             @Parameter(description = "Text to search")
//             @RequestParam(value = "text", required = false)
//             String text,

//             @Parameter(description = "This day must be lower than created_at")
//             @RequestParam(value = "from_date", required = false)
//             Long fromDate,

//             @Parameter(description = "This day must be greater than created_at")
//             @RequestParam(value = "to_date", required = false)
//             Long toDate,

//             @Parameter(description = "Day of the week must be between MONDAY -> SUNDAY")
//             @RequestParam(value = "week_days", required = false)
//             DayOfWeek[] weekDays,

//             @Parameter(description = "This date time must be lower than alarm active time")
//             @RequestParam(value = "from_time", required = false)
//             LocalTime fromTime,

//             @Parameter(description = "This date time must be greater than alarm active time")
//             @RequestParam(value = "to_time", required = false)
//             LocalTime toTime,

//             @Parameter(description = "Id of an analysis that the rules belong to")
//             @RequestParam(value = "analysis_id", required = false)
//             String analysisId,

//             @Parameter(description = "Id of an category that the rules belong to")
//             @RequestParam(value = "category_id", required = false)
//             Long categoryId,

//             @Parameter(description = "Id of an owner that the rules belong to")
//             @RequestParam(value = "owner_id", required = false)
//             Long ownerId,

//             @Parameter(description = "The status of rules")
//             @RequestParam(value = "status", required = false)
//             String status,

//             @Parameter(description = "Fields will be used to sort returning data")
//             @RequestParam(value = "sort", required = false)
//             String[] orderByColumns,

//             @Parameter(description = "Page is used to page the returning result")
//             @RequestParam(value = "page", required = false)
//             Integer page,

//             @Parameter(description = "Size is used to size the returning result")
//             @RequestParam(value = "size", required = false)
//             Integer size
//     );

//     /**
//      * Retrieve all available user who can subscribe this rule: GET /alarm/rules/subscribers/available
//      *
//      * @param agencyId
//      * @param ruleId
//      * @param text
//      * @param page
//      * @param size
//      * @return SubscriberSearchResultObject
//      */
//     @Operation(summary = "Retrieve all available user who can subscribe this rule")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "404", description = "Not Found") })

//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true,
//             in = ParameterIn.HEADER, description = "Bearer Token type")

//     @ResponseStatus(HttpStatus.OK)
//     @GetMapping("subscribers/available")
//     ResponseEntity<SubscriberSearchResultObject> searchAvailableSubscribers(
//             @Parameter(description = "Id of an agency that the rule belongs to")
//             @PathVariable("agency_id") Integer agencyId,

//             @Parameter(description = "Id of the alarm rule to search subscribers")
//             @RequestParam(value = "rule_id", required = false)
//             Long ruleId,

//             @Parameter(description = "Exclude users by id")
//             @RequestParam(value = "exclude_ids", required = false)
//             Long[] excludeIds,

//             @Parameter(description = "text of rule to search")
//             @RequestParam(value = "text", required = false)
//             String text,

//             @Parameter(description = "Fields will be used to sort returning data")
//             @RequestParam(value = "sort", required = false)
//             String[] orderByColumns,

//             @Parameter(description = "Page is used to page the returning result")
//             @RequestParam(value = "page", required = false)
//             Integer page,

//             @Parameter(description = "Size is used to size the returning result")
//             @RequestParam(value = "size", required = false)
//             Integer size
//     );

//     /**
//      * Search subscribers by rule's id and user's name or email: GET /alarm/rules/{rule_id}/subscribers
//      *
//      * @param ruleId
//      * @param text
//      * @param orderByColumns
//      * @param page
//      * @param size
//      * @return SubscriberSearchListResultObject
//      */
//     @Operation(summary = "Retrieve a list of subscribers depending on rule_id")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "400", description = "Bad Request") })

//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true,
//             in = ParameterIn.HEADER, description = "Bearer Token type")

//     @ResponseStatus(HttpStatus.OK)
//     @GetMapping("{rule_id}/subscribers")
//     ResponseEntity<SubscriberSearchResultObject> searchSubscribers(
//             @Parameter(description = "Id of an agency that the rule belongs to")
//             @PathVariable("agency_id") Integer agencyId,

//             @Parameter(description = "Id of the alarm rule to search subscribers")
//             @Valid @NotNull(message = "alarm_rule.rule_id_not_empty")
//             @NotFoundAlarmRule(message = "alarm_rule.rule_not_found")
//             @PathVariable("rule_id") Long ruleId,

//             @Parameter(description = "It will be used to search subscribers by name, email")
//             @RequestParam(value = "text", required = false)
//             String text,

//             @Parameter(description = "It will be used to search subscribers by status")
//             @RequestParam(value = "status", required = false)
//             String status,

//             @Parameter(description = "Fields will be used to sort returning data")
//             @RequestParam(value = "sort", required = false)
//             String[] orderByColumns,

//             @Parameter(description = "Page is used to page the returning result")
//             @RequestParam(value = "page", required = false)
//             Integer page,

//             @Parameter(description = "Size is used to size the returning result")
//             @RequestParam(value = "size", required = false)
//             Integer size
//     );

//     /**
//      * Add or remove subscribers for a specific alarm rule: PATCH /alarm/rules/{rule_id}/subscribers
//      *
//      * @param ruleId
//      * @param subscriberIdList
//      * @return AlarmRuleManipulateResultObject
//      */
//     @Operation(summary = "Add or remove subscribers to a specific alarm rule")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "400", description = "Bad Request") })

//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true,
//             in = ParameterIn.HEADER, description = "Bearer Token type")

//     @ResponseStatus(HttpStatus.NO_CONTENT)
//     @PatchMapping("{rule_id}/subscribers")
//     ResponseEntity<AlarmRuleManipulateResultObject> updateSubscribersRule(
//             @Parameter(description = "Id of an agency that the rule belongs to")
//             @PathVariable("agency_id") Integer agencyId,

//             @Parameter(description = "Id of a rule that subscribers will be added to or removed from")
//             @Valid @NotNull(message = "alarm_rule.rule_id_not_empty")
//             @NotFoundAlarmRule(message = "alarm_rule.rule_not_found")
//             @PathVariable("rule_id")
//             Long ruleId,

//             @Parameter(description = "List of ids of subscribers to be added or removed")
//             @Valid
//             @RequestBody
//             SubscriberIdListRequestVO subscriberIdList
//     );

//     /**
//      * Update subscription rules for the current user (depend on action): PATCH /alarm/rules/subscription
//      *
//      * @param ruleIdList
//      * @return AlarmRuleManipulateResultObject
//      */
//     @Operation(summary = "Update subscription rules for the current user (depend on action).")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "400", description = "Bad Request") })

//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true,
//             in = ParameterIn.HEADER, description = "Bearer Token type")

//     @ResponseStatus(HttpStatus.NO_CONTENT)
//     @PatchMapping("subscription")
//     ResponseEntity<AlarmRuleManipulateResultObject> updateSubscriptionRuleForCurrentUser(
//             @Parameter(description = "Id of an agency that the rule belongs to")
//             @PathVariable("agency_id") Integer agencyId,

//             @Parameter(description = "List of IDs of alarm rules which will be subscribed by the current user.")
//             @Valid
//             @RequestBody
//             SubscribeRulesCurrentUserRequestVO ruleIdList
//     );

//     /**
//      * Search intersections: GET /alarm/rules/{rule_id}/intersections
//      *
//      * @param ruleId
//      * @param text
//      * @param orderByColumns
//      * @param page
//      * @param size
//      * @return IntersectionSearchResultObject
//      */
//     @Operation(summary = "Retrieve a list of intersections depending on rule_id")
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true,
//             in = ParameterIn.HEADER, description = "Bearer Token type")
//     @ResponseStatus(HttpStatus.OK)
//     @GetMapping("{rule_id}/intersections")
//     ResponseEntity<IntersectionSearchResultObject> searchIntersections(
//             @Parameter(description = "Id of an agency that the rule belongs to")
//             @PathVariable("agency_id") Integer agencyId,

//             @Parameter(description = "Id of the alarm rule to search intersections")
//             @Valid @NotNull(message = "alarm_rule.rule_id_not_empty")
//             @NotFoundAlarmRule(message = "alarm_rule.rule_not_found")
//             @PathVariable("rule_id")
//             Long ruleId,

//             @Parameter(description = "It will be used to search intersections by name, email")
//             @RequestParam(value = "text", required = false)
//             String text,

//             @Parameter(description = "It will be used to search intersections by status")
//             @RequestParam(value = "status", required = false)
//             String status,

//             @Parameter(description = "Fields will be used to sort returning data")
//             @RequestParam(value = "sort", required = false)
//             String[] orderByColumns,

//             @Parameter(description = "Page is used to page the returning result")
//             @RequestParam(value = "page", required = false)
//             Integer page,

//             @Parameter(description = "Size is used to size the returning result")
//             @RequestParam(value = "size", required = false)
//             Integer size
//     );

//     /**
//      * Retrieve all available intersections for the current user can select: GET /alarm/rules/intersections/available
//      *
//      * @param agencyId
//      * @param searchRequestVO
//      * @return IntersectionSearchResultObject
//      */
//     @Operation(summary = "Retrieve all available intersections for the current user can select")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "404", description = "Not Found") })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true,
//             in = ParameterIn.HEADER, description = "Bearer Token type")
//     @ResponseStatus(HttpStatus.OK)
//     @PostMapping("intersections/available")
//     ResponseEntity<IntersectionSearchResultObject> searchAvailableIntersections(
//             @Parameter(description = "Id of an agency that the rule belongs to")
//             @PathVariable("agency_id") Integer agencyId,

//             @Parameter(name = "request_body", description = "Information to query intersections")
//             @Valid
//             @RequestBody
//             IntersectionSearchRequestVO searchRequestVO
//     );

//     /**
//      * Update intersections to a specific alarm rule (delete or add depends on action): PATCH
//      * /alarm/rules/{rule_id}/intersections
//      *
//      * @param ruleId
//      * @param intersectionIdListVO
//      * @return AlarmRuleManipulateResultObject
//      */

//     @Operation(summary = "Update intersections to a specific alarm rule (delete or add depends on action)")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "400", description = "Bad Request") })

//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true,
//             in = ParameterIn.HEADER, description = "Bearer Token type")

//     @ResponseStatus(HttpStatus.NO_CONTENT)
//     @PatchMapping("{rule_id}/intersections")
//     ResponseEntity<AlarmRuleManipulateResultObject> updateIntersections(
//             @Parameter(description = "Id of an agency that the rule belongs to")
//             @PathVariable("agency_id") Integer agencyId,

//             @Parameter(description = "Id of a rule that intersections will be added")
//             @Valid @NotNull(message = "alarm_rule.rule_id_not_empty")
//             @NotFoundAlarmRule(message = "alarm_rule.rule_not_found")
//             @PathVariable("rule_id")
//             Long ruleId,

//             @Parameter(description = "List of ids of intersections to be added.")
//             @Valid
//             @RequestBody
//             IntersectionIdListRequestVO intersectionIdListVO
//     );

//     @ResponseStatus(HttpStatus.NO_CONTENT)
//     @PostMapping("{rule_id}/execute")
//     ResponseEntity<AlarmRuleManipulateResultObject> manualTriggerAlarmRule(
//             @Parameter(description = "Id of an agency that the rule belongs to")
//             @PathVariable("agency_id") Integer agencyId,

//             @Parameter(description = "Id of a rule that will be executed")
//             @Valid @NotNull(message = "alarm_rule.rule_id_not_empty")
//             @NotFoundAlarmRule(message = "alarm_rule.rule_not_found")
//             @PathVariable("rule_id") Long ruleId,

//             @Parameter(description = "The date to execute the alarm rule.")
//             @Valid
//             @RequestBody ManualAlarmRequestVO manualAlarmRequestVO
//     );

// }
