// /*
//  * Copyright (C) Siemens.  All Rights Reserved.
//  *
//  * Source      : AlarmRecordController.java
//  * Project     : SPM Platform
//  */

// package com.siemens.spm.rule.api.controller;

// import java.time.LocalDateTime;

// import org.springframework.format.annotation.DateTimeFormat;
// import org.springframework.http.HttpStatus;
// import org.springframework.http.ResponseEntity;
// import org.springframework.web.bind.annotation.GetMapping;
// import org.springframework.web.bind.annotation.PathVariable;
// import org.springframework.web.bind.annotation.PostMapping;
// import org.springframework.web.bind.annotation.RequestBody;
// import org.springframework.web.bind.annotation.RequestMapping;
// import org.springframework.web.bind.annotation.RequestParam;
// import org.springframework.web.bind.annotation.ResponseStatus;

// import com.siemens.spm.common.api.PublicController;
// import com.siemens.spm.rule.api.vo.request.AlarmRecordNotifyRequestVO;
// import com.siemens.spm.rule.api.vo.response.AlarmRecordDetailsResponseVO;
// import com.siemens.spm.rule.api.vo.response.AlarmRecordManipulateResponseVO;
// import com.siemens.spm.rule.api.vo.response.AlarmRecordSearchResponseVO;
// import com.siemens.spm.rule.api.vo.response.AlarmRecordSummaryResponseVO;

// import io.swagger.v3.oas.annotations.Operation;
// import io.swagger.v3.oas.annotations.Parameter;
// import io.swagger.v3.oas.annotations.enums.ParameterIn;
// import io.swagger.v3.oas.annotations.responses.ApiResponse;
// import io.swagger.v3.oas.annotations.responses.ApiResponses;
// import jakarta.validation.Valid;
// import jakarta.validation.constraints.NotNull;

// @RequestMapping(AlarmRecordController.URL_ROOT)
// public interface AlarmRecordController extends PublicController {

//     String VERSION = "/v1";
//     String ALARM_RECORD_RESOURCE = "/alarm/records";
//     String URL_ROOT = PUBLIC_API + VERSION + "/agencies/{agency_id}" + ALARM_RECORD_RESOURCE;

//     String ALARM_RECORD_DETAIL_RESOURCE = "/{record_id}";
//     String SUMMARIES_RESOURCE = "/summaries";
//     String EXPORT_RESOURCE = "/export";
//     String NOTIFICATIONS_RESOURCE = "/notifications";

//     /**
//      * Get alarm record in detail: GET /alarm/records/{record_id}
//      *
//      * @param recordId
//      * @return ResponseEntity<AlarmRecordDetailsResponseVO>
//      */
//     @Operation(summary = "Retrieve an alarm record in detail by id")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "400", description = "Bad Request"),
//             @ApiResponse(responseCode = "404", description = "Not Found")
//     })

//     @Parameter(name = "Authorization", required = true, in = ParameterIn.HEADER, description = "Bearer Token type")

//     @ResponseStatus(HttpStatus.OK)
//     @GetMapping(ALARM_RECORD_DETAIL_RESOURCE)
//     ResponseEntity<AlarmRecordDetailsResponseVO> getRecordDetail(
//             @Parameter(description = "Id of an agency that the record belongs to")
//             @PathVariable("agency_id") Integer agencyId,

//             @Parameter(description = "Id of the alarm record need to retrieve")
//             @Valid
//             @NotNull(message = "alarm_record.record_id_not_empty")
//             @PathVariable("record_id")
//             Long recordId
//     );

//     /**
//      * Filter/search alarm record: GET /alarm/records?agency_id={agency_id}&...
//      *
//      * @param agencyId
//      * @param text
//      * @param alarmTimeFrom
//      * @param alarmTimeTo
//      * @param analysisId
//      * @param categoryId
//      * @param intersectionId
//      * @param orderByColumns
//      * @param page
//      * @param size
//      * @return ResponseEntity<AlarmRecordSearchResponseVO>
//      */
//     @Operation(summary = "Search an alarm record by it's information")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "400", description = "Bad Request"),
//             @ApiResponse(responseCode = "404", description = "Not Found")
//     })

//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true,
//             in = ParameterIn.HEADER, description = "Bearer Token type")

//     @ResponseStatus(HttpStatus.OK)
//     @GetMapping
//     ResponseEntity<AlarmRecordSearchResponseVO> searchRecords(
//             @Parameter(description = "Id of an agency that the record belongs to")
//             @PathVariable("agency_id") Integer agencyId,

//             @Parameter(description = "text to search")
//             @RequestParam(value = "text", required = false)
//             String text,

//             @Parameter(description = "This date time must be lower than alarm_time")
//             @RequestParam(value = "from_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
//             LocalDateTime alarmTimeFrom,

//             @Parameter(description = "This date time must be greater than alarm_time")
//             @RequestParam(value = "to_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
//             LocalDateTime alarmTimeTo,

//             @Parameter(description = "Id of analysis that the records belong to")
//             @RequestParam(value = "analysis_id", required = false)
//             String analysisId,

//             @Parameter(description = "Id of an category that the records belong to")
//             @RequestParam(value = "category_id", required = false)
//             Long categoryId,

//             @Parameter(description = "Id or name of intersection that the records belong to")
//             @RequestParam(value = "intersection", required = false)
//             String intersection,

//             @Parameter(description = "Id of intersection that the records belong to")
//             @RequestParam(value = "intersection_id", required = false)
//             String intersectionId,

//             @Parameter(description = "Fields will be used to sort returning data")
//             @RequestParam(value = "sort", required = false)
//             String[] orderByColumns,

//             @Parameter(description = "Page is used to page the returning result")
//             @RequestParam(value = "page", required = false)
//             Integer page,

//             @Parameter(description = "Size is used to size the returning result")
//             @RequestParam(value = "size", required = false)
//             Integer size
//     );

//     /**
//      * Summary alarm record: GET /alarm/records/summaries
//      *
//      * @param agencyId
//      * @param text
//      * @param fromTime
//      * @param toTime
//      * @param analysisId
//      * @param categoryId
//      * @param intersectionId
//      * @param intersection
//      * @return ResponseEntity<AlarmRecordSummaryResultObject>
//      */
//     @Operation(summary = "Retrieve a summary of alarm record")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "400", description = "Bad Request"),
//             @ApiResponse(responseCode = "404", description = "Not Found")
//     })

//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true,
//             in = ParameterIn.HEADER, description = "Bearer Token type")

//     @ResponseStatus(HttpStatus.OK)
//     @GetMapping(SUMMARIES_RESOURCE)
//     ResponseEntity<AlarmRecordSummaryResponseVO> summaryRecords(
//             @Parameter(description = "Id of an agency that the record belongs to")
//             @PathVariable("agency_id") Integer agencyId,

//             @Parameter(description = "text to search")
//             @RequestParam(value = "text", required = false)
//             String text,

//             @Parameter(description = "This date time must be lower than alarm_time")
//             @RequestParam(value = "from_time", required = true)
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
//             LocalDateTime fromTime,

//             @Parameter(description = "This date time must be greater than alarm_time")
//             @RequestParam(value = "to_time", required = true)
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
//             LocalDateTime toTime,

//             @Parameter(description = "Id of analysis that the records belong to")
//             @RequestParam(value = "analysis_id", required = false)
//             String analysisId,

//             @Parameter(description = "Id of category that the records belong to")
//             @RequestParam(value = "category_id", required = false)
//             Long categoryId,

//             @Parameter(description = "Id or name of intersection that the records belong to")
//             @RequestParam(value = "intersection", required = false)
//             String intersection,

//             @Parameter(description = "Id of intersection that the records belong to")
//             @RequestParam(value = "intersection_id", required = false)
//             String intersectionId
//     );

//     /**
//      * Summary alarm record: GET /alarm/records/summaries/export
//      *
//      * @param agencyId
//      * @param text
//      * @param fromTime
//      * @param toTime
//      * @param analysisId
//      * @param categoryId
//      * @param intersectionId
//      * @param intersection
//      * @return ResponseEntity<AlarmRecordSummaryResultObject>
//      */
//     @Operation(summary = "Retrieve a summary of alarm record")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "400", description = "Bad Request"),
//             @ApiResponse(responseCode = "404", description = "Not Found")
//     })

//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true,
//             in = ParameterIn.HEADER, description = "Bearer Token type")

//     @ResponseStatus(HttpStatus.OK)
//     @GetMapping(SUMMARIES_RESOURCE + EXPORT_RESOURCE)
//     ResponseEntity<Object> exportRecordSummary(
//             @Parameter(description = "Id of an agency that the record belongs to")
//             @PathVariable("agency_id") Integer agencyId,

//             @Parameter(description = "text to search")
//             @RequestParam(value = "text", required = false)
//             String text,

//             @Parameter(description = "This date time must be lower than alarm_time")
//             @RequestParam(value = "from_time", required = true)
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
//             LocalDateTime fromTime,

//             @Parameter(description = "This date time must be greater than alarm_time")
//             @RequestParam(value = "to_time", required = true)
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
//             LocalDateTime toTime,

//             @Parameter(description = "Id of analysis that the records belong to")
//             @RequestParam(value = "analysis_id", required = false)
//             String analysisId,

//             @Parameter(description = "Id of category that the records belong to")
//             @RequestParam(value = "category_id", required = false)
//             Long categoryId,

//             @Parameter(description = "Id or name of intersection that the records belong to")
//             @RequestParam(value = "intersection", required = false)
//             String intersection,

//             @Parameter(description = "Id of intersection that the records belong to")
//             @RequestParam(value = "intersection_id", required = false)
//             String intersectionId,

//             @Parameter(description = "Report name")
//             @RequestParam(value = "report_name", required = false)
//             String reportName,

//             @Parameter(description = "Report type")
//             @RequestParam(value = "report_type", required = false)
//             String reportType
//     );

//     /**
//      * Notify alarm record to other users: POST /alarm/records/notifications
//      *
//      * @param notifyRequest
//      * @return ResponseEntity<AlarmRecordManipulateResultObject>
//      */
//     @Operation(summary = "Notify alarm record to other users")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "400", description = "Bad Request"),
//             @ApiResponse(responseCode = "404", description = "Not Found")
//     })

//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true,
//             in = ParameterIn.HEADER, description = "Bearer Token type")

//     @ResponseStatus(HttpStatus.OK)
//     @PostMapping(NOTIFICATIONS_RESOURCE)
//     ResponseEntity<AlarmRecordManipulateResponseVO> notifyRecord(
//             @Parameter(description = "Id of an agency that the record belongs to")
//             @PathVariable("agency_id") Integer agencyId,

//             @Parameter(description = "notifyRequest contains alarm record id and a list of user's id")
//             @Valid
//             @RequestBody
//             AlarmRecordNotifyRequestVO notifyRequest
//     );

// }
