/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AlarmRuleEvaluationJobUpdateRequestVO.java
 * Project     : SPM Platform
 */

package com.siemens.spm.rule.api.vo.request;

import java.io.Serializable;

import jakarta.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_NULL)
public class AlarmRuleEvaluationJobUpdateRequestVO implements Serializable {

    private static final long serialVersionUID = -7273703829730736170L;

    @NotNull(message = "alarm_rule_evaluation_job.agency_id_not_null")
    @JsonProperty("agency_id")
    private Integer agencyId;

    @NotNull(message = "alarm_rule_evaluation_job.job_id_not_null")
    @JsonProperty("job_id")
    private String jobId;

    @NotNull(message = "alarm_rule_evaluation_job.intersection_id_not_null")
    @JsonProperty("intersection_id")
    private String intersectionId;

}
