/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AlarmEvaluationService.java
 * Project     : SPM Platform
 */

package com.siemens.spm.rule.api.boundary;

import com.siemens.spm.common.shared.vo.SimpleResultObject;
import com.siemens.spm.rule.api.vo.request.AlarmRuleEvaluationJobUpdateRequestVO;
import com.siemens.spm.rule.api.vo.request.AlarmRulesScanOnDemandRequestVO;

public interface AlarmEvaluationService {

    /**
     * Scan alarm rules to check if they can be evaluated or not
     *
     * @return SimpleResultObject
     */
    void scanRulesInternal(Integer agencyId);

    /**
     * Update rule evaluation job (after perflog of an intersection was evaluated
     * successfully)
     *
     * @param requestVO AlarmRuleEvaluationJobUpdateRequestVO
     * @return SimpleResultObject
     */
    SimpleResultObject updateRuleEvaluationJobInternal(AlarmRuleEvaluationJobUpdateRequestVO requestVO);

    /**
     * Scanning alarm rules on demand
     *
     * @param requestVO AlarmRulesScanOnDemandRequestVO
     * @return SimpleResultObject
     */
    SimpleResultObject scanRulesOnDemandInternal(AlarmRulesScanOnDemandRequestVO requestVO);

}
