/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AlarmRecordSearchRequestVO.java
 * Project     : SPM Platform
 */

package com.siemens.spm.rule.api.vo.request;

import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class AlarmRecordSearchRequestVO {

    @JsonProperty("agency_id")
    private Integer agencyId;

    @JsonProperty("alarm_time_from")
    private LocalDateTime alarmTimeFrom;

    @JsonProperty("alarm_time_to")
    private LocalDateTime alarmTimeTo;

    @JsonProperty("analysis_id")
    private String analysisId;

    @JsonProperty("category_id")
    private Long categoryId;

    // this is for searching by intersection name or intersection id
    @JsonProperty("intersection")
    private String intersection;

    // this is for searching by intersection id only
    @JsonProperty("intersection_id")
    private String intersectionId;

    @JsonProperty("order_by_columns")
    private String[] orderByColumns;

    @JsonProperty("page")
    private Integer page;

    @JsonProperty("size")
    private Integer size;

    @JsonProperty("text")
    private String text;

}
