/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AlarmRuleManipulateResultObject.java
 * Project     : SPM Platform
 */

package com.siemens.spm.rule.api.vo.response;

import org.springframework.http.HttpStatus;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.siemens.spm.common.shared.vo.AbstractSimpleResultObject;
import com.siemens.spm.rule.api.vo.response.AlarmRuleManipulateResultObject.AlarmRuleManipulateStatusCode;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class AlarmRuleManipulateResultObject extends AbstractSimpleResultObject<AlarmRuleManipulateStatusCode> {

    private static final long serialVersionUID = -61388668131616576L;

    private AlarmRuleManipulateStatusCode statusCode;

    /**
     * Constructor to set status to other than success
     *
     * @param statusCode
     */
    public AlarmRuleManipulateResultObject(AlarmRuleManipulateStatusCode statusCode) {
        super(statusCode);
    }

    @Override
    public AlarmRuleManipulateStatusCode getStatusCode() {
        return statusCode;
    }

    @Override
    public HttpStatus getHttpStatus() {
        return statusCode.getHttpStatus();
    }

    @Override
    public String getErrorFieldName() {
        return statusCode.getErrorField();
    }

    @Override
    protected void setStatusCode(AlarmRuleManipulateStatusCode value) {
        this.statusCode = value;
    }

    @Override
    protected AlarmRuleManipulateStatusCode getErrorStatusValue() {

        return AlarmRuleManipulateStatusCode.ERROR;
    }

    @Override
    public AlarmRuleManipulateStatusCode getSuccessfulStatusValue() {
        return AlarmRuleManipulateStatusCode.SUCCESS;
    }

    @Override
    public String getMessage() {
        return statusCode.getMessage();
    }

    @AllArgsConstructor
    @Getter
    public enum AlarmRuleManipulateStatusCode {
        SUCCESS("success", HttpStatus.OK),
        NO_CONTENT("no_content", HttpStatus.NO_CONTENT),
        CREATED("created", HttpStatus.CREATED),

        NO_ACTION("action", "no_action", HttpStatus.BAD_REQUEST),
        INTERSECTION_SCOPE_INVALID("intersection: scope", "rules.intersections.scope_invalid",
                HttpStatus.BAD_REQUEST),
        // below status codes map 1-1 with status code of
        // UsersInAgencyVerifyResultObject and
        // IntersectionsInAgencyVerifyResultObject
        MISSING_AGENCY_ID("agency_id", "agency_id_is_missing", HttpStatus.BAD_REQUEST),
        AGENCY_NOT_FOUND("agency_id", "agency_not_found", HttpStatus.BAD_REQUEST),
        USER_NOT_FOUND("subscriber_ids", "user_not_found", HttpStatus.BAD_REQUEST),
        USER_NOT_UNDER_AGENCY_MANAGEMENT("subscriber_ids", "user_not_under_agency_management", HttpStatus.BAD_REQUEST),
        INTERSECTION_NOT_FOUND("intersection_ids", "intersection_not_found", HttpStatus.BAD_REQUEST),
        INTERSECTION_NOT_UNDER_AGENCY_MANAGEMENT("intersection_ids", "intersection_not_under_agency_management",
                HttpStatus.BAD_REQUEST),
        INTERSECTION_EMPTY("alarm_rule.intersection_ids_not_empty", HttpStatus.BAD_REQUEST),
        // end status codes mapping
        EXCEED_MAX_NUMBER_OF_INTERSECTIONS("INTERSECTION_NOT_UNDER_AGENCY_MANAGEMENT", HttpStatus.BAD_REQUEST),
        RULE_NOT_FOUND("alarm_rule.rule_not_found", HttpStatus.BAD_REQUEST),
        RULE_INACTIVE("alarm_rule.rule_inactive", HttpStatus.BAD_REQUEST),
        OVERNIGHT_NOT_SUPPORTED("alarm_rule.overnight_not_supported", HttpStatus.BAD_REQUEST),
        NO_DATA("no_data", HttpStatus.NO_CONTENT),

        ERROR("unknown_exception", HttpStatus.INTERNAL_SERVER_ERROR),
        TIME_ZONE_NOT_FOUND("timezone_not_found", HttpStatus.BAD_REQUEST),
        TIME_ZONE_NOT_AVAILABLE_IN_AGENCY("timezone_not_available", HttpStatus.BAD_REQUEST),
        ;

        private final String errorField;
        private final String message;
        private final HttpStatus httpStatus;

        AlarmRuleManipulateStatusCode(String message, HttpStatus httpStatus) {
            this(null, message, httpStatus);
        }
    }
}
