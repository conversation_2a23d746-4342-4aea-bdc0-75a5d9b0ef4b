/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AlarmRuleSearchResponseVO.java
 * Project     : SPM Platform
 */
package com.siemens.spm.rule.api.vo.response;

import java.io.Serializable;
import java.util.List;

import org.springframework.http.HttpStatus;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.siemens.spm.common.shared.vo.AbstractResultObject;
import com.siemens.spm.rule.api.vo.AlarmRuleSearchVO;
import com.siemens.spm.rule.api.vo.response.AlarmRuleSearchResponseVO.StatusCode;
import com.siemens.spm.rule.api.vo.response.AlarmRuleSearchResponseVO.ResponseData;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class AlarmRuleSearchResponseVO extends AbstractResultObject<ResponseData, StatusCode> {

    private static final long serialVersionUID = 3656870235628394739L;

    private ResponseData data;

    private StatusCode statusCode;

    /**
     * Constructor for SUCCESS case
     *
     * @param data
     */
    public AlarmRuleSearchResponseVO(ResponseData data) {
        super(data);
    }

    @Override
    public HttpStatus getHttpStatus() {
        return statusCode.getHttpStatus();
    }

    @Override
    public StatusCode getStatusCode() {
        return statusCode;
    }

    /**
     * Constructor to set status to other than success
     *
     * @param data
     * @param statusCode
     */
    public AlarmRuleSearchResponseVO(ResponseData data, StatusCode statusCode) {
        super(data, statusCode);
    }

    @Override
    public ResponseData getData() {
        return data;
    }

    @Override
    protected void setData(ResponseData value) {
        data = value;
    }

    @Override
    protected StatusCode getErrorStatusValue() {
        return StatusCode.ERROR;
    }

    @Override
    public String getErrorFieldName() {
        return statusCode.getErrorField();
    }

    @Override
    protected void setStatusCode(StatusCode value) {
        statusCode = value;
    }

    @Override
    public StatusCode getSuccessfulStatusValue() {
        return StatusCode.SUCCESS;
    }

    @Override
    public String getMessage() {
        return statusCode.getMessage();
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonInclude(Include.NON_NULL)
    public static class ResponseData implements Serializable {

        private static final long serialVersionUID = -6369509576315928129L;

        @JsonProperty("total_count")
        private Long totalCount;

        @JsonProperty("rules")
        private List<AlarmRuleSearchVO> alarmRules;

    }

    @Getter
    @AllArgsConstructor
    public enum StatusCode {
        /* OK */
        SUCCESS("success", HttpStatus.OK),
        NO_DATA("no_data", HttpStatus.NO_CONTENT),

        /* Internal Server Error */
        ERROR("unknown_exception", HttpStatus.INTERNAL_SERVER_ERROR),

        /* Bad Request */
        INVALID_INPUT_EXCEPTION("invalid_input_exception", HttpStatus.BAD_REQUEST),
        MISSING_AGENCY_ID("agency_id_not_empty", HttpStatus.BAD_REQUEST),
        AGENCY_MISMATCHED("alarm_rule.user_not_access_agency_data", HttpStatus.BAD_REQUEST),
        INVALID_SORT_COLUMN("sort", "sort_column_invalid", HttpStatus.BAD_REQUEST),
        INVALID_SORT_ORDER("sort", "sort_order_invalid", HttpStatus.BAD_REQUEST);

        private final String errorField;
        private final String message;
        private final HttpStatus httpStatus;

        StatusCode(String message, HttpStatus httpStatus) {
            this(null, message, httpStatus);
        }
    }

}
