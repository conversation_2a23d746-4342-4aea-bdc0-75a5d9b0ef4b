/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AlarmRecordSummaryVO.java
 * Project     : SPM Platform
 */

package com.siemens.spm.rule.api.vo;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_NULL)
public class AlarmRecordSummaryVO implements Serializable {

    private static final long serialVersionUID = 7767316388941166581L;
   
	@JsonProperty("rule_name")
	private String name;

	@JsonProperty("description")
	private String description;

	@JsonIgnore
	@JsonProperty("intersection_id")
	private String intersectionId;

	@JsonProperty("intersection_name")
	private String intersectionName;

	@JsonIgnore
	@JsonProperty("agency_id")
	private Integer agencyId;

	@JsonProperty("agency_name")
	private String agencyName;

	@JsonProperty("number_triggered_alarm")
	private Long numberTriggeredAlarm;
}
