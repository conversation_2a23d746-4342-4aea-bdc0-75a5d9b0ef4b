/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : SubscriberIdListVO.java
 * Project     : SPM Platform
 */

package com.siemens.spm.rule.api.vo.request;

import java.io.Serializable;
import java.util.List;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_NULL)
public class SubscriberIdListRequestVO implements Serializable {

    private static final long serialVersionUID = -2135914885512798009L;

    @Schema(description = "action is either ADD or REMOVE")
    @NotBlank(message = "alarm_rule.action_not_blank")
    @JsonProperty("action")
    private String action;

    @Schema(description = "A list of subscriber's ids")
    @NotEmpty(message = "alarm_rule.subscriber_ids_not_empty")
    @JsonProperty("subscriber_ids")
    private List<Long> subscriberIds;

}
