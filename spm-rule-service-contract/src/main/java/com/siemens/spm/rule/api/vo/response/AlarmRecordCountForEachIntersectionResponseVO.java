/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AlarmRecordCountForEachIntersectionResponseVO.java
 * Project     : SPM Platform
 */

package com.siemens.spm.rule.api.vo.response;

import java.io.Serializable;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.common.shared.vo.AbstractResultObject;
import com.siemens.spm.rule.api.vo.response.AlarmRecordCountForEachIntersectionResponseVO.ResponseData;
import com.siemens.spm.rule.api.vo.response.AlarmRecordCountForEachIntersectionResponseVO.StatusCode;

import org.springframework.http.HttpStatus;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

public class AlarmRecordCountForEachIntersectionResponseVO extends AbstractResultObject<ResponseData, StatusCode> {

    private static final long serialVersionUID = -1031124809715563863L;

    private ResponseData data;

    private StatusCode statusCode;

    /**
     * Constructor for ERROR case
     */
    public AlarmRecordCountForEachIntersectionResponseVO() {
    }

    /**
     * Constructor to set status to other than success
     *
     * @param data
     * @param statusCode
     */
    public AlarmRecordCountForEachIntersectionResponseVO(ResponseData data, StatusCode statusCode) {
        super(data, statusCode);
    }

    @Override
    protected void setStatusCode(StatusCode value) {
        statusCode = value;
    }

    /**
     * Constructor for SUCCESS case
     *
     * @param data
     */
    public AlarmRecordCountForEachIntersectionResponseVO(ResponseData data) {
        super(data);
    }

    public String getMessage() {
        return statusCode.getMessage();
    }

    @Override
    protected void setData(ResponseData value) {
        data = value;

    }

    @Override
    public HttpStatus getHttpStatus() {
        return statusCode.getHttpStatus();
    }

    @Override
    public StatusCode getStatusCode() {
        return statusCode;
    }

    @Getter
    @AllArgsConstructor
    public enum StatusCode {
        /* OK */
        SUCCESS("success", HttpStatus.OK),
        NO_DATA("no_data", HttpStatus.NO_CONTENT),

        /* Internal Server Error */
        ERROR("unknown_exception", HttpStatus.INTERNAL_SERVER_ERROR);

        private final String errorField;
        private final String message;
        private final HttpStatus httpStatus;

        StatusCode(String message, HttpStatus httpStatus) {
            this(null, message, httpStatus);
        }
    }

    @Override
    public StatusCode getSuccessfulStatusValue() {
        return StatusCode.SUCCESS;
    }

    @Override
    public ResponseData getData() {
        return data;
    }

    @Override
    public String getErrorFieldName() {
        return statusCode.getErrorField();
    }

    @Data
    @NoArgsConstructor
    @Builder
    @AllArgsConstructor
    @JsonInclude(Include.NON_NULL)
    public static class ResponseData implements Serializable {

        private static final long serialVersionUID = -3547432942835299265L;

        @JsonProperty("intersection_id_to_alarm_record_count_map")
        private Map<String, Long> intersectionIdToAlarmRecordCountMap;

    }

    @Override
    protected StatusCode getErrorStatusValue() {

        return StatusCode.ERROR;
    }
}
