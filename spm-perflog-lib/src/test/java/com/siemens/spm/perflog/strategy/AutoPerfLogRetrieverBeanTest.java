package com.siemens.spm.perflog.strategy;

import java.time.LocalDateTime;
import java.util.List;

import org.springframework.test.util.ReflectionTestUtils;

import com.siemens.spm.perflog.boundary.CachedPerfLogRetriever;
import com.siemens.spm.perflog.boundary.DirectPerfLogRetriever;
import com.siemens.spm.perflog.exception.MissedCacheException;
import com.siemens.spm.perflog.vo.PerfLogBundleVO;
import com.siemens.spm.perflog.vo.PerfLogChunkVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

class AutoPerfLogRetrieverBeanTest {

    @InjectMocks
    AutoPerfLogRetrieverBean autoPerfLogRetrieverBean;

    @Mock
    CachedPerfLogRetriever cachedPerfLogRetriever;

    @Mock
    DirectPerfLogRetriever directPerfLogRetriever;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void test_getPerfLogGap() throws MissedCacheException {
        final Integer agencyId = 1;
        final String intUUID = "000";
        final LocalDateTime fromTime = LocalDateTime.of(2022, 01, 01, 01, 01);
        final LocalDateTime toTime = LocalDateTime.of(2022, 01, 01, 02, 01);

        ReflectionTestUtils.setField(autoPerfLogRetrieverBean, "activeProfiles", "prod");
        autoPerfLogRetrieverBean.getPerfLogGap(agencyId, intUUID, fromTime, toTime);
        Mockito.verify(cachedPerfLogRetriever, Mockito.never()).getPerfLog(agencyId, intUUID, fromTime, toTime);
        Mockito.verify(directPerfLogRetriever, Mockito.atMost(1)).getPerfLog(agencyId, intUUID, fromTime, toTime);

        ReflectionTestUtils.setField(autoPerfLogRetrieverBean, "activeProfiles", "dev");
        autoPerfLogRetrieverBean.getPerfLogGap(agencyId, intUUID, fromTime, toTime);
        Mockito.verify(cachedPerfLogRetriever, Mockito.atMost(1)).getPerfLog(agencyId, intUUID, fromTime, toTime);
        Mockito.verify(directPerfLogRetriever, Mockito.never()).getPerfLog(agencyId, intUUID, fromTime, toTime);
    }

    @Test
    void test_getPerfLog_fail() throws MissedCacheException {
        Assertions.assertThrows(
                IllegalArgumentException.class,
                () -> {
                    autoPerfLogRetrieverBean.getPerfLog(null, null, null, null);
                }
        );
    }

    //    @Test
    //    void test_getPerfLog_okCache() throws MissedCacheException {
    //        final String agencyUUID = "000";
    //        final String intUUID = "000";
    //        final LocalDateTime fromTime = LocalDateTime.of(2022, 01, 01, 01, 01);
    //        final LocalDateTime toTime = LocalDateTime.of(2022, 01, 01, 02, 01);
    //
    //        Mockito.when(
    //                cachedPerfLogRetriever.getPerfLog(agencyUUID, intUUID, fromTime, toTime)
    //        ).thenReturn(
    //                PerfLogBundleVO.builder()
    //                        .perfLogChunks(
    //                                List.of(
    //                                        PerfLogChunkVO.builder()
    //                                                .perfLogEvents(
    //                                                        List.of(
    //                                                                PerfLogEventVO.builder()
    //                                                                        .event(PerfLogEventVO.Event.COORD_CYCLE_STATE_CHANGE)
    //                                                                        .build()
    //                                                        )
    //                                                )
    //                                                .build()
    //                                )
    //                        )
    //                        .build()
    //        );
    //        autoPerfLogRetrieverBean.getPerfLog(agencyUUID, intUUID, fromTime, toTime);
    //        Mockito.verify(directPerfLogRetriever, Mockito.never()).getPerfLog(agencyUUID, intUUID, fromTime, toTime);
    //    }

    //    @Test
    //    void test_getPerfLog_failCache() throws MissedCacheException {
    //        final String agencyUUID = "000";
    //        final String intUUID = "000";
    //        final LocalDateTime fromTime = LocalDateTime.of(2022, 01, 01, 01, 01);
    //        final LocalDateTime toTime = LocalDateTime.of(2022, 01, 01, 02, 01);
    //
    //        Mockito.when(
    //                cachedPerfLogRetriever.getPerfLog(agencyUUID, intUUID, fromTime, toTime)
    //        ).thenReturn(
    //                PerfLogBundleVO.builder()
    //                        .perfLogChunks(
    //                                List.of(
    //                                        PerfLogChunkVO.builder()
    //                                                .perfLogEvents(
    //                                                        List.of(
    //                                                                PerfLogEventVO.builder()
    //                                                                        .event(PerfLogEventVO.Event.COORD_CYCLE_STATE_CHANGE)
    //                                                                        .build()
    //                                                        )
    //                                                )
    //                                                .build()
    //                                )
    //                        )
    //                        .build()
    //        );
    //        autoPerfLogRetrieverBean.getPerfLog(agencyUUID, intUUID, fromTime, toTime);
    //        Mockito.verify(directPerfLogRetriever, Mockito.never()).getPerfLog(agencyUUID, intUUID, fromTime, toTime);
    //
    //        Mockito.when(
    //                cachedPerfLogRetriever.getPerfLog(agencyUUID, intUUID, fromTime, toTime)
    //        ).thenReturn(
    //                null
    //        );
    //        autoPerfLogRetrieverBean.getPerfLog(agencyUUID, intUUID, fromTime, toTime);
    //        Mockito.verify(directPerfLogRetriever, Mockito.atLeast(1)).getPerfLog(agencyUUID, intUUID, fromTime, toTime);
    //
    //        Mockito.when(
    //                cachedPerfLogRetriever.getPerfLog(agencyUUID, intUUID, fromTime, toTime)
    //        ).thenReturn(
    //                PerfLogBundleVO.builder().build()
    //        );
    //        autoPerfLogRetrieverBean.getPerfLog(agencyUUID, intUUID, fromTime, toTime);
    //        Mockito.verify(directPerfLogRetriever, Mockito.atLeast(1)).getPerfLog(agencyUUID, intUUID, fromTime, toTime);
    //    }
}
