package com.siemens.spm.perflog.persistence;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class QueueLengthAggregationTest {

    @Test
    void test_init() {
        Assertions.assertNotNull(QueueLengthAggregation.init().getSumQueueLength());
        Assertions.assertNotNull(QueueLengthAggregation.init().getNoCycle());
    }

    @Test
    void test_clone() {
        Assertions.assertNotNull(QueueLengthAggregation.clone(null));
        QueueLengthAggregation origin = new QueueLengthAggregation();

        Assertions.assertNull(QueueLengthAggregation.clone(origin).getSumQueueLength());
        Assertions.assertNull(QueueLengthAggregation.clone(origin).getNoCycle());

        origin.setNoCycle(3);
        origin.setSumQueueLength(6D);
        Assertions.assertNotSame(origin, QueueLengthAggregation.clone(origin));
        Assertions.assertEquals(6D, QueueLengthAggregation.clone(origin).getSumQueueLength());
        Assertions.assertEquals(3, QueueLengthAggregation.clone(origin).getNoCycle());
    }

    @Test
    void test_merge() {

        QueueLengthAggregation origin = new QueueLengthAggregation();
        origin.merge(null);
        Assertions.assertNull(origin.getSumQueueLength());
        Assertions.assertNull(origin.getNoCycle());

        origin.setNoCycle(3);
        origin.setSumQueueLength(6D);
        origin.merge(null);

        Assertions.assertEquals(6D, origin.getSumQueueLength());
        Assertions.assertEquals(3, origin.getNoCycle());

        QueueLengthAggregation originMerging = new QueueLengthAggregation();
        originMerging.setSumQueueLength(3D);
        origin.merge(originMerging);

        Assertions.assertEquals(9D, origin.getSumQueueLength());
        Assertions.assertEquals(3, origin.getNoCycle());

        originMerging.setSumQueueLength(null);
        originMerging.setNoCycle(3);
        origin.merge(originMerging);

        Assertions.assertEquals(9D, origin.getSumQueueLength());
        Assertions.assertEquals(6, origin.getNoCycle());
    }

}
