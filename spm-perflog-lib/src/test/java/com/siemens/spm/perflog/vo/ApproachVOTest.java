package com.siemens.spm.perflog.vo;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class ApproachVOTest {

    @Test
    void test_getApproachSpeedMetersPerSecond() {
        Assertions.assertNull(ApproachVO.builder().build().getApproachSpeedMetersPerSecond());
        Assertions.assertNotNull(ApproachVO.builder().approachSpeed(0D).build().getApproachSpeedMetersPerSecond());
        Assertions.assertEquals(0D, ApproachVO.builder().approachSpeed(0D).build().getApproachSpeedMetersPerSecond());
    }
}
