package com.siemens.spm.perflog.utils;

import com.siemens.spm.datahub.api.vo.DataHubConfigDataVO;
import com.siemens.spm.datahub.api.vo.DataHubIntersectionConfigVO;
import com.siemens.spm.datahub.api.vo.DataHubPerfLogChunkVO;
import com.siemens.spm.datahub.api.vo.DataHubPerfLogEventVO;
import com.siemens.spm.datahub.api.vo.response.DataHubMissingDataResponseVO;
import com.siemens.spm.datahub.api.vo.response.DataHubPerfLogChunkResponseVO;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.List;

class PerfLogConverterTest {

    private final LocalDateTime fromTimeTest = LocalDateTime.of(2022, 1, 1, 1, 1, 2);

    private final LocalDateTime toTimeTest = LocalDateTime.of(2022, 1, 2, 1, 1, 2);

    private final String intUUID = "intUUID_test";

    @Test
    void test_dateTimeToStr() {
        Assertions.assertNull(PerfLogConverter.dateTimeToStr(null));
        Assertions.assertEquals("2022-01-01 01:01:01",
                PerfLogConverter.dateTimeToStr(LocalDateTime.of(2022, 1, 1, 1, 1, 1)));
        Assertions.assertEquals(LocalDateTime.of(2022, 1, 1, 1, 1, 2),
                PerfLogConverter.strToDateTime("2022-01-01 01:01:02"));
        Assertions.assertNull(PerfLogConverter.strToDateTime(null));
    }

    @Test
    void test_convertPerfLogChunk() {
        DataHubPerfLogChunkResponseVO input = new DataHubPerfLogChunkResponseVO();
        input.add(missingConfig());
        input.add(missingConfigId());
        input.add(missingPerfLog());

        Assertions.assertThrows(
                IllegalArgumentException.class,
                () -> PerfLogConverter.convertPerfLogChunk(null, null, null, null)
        );

        Assertions.assertDoesNotThrow(
                () -> PerfLogConverter.convertPerfLogChunk(null, intUUID, fromTimeTest, toTimeTest)
        );

        Assertions.assertThrows(
                IllegalArgumentException.class,
                () -> PerfLogConverter.convertPerfLogChunk(input, null, null, null)
        );

        Assertions.assertThrows(
                IllegalArgumentException.class,
                () -> PerfLogConverter.convertPerfLogChunk(input, intUUID, fromTimeTest, toTimeTest)
        );

    }

    @Test
    void test_convertMissingData() {
        DataHubMissingDataResponseVO dataHubMissingDataResponseVO = new DataHubMissingDataResponseVO();

        Assertions.assertThrows(
                IllegalArgumentException.class,
                () -> PerfLogConverter.convertMissingData(null)
        );

        Assertions.assertTrue(PerfLogConverter.convertMissingData(dataHubMissingDataResponseVO).isEmpty());

        dataHubMissingDataResponseVO.setMissingTimes(
                List.of(LocalDateTime.of(2020, 1, 1, 1, 1), LocalDateTime.of(2020, 1, 1, 1, 1)));
        Assertions.assertEquals(2, PerfLogConverter.convertMissingData(dataHubMissingDataResponseVO).size());
    }

    private DataHubPerfLogChunkVO missingConfig() {
        return DataHubPerfLogChunkVO.builder()
                .configId("configID_1")
                .fromTime(fromTimeTest)
                .toTime(toTimeTest)
                .configData(null)
                .events(
                        List.of(
                                DataHubPerfLogEventVO.builder()
                                        .dateTime(fromTimeTest)
                                        .event(1)
                                        .parameter(1)
                                        .build()
                        )
                )
                .build();
    }

    private DataHubPerfLogChunkVO missingConfigId() {
        return DataHubPerfLogChunkVO.builder()
                .configId(null)
                .fromTime(fromTimeTest)
                .toTime(toTimeTest)
                .events(
                        List.of(
                                DataHubPerfLogEventVO.builder()
                                        .dateTime(fromTimeTest)
                                        .event(1)
                                        .parameter(1)
                                        .build()
                        )
                )
                .build();
    }

    private DataHubPerfLogChunkVO missingPerfLog() {
        return DataHubPerfLogChunkVO.builder()
                .configId("configID_1")
                .fromTime(fromTimeTest)
                .toTime(toTimeTest)
                .configData(DataHubConfigDataVO.builder()
                        .validFromTime(fromTimeTest)
                        .validToTime(toTimeTest)
                        .config(
                                DataHubIntersectionConfigVO.builder()
                                        .sequenceInfo(null)
                                        .build()
                        )
                        .build()
                )
                .build();
    }

}
