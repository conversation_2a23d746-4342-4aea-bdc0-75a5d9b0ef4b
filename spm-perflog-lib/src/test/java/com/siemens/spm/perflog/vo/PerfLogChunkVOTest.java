package com.siemens.spm.perflog.vo;

import com.siemens.spm.perflog.domaintype.PerfLogChunkStatus;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class PerfLogChunkVOTest {

    @Test
    void test_isNoData() {
        Assertions.assertFalse(PerfLogChunkVO.builder().build().isNoData());
        Assertions.assertFalse(PerfLogChunkVO.builder().status(PerfLogChunkStatus.NORMAL).build().isNoData());
        Assertions.assertTrue(PerfLogChunkVO.builder().status(PerfLogChunkStatus.NO_DATA).build().isNoData());
    }
}
