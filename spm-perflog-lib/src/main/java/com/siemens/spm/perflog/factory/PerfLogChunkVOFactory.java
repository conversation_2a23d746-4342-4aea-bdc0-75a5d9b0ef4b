/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : PerfLogChunkVOFactory.java
 * Project     : SPM Platform
 */
package com.siemens.spm.perflog.factory;

import java.util.ArrayList;

import com.fasterxml.jackson.core.type.TypeReference;
import com.siemens.spm.perflog.persistence.PerfLogChunk;
import com.siemens.spm.perflog.utils.PerfLogConverter;
import com.siemens.spm.perflog.utils.PerfLogJson;
import com.siemens.spm.perflog.vo.PerfLogChunkVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;

public class PerfLogChunkVOFactory {

    private PerfLogChunkVOFactory() {
    }

    /**
     * Create PerfLogChunkVO from PerfLogChunk
     *
     * @param chunk
     * @return
     */
    public static PerfLogChunkVO create(PerfLogChunk chunk) {
        PerfLogChunkVO chunkVO = PerfLogChunkVO.builder()
                .fromTime(PerfLogConverter.strToDateTime(chunk.getFromTime()))
                .toTime(PerfLogConverter.strToDateTime(chunk.getToTime()))
                .configID(chunk.getIntConfig() != null ? chunk.getIntConfig().getUuid() : null)
                .status(chunk.getStatus())
                .build();

        TypeReference<ArrayList<PerfLogEventVO>> typeRef = new TypeReference<>() {
        };
        ArrayList<PerfLogEventVO> eventVOList = PerfLogJson.serializer().fromJSONString(
                chunk.getEventLob(), typeRef);
        chunkVO.setPerfLogEvents(eventVOList);

        return chunkVO;
    }

}
