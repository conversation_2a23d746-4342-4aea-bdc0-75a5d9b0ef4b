package com.siemens.spm.perflog.utils;

import java.util.List;

import com.siemens.spm.perflog.persistence.IntersectionConfig;
import com.siemens.spm.perflog.persistence.PhaseStat;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> Mai
 */
@Slf4j
public final class PhaseUtil {
    private PhaseUtil() {
    }

    public static boolean isCoordinatedPhase(PhaseStat phaseStat, List<IntersectionConfig> intConfigList) {
        if (phaseStat == null || intConfigList == null)
            throw new IllegalArgumentException();

        // Loop from latest intConfig
        for (int i = intConfigList.size(); i-- > 0; ) {
            IntersectionConfig intConfig = intConfigList.get(i);
            if (phaseStat.getIntUUID().equalsIgnoreCase(intConfig.getIntUUID())
                    && phaseStat.getFromTimeUTC().after(intConfig.getFromTimeUtc())) {
                return intConfig.isCoordinatedPhase(phaseStat.getPhase());
            }
        }

        // NOTE: Without corresponding intConfig, consider the phase as non-coordinated
        String msg = String.format(
                "Cannot find IntersectionConfig for PhaseStat with intUUID=%s, fromTime=%s, toTime=%s",
                phaseStat.getIntUUID(), phaseStat.getFromTimeUTC(), phaseStat.getToTimeUTC());
        log.warn(msg);
        return false;
    }

}
