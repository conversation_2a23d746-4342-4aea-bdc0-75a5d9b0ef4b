/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : CachedPerfLogRetriever.java
 * Project     : SPM Platform
 */
package com.siemens.spm.perflog.boundary;

import java.time.LocalDateTime;
import java.util.List;

import com.siemens.spm.perflog.exception.MissedCacheException;
import com.siemens.spm.perflog.persistence.PerfLogChunkView;

public interface CachedPerfLogRetriever extends PerfLogRetriever {

    /**
     * Get PerfLog with corresponding intersection configuration, without PerfLog
     * events
     * 
     * @param intUUID
     * @param fromTime
     * @param toTime
     * @return
     * @throws MissedCacheException
     */
    List<PerfLogChunkView> getPerfLogChunkView(String intUUID,
                                               LocalDateTime fromTime,
                                               LocalDateTime toTime)
            throws MissedCacheException;

}
