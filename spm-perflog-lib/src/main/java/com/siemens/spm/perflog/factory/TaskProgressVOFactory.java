package com.siemens.spm.perflog.factory;

import java.time.format.DateTimeFormatter;

import com.siemens.spm.common.constant.CommonConstants;
import com.siemens.spm.common.message.MessageService;
import com.siemens.spm.common.shared.domaintype.analysis.AnalysisType;
import com.siemens.spm.common.shared.resource.TextKey;
import com.siemens.spm.common.util.BeanFinder;
import com.siemens.spm.common.util.NotificationUtil;
import com.siemens.spm.perflog.domaintype.TaskStatus;
import com.siemens.spm.perflog.persistence.TaskProgress;
import com.siemens.spm.perflog.vo.TaskProgressVO;
import com.siemens.spm.perflog.vo.UITaskStatus;

/**
 * <AUTHOR> Mai
 * <AUTHOR> Nguyen - <EMAIL>
 */
public final class TaskProgressVOFactory {
    private TaskProgressVOFactory() {
    }

    public static TaskProgressVO create(TaskProgress taskProgress) {
        if (taskProgress == null) {
            throw new IllegalArgumentException();
        }

        return TaskProgressVO.builder()
                .id(taskProgress.getId())
                .title(buildTitle(taskProgress))
                .description(buildDescription(taskProgress))
                .status(UITaskStatus.from(taskProgress.getStatus()))
                .errorMessage(buildErrorMessage(taskProgress))
                .action(NotificationUtil.buildActionNode(taskProgress.getAction(),
                        BeanFinder.getDefaultMessageService()))
                .canCancel(taskProgress.checkCancel())
                .canRemove(taskProgress.checkDelete())
                .createdAt(taskProgress.getCreatedAt())
                .lastModifiedAt(taskProgress.getLastModifiedAt())
                .build();
    }

    private static String buildTitle(TaskProgress taskProgress) {
        String title = null;

        MessageService messageService = BeanFinder.getDefaultMessageService();

        switch (taskProgress.getType()) {
        case EXPORT_PERFLOG:
            String intName = taskProgress.getIntName() != null ?
                    taskProgress.getIntName() :
                    CommonConstants.NOT_AVAILABLE;
            String[] args = new String[] { intName };
            title = messageService.getMessage(TextKey.EXPORT_PROGRESS_TITLE, args);
            break;

        case SUMMARY_REPORT:
            title = messageService.getMessage(TextKey.SUMMARY_REPORT_TITLE);
            break;

        case PERFORMANCE_METRIC:
            title = messageService.getMessage(TextKey.PERFORMANCE_METRIC_TITLE);
            break;
        case DETECTOR_REPORT:
                title = messageService.getMessage(TextKey.DETECTOR_REPORT_TITLE);
            break;
        default:
            break;
        }

        return title;
    }

    private static String buildDescription(TaskProgress taskProgress) {
        String description = null;
        MessageService messageService = BeanFinder.getDefaultMessageService();

        switch (taskProgress.getType()) {
        case EXPORT_PERFLOG:
            String[] exportArgs = new String[] {
                    taskProgress.getIntUUID(),
                    taskProgress.getFromTime().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
                    taskProgress.getToTime().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)
            };
            description = messageService.getMessage(TextKey.EXPORT_PROGRESS_DESCRIPTION, exportArgs);
            break;

        case SUMMARY_REPORT:
            String[] reportArgs = new String[] { 
                taskProgress.getReportName()
            };
            description = messageService.getMessage(TextKey.SUMMARY_REPORT_DESCRIPTION, reportArgs);
            break;

        case PERFORMANCE_METRIC:
            AnalysisType type = AnalysisType.getById(taskProgress.getReportName());
            if (type != null) {
                String[] descArgs = new String[] { 
                    type.getTranslatedName()
                };
                description = messageService.getMessage(TextKey.PERFORMANCE_METRIC_DESCRIPTION, descArgs);
            }
            break;

        case DETECTOR_REPORT:
            String[] reportArguments = new String[] { 
                taskProgress.getReportName()
            };
            description = messageService.getMessage(TextKey.DETECTOR_REPORT_DESCRIPTION, reportArguments);
            break;

        default:
            break;
        }

        return description;
    }

    /**
     * Build error message in case status progress is equal ERROR
     *
     * @param taskProgress
     * @return
     */
    private static String buildErrorMessage(TaskProgress taskProgress) {
        if (!TaskStatus.ERROR.equals(taskProgress.getStatus())) {
            return null;
        }

        MessageService messageService = BeanFinder.getDefaultMessageService();

        String errorMsg = null;
        switch (taskProgress.getType()) {
        case EXPORT_PERFLOG:
            errorMsg = messageService.getMessage(TextKey.EXPORT_FAILED_MESSAGE);
            break;

        case SUMMARY_REPORT:
            errorMsg = messageService.getMessage(TextKey.SUMMARY_REPORT_FAILED_MESSAGE);
            break;

        case PERFORMANCE_METRIC:
            errorMsg = messageService.getMessage(TextKey.PERFORMANCE_METRIC_FAILED_MESSAGE);
            break;
        case DETECTOR_REPORT:
            errorMsg = messageService.getMessage(TextKey.DETECTOR_REPORT_FAILED_MESSAGE);
            break;
        default:
            break;
        }

        return errorMsg;
    }

}
