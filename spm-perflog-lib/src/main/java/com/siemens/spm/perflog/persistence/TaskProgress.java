package com.siemens.spm.perflog.persistence;

import java.sql.Timestamp;
import java.time.LocalDateTime;

import com.siemens.spm.common.constant.AgencyConstants;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;

import com.siemens.spm.perflog.domaintype.TaskStatus;
import com.siemens.spm.perflog.domaintype.TaskType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@Table(name = TaskProgress.TABLE_NAME, schema = AgencyConstants.AGENCY_SCHEMA_PLACEHOLDER)
public class TaskProgress {
    public static final String TABLE_NAME = "task_progress";

    public static final class ColumnName {
        private ColumnName() {
        }

        public static final String ID = "id";
        public static final String TYPE = "type";
        public static final String USER_ID = "user_id";
        public static final String AGENCY_ID = "agency_id";
        public static final String INT_UUID = "int_uuid";
        public static final String INT_NAME = "int_name";
        public static final String REPORT_NAME = "report_name";
        public static final String FROM_TIME = "from_time";
        public static final String TO_TIME = "to_time";
        public static final String STATUS = "status";
        public static final String PROGRESS = "progress";
        public static final String IS_DELETED = "is_deleted";
        public static final String ACTION = "action";
        public static final String CREATED_AT = "created_at";
        public static final String LAST_MODIFIED_AT = "last_modified_at";
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = ColumnName.ID)
    private Long id;

    @NotNull
    @Column(name = ColumnName.TYPE)
    @Enumerated(EnumType.STRING)
    private TaskType type;

    @NotNull
    @Column(name = ColumnName.USER_ID)
    private Long userId;

    @NotNull
    @Column(name = ColumnName.AGENCY_ID)
    private Integer agencyId;

    @Column(name = ColumnName.INT_UUID)
    private String intUUID;

    @Column(name = ColumnName.INT_NAME)
    private String intName;

    @Column(name = ColumnName.REPORT_NAME)
    private String reportName;

    @Column(name = ColumnName.FROM_TIME)
    private LocalDateTime fromTime;

    @Column(name = ColumnName.TO_TIME)
    private LocalDateTime toTime;

    @Column(name = ColumnName.ACTION, columnDefinition = "TEXT")
    private String action;

    @NotNull
    @Column(name = ColumnName.STATUS)
    @Enumerated(EnumType.STRING)
    private TaskStatus status;

    @Column(name = ColumnName.PROGRESS)
    private int progress;

    @Column(name = ColumnName.IS_DELETED)
    private boolean deleted;

    @NotNull
    @Column(name = ColumnName.CREATED_AT)
    private Timestamp createdAt;

    @Column(name = ColumnName.LAST_MODIFIED_AT)
    protected Timestamp lastModifiedAt;

    /**
     * Only delete tasks with status is equal COMPLETED, ERROR or CANCELLED
     *
     * @return {@code true} if task can be deleted, {@code false} otherwise
     */
    public boolean checkDelete() {
        return TaskStatus.COMPLETED.equals(status) || TaskStatus.ERROR.equals(status)
                || TaskStatus.CANCELLING.equals(status)
                || TaskStatus.CANCELLED.equals(status);
    }

    /**
     * Only cancel tasks with status is equal PROCESSING
     *
     * @return {@code true} if task can be cancelled, {@code false} otherwise
     */
    public boolean checkCancel() {
        if (TaskType.SUMMARY_REPORT.equals(type)) {
            return false;
        }

        return TaskStatus.QUEUED.equals(status) || TaskStatus.PROCESSING.equals(status);
    }

}
