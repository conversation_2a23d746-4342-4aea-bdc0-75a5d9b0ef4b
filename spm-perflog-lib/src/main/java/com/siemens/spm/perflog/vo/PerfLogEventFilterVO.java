package com.siemens.spm.perflog.vo;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PerfLogEventFilterVO implements Serializable {

    private static final long serialVersionUID = 6359707603583036871L;

    /**
     * Event code to filter exported events. If this field is null or empty then should export all event codes
     */
    @JsonProperty("event_code")
    private int eventCode;

    /**
     * List of event params to filter exported events. If this field is null or empty then should export all event
     * params
     */
    @JsonProperty("params")
    private List<Integer> eventParams;

}
