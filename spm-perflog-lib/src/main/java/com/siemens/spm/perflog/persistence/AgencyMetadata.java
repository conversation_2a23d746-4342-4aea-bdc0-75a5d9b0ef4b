/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AgencyMetadata.java
 * Project     : SPM Platform
 */
package com.siemens.spm.perflog.persistence;

import java.sql.Timestamp;

import com.siemens.spm.common.constant.AgencyConstants;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "agency_metadata", schema = AgencyConstants.AGENCY_SCHEMA_PLACEHOLDER)
public class AgencyMetadata {

    /**
     * Agency UUID
     */
    @Id
    @Column(name = "id", nullable = false)
    private Integer id;

    @Column(name = "perflog_updated_time")
    private Timestamp perfLogUpdatedTime;

}
