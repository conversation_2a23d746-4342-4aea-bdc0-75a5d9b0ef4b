/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : PerfLogChunkID.java
 * Project     : SPM Platform
 */
package com.siemens.spm.perflog.persistence;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PerfLogChunkID implements Serializable {

    private static final long serialVersionUID = 5504830538137045426L;

    private String intUUID;

    private String fromTime;

    private String toTime;

}
