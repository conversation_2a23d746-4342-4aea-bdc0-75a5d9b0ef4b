/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : SequenceInfoVO.java
 * Project     : SPM Platform
 */
package com.siemens.spm.perflog.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.perflog.domaintype.GapType;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Builder(toBuilder = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PerfLogGapVO implements Serializable {

    private static final long serialVersionUID = -5564237463272875337L;

    @NotNull
    @JsonProperty("from_time")
    private LocalDateTime fromTime;

    @NotNull
    @JsonProperty("to_time")
    private LocalDateTime toTime;

    @JsonProperty("type")
    private GapType gapType;
}
