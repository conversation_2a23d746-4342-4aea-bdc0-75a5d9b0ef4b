package com.siemens.spm.perflog.domaintype;

import com.google.common.collect.Maps;
import com.siemens.spm.common.constant.CommonConstants;
import com.siemens.spm.common.shared.resource.TextKey;
import com.siemens.spm.common.util.BeanFinder;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.util.StringUtils;

import java.util.EnumSet;
import java.util.Map;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON>
 */
@Getter
@AllArgsConstructor
public enum DetectorType {
    ADVANCE("det_advance", TextKey.DETECTOR_ADVANCE, "ADVANCE"),
    STOP_BAR("det_stopbar", TextKey.DETECTOR_STOPBAR, "STOP_BAR"),
    RED_LIGHT_VIOLATION("det_red_light_violation", TextKey.DETECTOR_RED_LIGHT_VIOLATION, "RED_LIGHT_VIOLATION"),
    PEDESTRIAN("det_pedestrian",TextKey.DETECTOR_PEDESTRIAN,"PEDESTRIAN")
    ;

    private static final Map<String, DetectorType> id2DetectorTypeMap = Maps
            .uniqueIndex(EnumSet.allOf(DetectorType.class), DetectorType::getId);
    private final String id;
    private final String labelKey;
    private final String value;

    public String getTranslatedName() {
        if (!StringUtils.hasText(labelKey)) {
            return CommonConstants.NOT_AVAILABLE;
        }
        return BeanFinder.getDefaultMessageService().getMessage(labelKey);
    }

    public static DetectorType getById(String id) {
        return id2DetectorTypeMap.get(id);
    }
}
