/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : PerfLogGapSpecifications.java
 * Project     : SPM Platform
 */
package com.siemens.spm.perflog.repository.specifications;

import java.time.LocalDateTime;

import jakarta.persistence.criteria.Predicate;

import org.springframework.data.jpa.domain.Specification;

import com.siemens.spm.perflog.persistence.PerfLogGap;

public class PerfLogGapSpecifications {

    private PerfLogGapSpecifications() {
        // Disable public constructor, may change to public if need create instance
    }

    public static Specification<PerfLogGap> getPerfLogGaps(String intUUID,
                                                           LocalDateTime fromTime,
                                                           LocalDateTime toTime) {
        return (root, query, criteriaBuilder) -> {
            Predicate intUUIDPred = criteriaBuilder.equal(root.get("intUUID"), intUUID);
            Predicate fromTimePred = criteriaBuilder.greaterThanOrEqualTo(root.get("fromTime"), fromTime);
            Predicate toTimePred = criteriaBuilder.lessThanOrEqualTo(root.get("toTime"), toTime);
            return criteriaBuilder.and(intUUIDPred, fromTimePred, toTimePred);
        };
    }

}
