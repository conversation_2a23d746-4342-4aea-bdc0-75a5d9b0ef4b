package com.siemens.spm.perflog.repository.filterdata;

import java.sql.Timestamp;
import java.util.List;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class PhaseStatFilterDataVO {

    private List<String> intUUIDList;

    private Timestamp fromTimeUTC;

    private Timestamp toTimeUTC;

    private List<Integer> phases;

    private Integer minPhase;

    private Integer maxPhase;

}
