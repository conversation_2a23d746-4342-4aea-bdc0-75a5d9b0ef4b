/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : IntersectionConfig.java
 * Project     : SPM Platform
 */
package com.siemens.spm.perflog.persistence;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.siemens.spm.common.constant.AgencyConstants;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.IdClass;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.siemens.spm.common.util.BeanFinder;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = IntersectionConfig.TABLE_NAME, schema = AgencyConstants.AGENCY_SCHEMA_PLACEHOLDER)
@IdClass(IntersectionConfigID.class)
public class IntersectionConfig {

    public static final String TABLE_NAME = "int_config";

    public static final class ColumnName {

        private ColumnName() {
        }

        public static final String UUID = "uuid";
        public static final String FROM_TIME_UTC = "from_time_utc";
        public static final String TO_TIME_UTC = "to_time_utc";
        public static final String VALID_FROM_TIME = "valid_from_time";
        public static final String VALID_TO_TIME = "valid_to_time";
        public static final String INT_UUID = "int_uuid";

        public static final String CONFIG_LOB = "config_lob";
        public static final String SPLIT_COORD_PHASE = "split_coord_phase";
        public static final String DET_CALL_PHASES = "det_call_phases";
        public static final String PHASE_INITIAL_STATE = "phase_initial_state";

    }

    @Id
    @Column(name = ColumnName.UUID, length = 40, nullable = false)
    private String uuid;

    @Id
    @Column(name = ColumnName.FROM_TIME_UTC, nullable = false)
    private Timestamp fromTimeUtc;

    @Column(name = ColumnName.TO_TIME_UTC)
    private Timestamp toTimeUtc;

    /**
     * Local time beginning interval in which the configuration is valid/effective
     */
    @Column(name = ColumnName.VALID_FROM_TIME, nullable = false)
    private LocalDateTime validFromTime;

    /**
     * Local time ending interval in which the configuration is valid/effective
     */
    @Column(name = ColumnName.VALID_TO_TIME)
    private LocalDateTime validToTime; // Can be updated

    @Column(name = ColumnName.INT_UUID, length = 40, nullable = false)
    private String intUUID;

    /**
     * JSON of IntersectionConfigVO
     */
    @Column(name = ColumnName.CONFIG_LOB, columnDefinition = "TEXT")
    private String intConfigVOLob;

    /**
     * JSON of splitCoordPhase. Lists whether a phase is coordinated (denoted by '1'). The index of the outer
     * array is the pattern number, and index of the nested array is coordinated flag for each phase.
     */
    @Column(name = ColumnName.SPLIT_COORD_PHASE, columnDefinition = "TEXT")
    private String splitCoordPhaseLob;

    /**
     * Should only be accessed by getSplitCoordPhase
     */
    @Transient
    private int[][] splitCoordPhase;

    /**
     * JSON of detCallPhases. Lists the phase number that will be called by each detector. The index of the outer array
     * is the detector number, the numbers stored in the nested array are the phases that detector will call.
     */
    @Column(name = ColumnName.DET_CALL_PHASES, columnDefinition = "TEXT")
    private String detCallPhasesLob;

    /**
     * Should only be accessed by getDetCallPhases
     */
    @Transient
    private int[][] detCallPhases;

    /**
     * JSON of phaseInitialState. Defines the initial state of each phase. 0 - None (not enabled) 1 - Inactive & enabled
     * 2 - Red Clr & enabled 3 - Yel Chg & enabled 4 - Green-Walk & enabled 5 - Dark & enabled 6 - Green-D Walk &
     * enabled 255 - Not enabled
     */
    @Column(name = ColumnName.PHASE_INITIAL_STATE)
    private String phaseInitialStateLob;

    /**
     * Should only be accessed by getPhaseInitialState
     */
    @Transient
    private int[] phaseInitialState;

    public int[][] getSplitCoordPhase() {
        if (splitCoordPhase == null) {
            try {
                splitCoordPhase = BeanFinder.getDefaultObjectMapper().readValue(this.splitCoordPhaseLob, int[][].class);
            } catch (JsonProcessingException e) {
                log.error("Cannot deserialize splitCoordPhase:\n" + this.splitCoordPhaseLob);
            }
        }
        return splitCoordPhase;
    }

    public int[][] getDetCallPhases() {
        if (detCallPhases == null) {
            try {
                detCallPhases = BeanFinder.getDefaultObjectMapper().readValue(this.detCallPhasesLob, int[][].class);
            } catch (JsonProcessingException e) {
                log.error("Cannot deserialize detCallPhases:\n" + this.detCallPhasesLob);
            }
        }
        return detCallPhases;
    }

    public int[] getPhaseInitialState() {
        if (phaseInitialState == null) {
            try {
                phaseInitialState = BeanFinder.getDefaultObjectMapper()
                        .readValue(this.phaseInitialStateLob, int[].class);
            } catch (JsonProcessingException e) {
                log.error("Cannot deserialize phaseInitialState:\n" + this.phaseInitialStateLob);
            }
        }
        return phaseInitialState;
    }

    /**
     * Check if a phase is a coordinated phase in this configuration. The check considers all configured patterns
     * regardless of current active pattern.
     *
     * @param phase
     * @return
     */
    public boolean isCoordinatedPhase(int phase) {
        if (phase < PhaseStatID.MIN_PHASE || phase > PhaseStatID.MAX_PHASE) {
            throw new IllegalArgumentException("Invalid phase number! Phase number must be in range"
                    + String.format("(%d, %d)", PhaseStatID.MIN_PHASE, PhaseStatID.MAX_PHASE));
        }

        int[][] scp = getSplitCoordPhase();

        if (scp == null) {
            return false;
        }

        for (int[] ints : scp) {
            if (ints == null || phase > ints.length) {
                log.warn("Strange scp: " + this.splitCoordPhaseLob);
                continue;
            }
            // phaseIdx = phaseNum - 1
            // splitCoordPhase[patIdx][phaseIdx]: 0 = Non-coordinated; 1 = Coordinated
            if (ints[phase - 1] != 0) {
                return true;
            }
        }

        return false;
    }

    public List<Integer> getCoordinatedPhases() {
        int[][] scp = getSplitCoordPhase();
        if (scp == null) {
            return Collections.emptyList();
        }

        Set<Integer> coordinatedPhases = new HashSet<>();
        for (int[] phaseEnableValues : scp) {
            if (phaseEnableValues == null) {
                continue;
            }
            for (int phase = 0; phase < phaseEnableValues.length; phase++) {
                if (phaseEnableValues[phase] != 0) {
                    coordinatedPhases.add(phase + 1);
                }
            }
        }

        return new ArrayList<>(coordinatedPhases);
    }

}
