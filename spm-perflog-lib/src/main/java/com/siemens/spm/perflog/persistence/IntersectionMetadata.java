/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : IntersectionMetadata.java
 * Project     : SPM Platform
 */
package com.siemens.spm.perflog.persistence;

import java.sql.Timestamp;

import com.siemens.spm.common.constant.AgencyConstants;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "intersection_metadata", schema = AgencyConstants.AGENCY_SCHEMA_PLACEHOLDER)
public class IntersectionMetadata {

    public static final class ColumnName {

        private ColumnName() {
        }

        public static final String ID = "uuid";
        public static final String INTERSECTION_NAME = "name";
        public static final String PERFLOG_UPDATED_TIME = "perflog_updated_time";
        public static final String TSP_UPDATED_TIME = "tsp_updated_time";
    }

    /**
     * Intersection UUID
     */
    @Id
    @Column(name = ColumnName.ID, length = 40, nullable = false)
    private String uuid;

    /**
     * Intersection name
     */
    @Column(name = ColumnName.INTERSECTION_NAME)
    private String name;

    @Column(name = ColumnName.PERFLOG_UPDATED_TIME)
    private Timestamp perfLogUpdatedTime;

    @Column(name = ColumnName.TSP_UPDATED_TIME)
    private Timestamp tspUpdatedTime;

}
