package com.siemens.spm.perflog.persistence;

import java.time.LocalDateTime;

import com.siemens.spm.common.constant.AgencyConstants;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.IdClass;
import jakarta.persistence.Table;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Entity
@Table(name = "phase_event", schema = AgencyConstants.AGENCY_SCHEMA_PLACEHOLDER)
@IdClass(PhaseEventID.class)
public class PhaseEvent {

    @Id
    @Column(name = "int_uuid")
    private String intUUID;

    @Id
    @Column(name = "from_time")
    private LocalDateTime fromTime;

    @Id
    @Column(name = "to_time")
    private LocalDateTime toTime;

    @Id
    @Column(name = "phase")
    private Integer phase;

    /** Number of detector hit event (82). Unit in raw count */
    @Column(name = "det_hit")
    private Integer detHit;

}
