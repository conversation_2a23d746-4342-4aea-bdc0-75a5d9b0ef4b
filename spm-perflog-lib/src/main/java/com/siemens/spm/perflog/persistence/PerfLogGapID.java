/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : PerfLogGapID.java
 * Project     : SPM Platform
 */
package com.siemens.spm.perflog.persistence;

import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PerfLogGapID implements Serializable {

    private static final long serialVersionUID = -3331000759902056277L;

    private String intUUID;

    private LocalDateTime fromTime;

}
