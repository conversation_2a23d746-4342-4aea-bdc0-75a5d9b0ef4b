package com.siemens.spm.perflog.utils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import javax.annotation.Nullable;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.siemens.spm.common.util.BeanFinder;
import com.siemens.spm.datahub.api.vo.DataHubApproachVO;
import com.siemens.spm.datahub.api.vo.DataHubDetectorVO;
import com.siemens.spm.datahub.api.vo.DataHubLaneVO;
import com.siemens.spm.datahub.api.vo.DataHubPhaseVO;
import com.siemens.spm.perflog.persistence.IntersectionConfig;
import com.siemens.spm.perflog.vo.ApproachVO;
import com.siemens.spm.perflog.vo.DetectorVO;
import com.siemens.spm.perflog.vo.IntersectionConfigVO;
import com.siemens.spm.perflog.vo.LaneVO;
import com.siemens.spm.perflog.vo.PhaseVO;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public final class IntersectionConfigConverter {

    private IntersectionConfigConverter() {
    }

    /**
     * Convert list of {@link DataHubApproachVO} to list of {@link ApproachVO}
     *
     * @param dataHubApproaches {@code List<DataHubApproachVO>}
     * @return list of {@link ApproachVO} object
     */
    public static List<ApproachVO> convertApproaches(@Nullable List<DataHubApproachVO> dataHubApproaches) {
        if (dataHubApproaches == null) {
            return Collections.emptyList();
        }
        List<ApproachVO> approaches = new ArrayList<>();
        for (DataHubApproachVO dataHubApproach : dataHubApproaches) {
            ApproachVO approach = ApproachVO.builder()
                    .approachSpeed(dataHubApproach.getApproachSpeed())
                    .direction(dataHubApproach.getDirection())
                    .upstreamEntityId(dataHubApproach.getUpstreamEntityId())
                    .upstreamDistance(dataHubApproach.getUpstreamDistance())
                    .downstreamEntityId(dataHubApproach.getDownstreamEntityId())
                    .downstreamDistance(dataHubApproach.getDownstreamDistance())
                    .phases(convertPhases(dataHubApproach.getPhases()))
                    .build();
            approaches.add(approach);
        }

        return approaches;
    }

    private static List<PhaseVO> convertPhases(@Nullable List<DataHubPhaseVO> dataHubPhases) {
        if (dataHubPhases == null) {
            return Collections.emptyList();
        }
        List<PhaseVO> phases = new ArrayList<>();
        for (DataHubPhaseVO dataHubPhase : dataHubPhases) {
            PhaseVO phase = PhaseVO.builder()
                    .phaseNumber(dataHubPhase.getPhaseNumber())
                    .type(dataHubPhase.getType())
                    .lanes(convertLanes(dataHubPhase.getLanes()))
                    .build();
            phases.add(phase);
        }
        return phases;
    }

    private static List<LaneVO> convertLanes(@Nullable List<DataHubLaneVO> dataHubLanes) {
        if (dataHubLanes == null) {
            return Collections.emptyList();
        }
        List<LaneVO> lanes = new ArrayList<>();
        for (DataHubLaneVO dataHubLane : dataHubLanes) {
            List<DataHubDetectorVO> dataHubDetectors = dataHubLane.getDetectors();
            List<DetectorVO> detectors = new ArrayList<>();
            if (dataHubDetectors != null) {
                for (DataHubDetectorVO dataHubDetector : dataHubDetectors) {
                    DetectorVO detector = DetectorVO.builder()
                            .detectorNumber(dataHubDetector.getDetectorNumber())
                            .type(DetectorVO.DetectorType.resolve(dataHubDetector.getType()))
                            .distance(dataHubDetector.getDistance())
                            .build();
                    detectors.add(detector);
                }
            }

            lanes.add(new LaneVO(dataHubLane.getMovement(), detectors));
        }
        return lanes;
    }

    public static IntersectionConfigVO toVO(IntersectionConfig config) {
        if (config == null) {
            return null;
        }

        if (config.getIntConfigVOLob() != null) {
            try {
                return BeanFinder.getDefaultObjectMapper()
                        .readValue(config.getIntConfigVOLob(), IntersectionConfigVO.class);
            } catch (JsonProcessingException e) {
                log.error("Failed to convert IntersectionConfig to IntersectionConfigVO", e);
            }
        }

        return null;
    }

}
