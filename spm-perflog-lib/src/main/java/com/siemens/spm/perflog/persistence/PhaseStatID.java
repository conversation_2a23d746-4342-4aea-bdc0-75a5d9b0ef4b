/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : PhaseStatID.java
 * Project     : SPM Platform
 */
package com.siemens.spm.perflog.persistence;

import java.io.Serializable;
import java.sql.Timestamp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PhaseStatID implements Serializable {

    private static final long serialVersionUID = 113153639755663474L;

    public static final int MIN_PHASE = 1;
    public static final int MAX_PHASE = 16;

    private String intUUID;
    private Timestamp fromTimeUTC;
    private Timestamp toTimeUTC;
    private int phase;

}
