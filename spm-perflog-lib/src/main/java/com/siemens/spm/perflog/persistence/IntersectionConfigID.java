/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : ConfigEventID.java
 * Project     : SPM Platform
 */
package com.siemens.spm.perflog.persistence;

import java.io.Serializable;
import java.sql.Timestamp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class IntersectionConfigID implements Serializable {

    private static final long serialVersionUID = 7199113217507714876L;

    private String uuid;
    private Timestamp fromTimeUtc;

}
