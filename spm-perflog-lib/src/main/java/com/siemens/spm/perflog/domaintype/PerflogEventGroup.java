package com.siemens.spm.perflog.domaintype;

import com.siemens.spm.common.constant.CommonConstants;
import com.siemens.spm.common.util.BeanFinder;
import lombok.Getter;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 */
@Getter
public enum PerflogEventGroup {

    /**
     * Groups perflog event
     */
    ACTIVE_PHASE(0, 20, "active_phase"),
    ACTIVE_PEDESTRIAN(21, 30, "active_pedestrian"),
    BARRIER_RING(31, 40, "barrier_ring"),
    PHASE_CONTROL(41, 60, "phase_control"),
    OVERLAP(61, 80, "overlap"),
    DETECTOR(81, 100, "detector_group"),
    PREEMPTION(101, 130, "preemption"),
    COORDINATION(131, 170, "coordination_group"),
    CABINET_SYSTEM(171, 255, "cabinet_system");

    private final int startEvent;
    private final int endEvent;
    private final String keyName;

    PerflogEventGroup(int startEvent, int endEvent, String keyName) {
        this.startEvent = startEvent;
        this.endEvent = endEvent;
        this.keyName = keyName;
    }

    public String getTranslatedName() {
        if (!StringUtils.hasText(keyName)) {
            return CommonConstants.NOT_AVAILABLE;
        }

        return BeanFinder.getDefaultMessageService().getMessage(keyName);
    }

}
