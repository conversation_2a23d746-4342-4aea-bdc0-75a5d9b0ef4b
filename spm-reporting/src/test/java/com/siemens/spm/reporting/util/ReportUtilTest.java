package com.siemens.spm.reporting.util;

import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class ReportUtilTest {

    @Test()
    void test_getColumnHeadersByFields() {
        Assertions.assertDoesNotThrow(() -> {
            final String name1 = "a1";
            final String name2 = "a2";
            final List<String> columnsTest = List.of(name1, name2);
            Map<String, String> result = ReportUtil.getColumnHeaders(columnsTest, null, null);
            Assertions.assertTrue(result.containsKey(name1));
            Assertions.assertTrue(result.containsKey(name2));
            Assertions.assertEquals(name1, result.get(name1));
            Assertions.assertEquals(name2, result.get(name2));

            Map<String, String> result1 = ReportUtil.getColumnHeaders(null, null, null);
            Assertions.assertTrue(result1.isEmpty());

            Map<String, String> result2 = ReportUtil.getColumnHeaders(List.of(), null, null);
            Assertions.assertTrue(result2.isEmpty());
        });
    }

}
