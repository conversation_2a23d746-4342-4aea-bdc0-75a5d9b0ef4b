package com.siemens.spm.reporting.util;

import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import org.springframework.context.MessageSource;
import org.springframework.context.NoSuchMessageException;

import com.siemens.spm.common.util.ListUtil;

public final class ReportUtil {

    private ReportUtil() {
    }

    /**
     * @param columns       the key name of columns that are used to translate
     * @param messageSource
     * @param locale
     * @return
     */
    public static Map<String, String> getColumnHeaders(List<String> columns,
                                                       MessageSource messageSource,
                                                       Locale locale) {
        Map<String, String> columnHeaders = new HashMap<>();

        if (ListUtil.hasNoItem(columns)) {
            return Map.of();
        }

        if (locale == null) {
            locale = Locale.getDefault();
        }

        for (String column : columns) {
            if (messageSource == null) {
                columnHeaders.put(column, column);
                continue;
            }

            String translatedText;
            try {
                translatedText = messageSource.getMessage(column, null, locale);
            } catch (NoSuchMessageException e) {
                translatedText = column;
            }

            columnHeaders.put(column, translatedText);
        }

        return columnHeaders;
    }
}
