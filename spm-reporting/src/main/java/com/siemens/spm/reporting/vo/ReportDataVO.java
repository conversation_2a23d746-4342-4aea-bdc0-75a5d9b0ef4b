package com.siemens.spm.reporting.vo;

import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_NULL)
public class ReportDataVO {

    private List<?> headerInformation;

    /**
     * For jasper report, it should be List<T extends Object>. For apache csv, it
     * should be List<Object[]>
     */
    private List<?> rowData;

    private String reportName;

    private String reportTitle;

    private String reportDescription;

    private Map<String, String> columnHeaders;

    private String jasperFile;
}
