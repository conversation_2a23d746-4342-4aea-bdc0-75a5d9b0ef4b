package com.siemens.spm.reporting.service;

import com.siemens.spm.reporting.domaintype.ReportType;
import com.siemens.spm.reporting.exception.ReportingException;
import com.siemens.spm.reporting.vo.ReportDataVO;

/**
 * 
 * <AUTHOR>
 *
 */
public interface ReportService {

    /**
     * 
     * @param data
     * @param type
     * @return file name
     * @throws ReportingException
     */
    String export(ReportDataVO data, ReportType type) throws ReportingException;
}
