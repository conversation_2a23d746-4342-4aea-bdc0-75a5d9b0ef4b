package com.siemens.spm.spmstudiosdk.service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.springframework.data.util.Pair;

import com.siemens.spm.spmstudiosdk.dto.StudioUserAssignmentDto;
import com.siemens.spm.spmstudiosdk.dto.StudioUserDto;
import com.siemens.spm.spmstudiosdk.dto.StudioUserSearchRequestDto;
import com.siemens.spm.spmstudiosdk.exception.StudioException;

public interface StudioUserService {

    Optional<StudioUserDto> findById(Integer id) throws StudioException;

    Optional<StudioUserDto> findByEmail(String email) throws StudioException;

    Optional<StudioUserAssignmentDto> findUserAssignmentById(Long id) throws StudioException;

    Optional<StudioUserAssignmentDto> findUserAssignmentByEmail(String email) throws StudioException;

    List<StudioUserDto> findAllByAgencyId(Integer agencyId) throws StudioException;

    List<StudioUserAssignmentDto> findAllUserAssignmentByAgencyId(Integer agencyId) throws StudioException;

    List<StudioUserDto> findAll() throws StudioException;

    List<StudioUserAssignmentDto> findAllUserAssignment() throws StudioException;

    Map<Integer, StudioUserDto> getMapUsersByIds(Collection<Integer> userIds) throws StudioException;

    Pair<List<StudioUserDto>, Long> searchUsers(StudioUserSearchRequestDto requestDto) throws StudioException;

}
