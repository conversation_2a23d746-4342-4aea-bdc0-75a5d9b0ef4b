package com.siemens.spm.perflogcrawler.api.tspevent.vo.response;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.common.shared.vo.AbstractResultObject;
import com.siemens.spm.perflogcrawler.api.tspevent.vo.TspEventVO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.http.HttpStatus;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TspEventsResultObject
        extends AbstractResultObject<TspEventsResultObject.ResponseData, TspEventsResultObject.StatusCode> {

    private static final long serialVersionUID = -8691022227188282093L;

    private ResponseData data;

    private StatusCode statusCode;

    @Override
    public String getMessage() {
        return statusCode.getMessage();
    }

    @Override
    protected StatusCode getErrorStatusValue() {
        return StatusCode.ERROR;
    }

    @Override
    public String getErrorFieldName() {
        return statusCode.getErrorField();
    }

    @Override
    public StatusCode getStatusCode() {
        return statusCode;
    }

    @Override
    public StatusCode getSuccessfulStatusValue() {
        return StatusCode.SUCCESS;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ResponseData implements Serializable {

        private static final long serialVersionUID = -6369509576315928129L;

        @JsonProperty("tsp_events")
        private List<TspEventVO> tspEvents;

        @JsonProperty("total_count")
        private long totalCount;

    }

    @Override
    public HttpStatus getHttpStatus() {
        return statusCode.getHttpStatus();
    }

    @Override
    public ResponseData getData() {
        return data;
    }

    @Getter
    @AllArgsConstructor
    public enum StatusCode {
        SUCCESS("success", HttpStatus.OK),

        INVALID_SORT_COLUMN("sort", "sort_column_invalid", HttpStatus.BAD_REQUEST),
        INVALID_SORT_ORDER("sort", "sort_order_invalid", HttpStatus.BAD_REQUEST),

        COULD_NOT_GET_DATA("could_not_get_data", HttpStatus.BAD_REQUEST),
        ERROR("unknown_exception", HttpStatus.INTERNAL_SERVER_ERROR);

        private final String errorField;
        private final String message;
        private final HttpStatus httpStatus;

        StatusCode(String message, HttpStatus httpStatus) {
            this(null, message, httpStatus);
        }
    }

}
