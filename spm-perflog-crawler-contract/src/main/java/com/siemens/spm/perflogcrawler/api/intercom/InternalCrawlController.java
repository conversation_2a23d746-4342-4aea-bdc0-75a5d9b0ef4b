/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : InternalCrawlController.java
 * Project     : SPM Platform
 */

package com.siemens.spm.perflogcrawler.api.intercom;

import com.siemens.spm.common.intercom.InterComController;
import com.siemens.spm.common.shared.vo.SimpleResultObject;
import com.siemens.spm.common.util.RestUtils;
import com.siemens.spm.perflog.vo.PerfLogBundleVO;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Internal controller for testing PerfLog crawling
 */
@RequestMapping(InternalCrawlController.API_ROOT)
public interface InternalCrawlController extends InterComController {

    String API_ROOT = INTERNAL_API + "/v1";
    String PERFLOG_CRAWL = "/perflog/crawl";
    String PERFLOG_PHASE_STAT = "/perflog/phase_stat";
    String PUT_PERFLOG_CRAWL_URL =
        PERFLOG_CRAWL + "?agency_uuid={0}&int_uuid={1}&from_time={3}&to_time={4}";

    /**
     * Get PerfLog data of a specific Intersection for a specific interval. Internal use and testing only.
     *
     * @param agencyId
     * @param intUUID
     * @param fromTime
     * @param toTime
     * @return ResponseEntity<PerfLogResultObject>
     */
    @PutMapping(PERFLOG_CRAWL)
    ResponseEntity<SimpleResultObject> crawl(@RequestParam(value = "agencyId") Integer agencyId,
        @RequestParam(value = "int_uuid") String intUUID,
        @RequestParam(value = "from_time") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime fromTime,
        @RequestParam(
            value = "to_time",
            required = true) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime toTime);

    static ResponseEntity<SimpleResultObject> invokeCrawl(String endpoint, Integer agencyId, String intUUID,
        LocalDateTime fromTime, LocalDateTime toTime) {
        String fromTimeString = fromTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        String toTimeString = toTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        String url =
            endpoint + MessageFormat.format(PUT_PERFLOG_CRAWL_URL, agencyId, intUUID, fromTimeString, toTimeString);

        return RestUtils.getWithAgencyHeader(url, String.valueOf(agencyId), SimpleResultObject.class);
    }

    /**
     * Get PerfLog data of a specific Intersection and save to phase_stat. Internal use and testing only.
     *
     * @param agencyId
     * @param intUUID
     * @param fromTime
     * @param toTime
     * @return ResponseEntity<SimpleResultObject>
     */
    @PostMapping(PERFLOG_PHASE_STAT)
    ResponseEntity<SimpleResultObject> storePhaseStatInternal(
        @Parameter(description = "Agency Id") @RequestParam("agency_id") Integer agencyId,

        @Parameter(description = "Intersection UUID") @RequestParam(
            value = "int_uuid",
            required = false) String intUUID,

        @Parameter(description = "Beginning of Analysis time range") @RequestParam(value = "from_time") @DateTimeFormat(
            iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime fromTime,

        @Parameter(description = "Ending of Analysis time range") @RequestParam(value = "to_time") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime toTime,

        @RequestBody(required = false) PerfLogBundleVO perfLogBundleVO);

}
