package com.siemens.spm.perflogcrawler.api.vo;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.common.message.MessageService;
import com.siemens.spm.common.shared.vo.TranslatableVO;
import com.siemens.spm.perflog.domaintype.DirectionType;
import lombok.Getter;
import lombok.Setter;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class DirectionVO implements Serializable, TranslatableVO {

    private static final long serialVersionUID = -6793294571452403024L;

    @Getter
    @Setter
    @JsonProperty("id")
    private String id;

    @Getter
    @Setter
    @JsonProperty("name")
    private String name;

    /**
     * Key for translating name
     */
    @Getter
    @JsonIgnore
    private String nameKey;

    private DirectionVO(String id, String nameKey) {
        super();
        this.id = id;
        this.nameKey = nameKey;
    }

    public static DirectionVO of(DirectionType directionType) {
        return new DirectionVO(directionType.getId(), directionType.getName());
    }

    @Override
    public void acceptTranslator(MessageService translator) {
        if (translator == null) {
            throw new IllegalArgumentException();
        }
        name = translator.getMessage(nameKey);
    }
}
