/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : PerfLogTaskController.java
 * Project     : SPM Platform
 */
package com.siemens.spm.perflogcrawler.api.intercom;

import java.util.concurrent.TimeUnit;

import com.siemens.spm.perflogcrawler.api.vo.PerfLogCrawlTask;
import com.siemens.spm.perflogcrawler.api.vo.PerfLogExportTask;
import com.siemens.spm.perflogcrawler.api.vo.PerfLogTask;
import org.springframework.data.redis.core.RedisTemplate;

public interface PerfLogTaskController {

    /**
     * Key for Redis queue
     */
    public static final String PERFLOG_TASK_KEY = "perflog-task";

    public static final String PERFLOG_EXPORT_TASK_KEY = "perflog-export-task";
    
    public static final String PERFLOG_CRAWL_TASK_KEY = "perflog-crawl-task";
    
    public static void invokeEnqueueExportTask(RedisTemplate<String, PerfLogExportTask> redisTemplate, PerfLogExportTask task) {
        redisTemplate.opsForList().rightPush(PERFLOG_EXPORT_TASK_KEY, task);
    }
    
    /**
    * Blocks connection until element available or DEQUEUE_TIMEOUT_SECONDS reached.
    * 
    * @param redisTemplate
    * @param timeoutSeconds
    * @return
    */
    public static PerfLogExportTask invokeDequeueExportTask(RedisTemplate<String, PerfLogExportTask> redisTemplate,
                                            long timeoutSeconds) {
        return redisTemplate.opsForList().leftPop(PERFLOG_EXPORT_TASK_KEY, timeoutSeconds, TimeUnit.SECONDS);
    }
    
    public static void invokeEnqueueCrawlTask(RedisTemplate<String, PerfLogCrawlTask> redisTemplate, PerfLogCrawlTask task) {
        redisTemplate.opsForList().rightPush(PERFLOG_CRAWL_TASK_KEY, task);
    }
    
    /**
    * Blocks connection until element available or DEQUEUE_TIMEOUT_SECONDS reached.
    * 
    * @param redisTemplate
    * @param timeoutSeconds
    * @return
    */
    public static PerfLogCrawlTask invokeDequeueCrawlTask(RedisTemplate<String, PerfLogCrawlTask> redisTemplate,
                                            long timeoutSeconds) {
        return redisTemplate.opsForList().leftPop(PERFLOG_CRAWL_TASK_KEY, timeoutSeconds, TimeUnit.SECONDS);
    }
    
    public static void invokeEnqueue(RedisTemplate<String, PerfLogTask> redisTemplate, PerfLogTask task) {
        redisTemplate.opsForList().rightPush(PERFLOG_TASK_KEY, task);
    }

    /**
     * Blocks connection until element available or DEQUEUE_TIMEOUT_SECONDS reached.
     * 
     * @param redisTemplate
     * @param timeoutSeconds
     * @return
     */
    public static PerfLogTask invokeDequeue(RedisTemplate<String, PerfLogTask> redisTemplate,
                                            long timeoutSeconds) {
        return redisTemplate.opsForList().leftPop(PERFLOG_TASK_KEY, timeoutSeconds, TimeUnit.SECONDS);
    }

}
