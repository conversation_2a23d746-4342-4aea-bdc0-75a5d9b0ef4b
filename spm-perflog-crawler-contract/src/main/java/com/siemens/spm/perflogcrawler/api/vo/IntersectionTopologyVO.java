package com.siemens.spm.perflogcrawler.api.vo;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.perflog.vo.ApproachVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class IntersectionTopologyVO implements Serializable {

    private static final long serialVersionUID = -736204039149001564L;
    @JsonProperty("Approaches")
    private List<ApproachVO> approaches;

}
