package com.siemens.spm.perflogcrawler.api.vo;

import java.util.HashMap;
import java.util.Map;

import org.springframework.util.StringUtils;

public enum PerfLogExportType {
    CSV, PDF;

    private static final Map<String, PerfLogExportType> EXPORT_TYPE_MAP = new HashMap<>();

    static {
        for (PerfLogExportType exportType : PerfLogExportType.values()) {
            EXPORT_TYPE_MAP.put(exportType.name().toLowerCase(), exportType);
        }
    }

    public static PerfLogExportType resolve(String exportType) {
        if (StringUtils.hasText(exportType) && EXPORT_TYPE_MAP.containsKey(exportType.toLowerCase())) {
            return EXPORT_TYPE_MAP.get(exportType.toLowerCase());
        }

        return null;
    }

}
