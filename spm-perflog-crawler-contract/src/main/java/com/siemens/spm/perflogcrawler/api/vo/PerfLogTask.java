/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : PerfLogTask.java
 * Project     : SPM Platform
 */
package com.siemens.spm.perflogcrawler.api.vo;

import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public abstract class PerfLogTask implements Serializable {

    private static final long serialVersionUID = 3340245052466736342L;

    protected Long id;
    protected Integer agencyId;
    protected String intUUID;
    protected LocalDateTime fromTime;
    protected LocalDateTime toTime;

    // Optional
    protected String intName;

    // Milliseconds since UNIX Epoch
    protected Long createdTimestamp;

    protected Long processingStartTime;

}
