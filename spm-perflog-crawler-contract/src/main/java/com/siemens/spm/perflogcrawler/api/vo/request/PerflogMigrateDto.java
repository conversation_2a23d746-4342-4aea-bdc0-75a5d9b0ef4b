package com.siemens.spm.perflogcrawler.api.vo.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR> Admin
 * @version : 1.0
 * @since : 27/3/2025
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PerflogMigrateDto {

    @JsonProperty("intersection_ids")
    private List<String> intersectionIds;

    @JsonProperty("from")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    private LocalDate from;

    @JsonProperty("to")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    private LocalDate to;

}
