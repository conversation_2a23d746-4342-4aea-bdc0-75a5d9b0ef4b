package com.siemens.spm.perflogcrawler.api.vo;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class IntersectionTopologySearchVO implements Serializable {

    private static final long serialVersionUID = -1902612310646141152L;

    @JsonProperty("int_uuid")
    private String intUUID;

    @JsonProperty("int_name")
    private String intName;
}
