package com.siemens.spm.perflogcrawler.api.vo.request;

import java.io.Serializable;
import java.sql.Timestamp;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class IntersectionConfigRequestSearchVO implements Serializable {

    private static final long serialVersionUID = 7944397514191199666L;

    private Integer agencyId;

    private String intUUID;

    private Timestamp fromTime;

    private Timestamp toTime;

    private String[] orderByColumns;

    private Integer page;

    private Integer size;

}
