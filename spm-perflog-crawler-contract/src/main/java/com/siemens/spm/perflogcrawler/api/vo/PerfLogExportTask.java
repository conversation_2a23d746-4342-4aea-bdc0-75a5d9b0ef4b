/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : PerfLogExportTask.java
 * Project     : SPM Platform
 */
package com.siemens.spm.perflogcrawler.api.vo;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.siemens.spm.common.util.ListUtil;
import com.siemens.spm.perflog.vo.PerfLogEventFilterVO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

/**
 * DON'T use {@link lombok.Data}, {@link lombok.EqualsAndHashCode}. <br> Different tasks may have exactly same content.
 * <br> Therefore, use default Equals and HashCode of Java, meaning memory address.
 *
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class PerfLogExportTask extends PerfLogTask {

    private static final long serialVersionUID = 5940190113817195991L;

    private Long userID;

    private PerfLogExportType exportType;

    private List<PerfLogEventFilterVO> eventCodeParams;

    private transient Map<Integer, List<Integer>> eventCodeParamMap;

    /**
     * Get cached event code map to retrieve O(1) read action
     *
     * @return a map contain all event codes and list event params need to filter. Or return {@link Collections#emptyMap()} if have no event
     * code to filter (get all events)
     */
    public Map<Integer, List<Integer>> getEventCodeParamMap() {
        if (eventCodeParamMap == null) {
            if (ListUtil.hasItem(eventCodeParams)) {
                eventCodeParamMap = new HashMap<>();
                for (PerfLogEventFilterVO eventCodeParam : eventCodeParams) {
                    eventCodeParamMap.put(eventCodeParam.getEventCode(), eventCodeParam.getEventParams());
                }
            } else {
                eventCodeParamMap = Collections.emptyMap();
            }
        }

        return eventCodeParamMap;
    }

}
