package com.siemens.spm.perflogcrawler.api.vo.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import org.springframework.http.HttpStatus;

import com.siemens.spm.common.shared.vo.AbstractSimpleResultObject;
import com.siemens.spm.perflogcrawler.api.vo.response.TaskProgressCancelResultObject.StatusCode;

@JsonInclude(Include.NON_NULL)
@JsonIgnoreProperties({ "statusCode", "message", "httpStatus" })
public class TaskProgressCancelResultObject extends AbstractSimpleResultObject<StatusCode> {
    private static final long serialVersionUID = 7472360862843644151L;

    @Setter
    @Getter
    private StatusCode statusCode;

    /**
     * Create default result
     */
    public TaskProgressCancelResultObject() {
    }

    /**
     * Create result object with null data and an error code
     *
     * @param statusCode Indicating error
     */
    public TaskProgressCancelResultObject(StatusCode statusCode) {
        this.statusCode = statusCode;
    }

    @Override
    protected StatusCode getErrorStatusValue() {
        return StatusCode.UNKNOWN_ERROR;
    }

    @Override
    public HttpStatus getHttpStatus() {
        return statusCode.getHttpStatus();
    }

    @Override
    public StatusCode getSuccessfulStatusValue() {
        return StatusCode.SUCCESS;
    }

    @Override
    public String getMessage() {
        return statusCode.getMessage();
    }

    @Override
    public String getErrorFieldName() {
        return this.statusCode.getErrorField();
    }

    @Getter
    @AllArgsConstructor
    public enum StatusCode {
        /** OK */
        SUCCESS("success", HttpStatus.OK),

        /** Request Error */
        TASK_NOT_EXIST("task_not_exist", HttpStatus.BAD_REQUEST),
        TASK_NOT_INVALID("task_invalid", HttpStatus.BAD_REQUEST),
        REQUEST_CANCEL_INVALID("request_cancel_invalid", HttpStatus.BAD_REQUEST),

        /** Internal Server Error */
        UNKNOWN_ERROR("unknown_error", HttpStatus.INTERNAL_SERVER_ERROR);

        private final String errorField;
        private final String message;
        private final HttpStatus httpStatus;

        StatusCode(String message, HttpStatus httpStatus) {
            this(null, message, httpStatus);
        }
    }
}
