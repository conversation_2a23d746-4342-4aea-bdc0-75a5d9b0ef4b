package com.siemens.spm.perflogcrawler.api.tspevent.vo.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TspEventSummarySearchRequest {

    private Integer agencyId;

    private LocalDateTime fromTime;

    private LocalDateTime toTime;

    private String text;

    private String[] orderByColumns;

    private Integer page;

    private Integer size;

}
