package com.siemens.spm.perflogcrawler.api.tspevent.controller;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.siemens.spm.common.api.PublicController;
import com.siemens.spm.common.constant.AgencyConstants;
import com.siemens.spm.perflogcrawler.api.tspevent.vo.response.TspEventSummaryResultObject;
import com.siemens.spm.perflogcrawler.api.tspevent.vo.response.TspEventTypesResultObject;
import com.siemens.spm.perflogcrawler.api.tspevent.vo.response.TspEventsResultObject;
import com.siemens.spm.perflogcrawler.api.tspevent.vo.response.TspIntersectionEventsResultObject;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;

@Tag(name = "tsp-event-history-v2", description = "Tsp Event resources")
@RequestMapping(TspEventHistoryControllerV2.API_ROOT)
public interface TspEventHistoryControllerV2 extends PublicController {

    String API_ROOT = PUBLIC_API + "/v2";
    String TSP_RESOURCE = "/tsp";
    String TSP_EVENTS_RESOURCE = TSP_RESOURCE + "/events";
    String TSP_INTERSECTION_EVENTS_RESOURCE = TSP_RESOURCE + "/intersection-events";

    String TSP_EVENT_SUMMARY_RESOURCE = TSP_RESOURCE + "/summary-events";
    String TSP_EVENT_TYPES_RESOURCE = TSP_RESOURCE + "/event-types";

    /**
     * Search tsp events history belong specific agencyID
     * <p>
     * GET: api/v1/tsp-events
     *
     * @param agencyId       agency id
     * @param fromTime       from time
     * @param toTime         to time
     * @param text           text for search be text(intersection name, intersection uuid)
     * @param eventType      type of event
     * @param routeNumber    event route number
     * @param orderByColumns sort by fields
     * @param page           page number
     * @param size           max size for page
     * @ {@code ResponseEntity<TspEventsResultObject>}
     */
    @Operation(summary = "Search Tsp Events")
    @GetMapping(TSP_EVENTS_RESOURCE)
    ResponseEntity<TspEventsResultObject> searchTspEvents(
            @Parameter(name = AgencyConstants.AGENCY_ID_HEADER_V2, 
                       example = AgencyConstants.AGENCY_ID_HEADER_V2 + "=4246",
                       required = true,
                       in = ParameterIn.HEADER, description = "Agency Id")
            @RequestHeader(AgencyConstants.AGENCY_ID_HEADER_V2) Integer agencyId,

            @Parameter(description = "query event time from this field. eventTime >= from_time")
            @RequestParam(value = "from_time")
            Long fromTime,

            @Parameter(description = "query event time to this field. eventTime <= to_time")
            @RequestParam(value = "to_time")
            Long toTime,

            @Parameter(description = "matching text for intersection id or intersection name")
            @RequestParam(value = "text", required = false)
            String text,

            @Parameter(description = "type of event")
            @RequestParam(value = "event_type", required = false)
            String eventType,

            @Parameter(description = "route number of event")
            @RequestParam(value = "route_number", required = false)
            Integer routeNumber,

            @Parameter(description = "Fields will be used to sort returning data")
            @RequestParam(value = "sort", required = false)
            String[] orderByColumns,

            @Parameter(description = "Page is used to page the returning result")
            @RequestParam(value = "page", required = false)
            Integer page,

            @Parameter(description = "Size is used to size the returning result")
            @RequestParam(value = "size", required = false)
            Integer size);

    /**
     * Search tsp intersection events
     * <p>
     * GET: /api/v1/tsp/intersection-events
     * </p>
     *
     * @param agencyId         id of agency need to search events
     * @param intersectionUUID id of intersection need to search events
     * @param fromTime         from time
     * @param toTime           to time
     * @param text             text search for intersection name
     * @param eventType        search by event type
     * @param orderByColumns   columns to order
     * @param page             paging
     * @param size             number of items in one page
     * @return {@code ResponseEntity<TspIntersectionEventsResultObject>}
     */
    @Operation(summary = "Search Tsp intersection events")
    @GetMapping(TSP_INTERSECTION_EVENTS_RESOURCE)
    ResponseEntity<TspIntersectionEventsResultObject> searchTspIntersectionEvents(
            @Parameter(name = AgencyConstants.AGENCY_ID_HEADER_V2, 
                       example = AgencyConstants.AGENCY_ID_HEADER_V2 + "=4246",
                       required = true,
                       in = ParameterIn.HEADER, description = "Agency Id")
            @RequestHeader(AgencyConstants.AGENCY_ID_HEADER_V2) Integer agencyId,

            @Parameter(description = "intersection id of event")
            @RequestParam(value = "intersection_uuid")
            String intersectionUUID,

            @Parameter(description = "query event time from this field. eventTime >= from_time")
            @RequestParam(value = "from_time")
            Long fromTime,

            @Parameter(description = "query event time to this field. eventTime <= to_time")
            @RequestParam(value = "to_time")
            Long toTime,

            @Parameter(description = "matching text for event details")
            @RequestParam(value = "text", required = false)
            String text,

            @Parameter(description = "type of event")
            @RequestParam(value = "event_type", required = false)
            String eventType,

            @Parameter(description = "Fields will be used to sort returning data")
            @RequestParam(value = "sort", required = false)
            String[] orderByColumns,

            @Parameter(description = "Page is used to page the returning result")
            @RequestParam(value = "page", required = false)
            Integer page,

            @Parameter(description = "Size is used to size the returning result")
            @RequestParam(value = "size", required = false)
            Integer size);

    /**
     * Search event summary
     * <p>
     * GET : /api/v1/tsp/summary-events
     * </p>
     *
     * @param agencyId       id of agency need to search
     * @param fromTime       from time
     * @param toTime         to time
     * @param text           text for matching intersection name
     * @param orderByColumns columns to order
     * @param page           paging
     * @param size           number of items in one page
     * @return {@code ResponseEntity<TspEventSummaryResultObject>}
     */
    @Operation(summary = "Search Tsp Event summary")
    @GetMapping(TSP_EVENT_SUMMARY_RESOURCE)
    ResponseEntity<TspEventSummaryResultObject> searchTspEventSummary(
            @Parameter(name = AgencyConstants.AGENCY_ID_HEADER_V2, 
                       example = AgencyConstants.AGENCY_ID_HEADER_V2 + "=4246",
                       required = true,
                       in = ParameterIn.HEADER, description = "Agency Id")
            @RequestHeader(AgencyConstants.AGENCY_ID_HEADER_V2) Integer agencyId,

            @Parameter(description = "query event time from this field. eventTime >= from_time")
            @RequestParam(value = "from_time")
            Long fromTime,

            @Parameter(description = "query event time to this field. eventTime <= to_time")
            @RequestParam(value = "to_time")
            Long toTime,

            @Parameter(description = "matching text for intersectionName")
            @RequestParam(value = "text", required = false)
            String text,

            @Parameter(description = "Fields will be used to sort returning data")
            @RequestParam(value = "sort", required = false)
            String[] orderByColumns,

            @Parameter(description = "Page is used to page the returning result")
            @RequestParam(value = "page", required = false)
            Integer page,

            @Parameter(description = "Size is used to size the returning result")
            @RequestParam(value = "size", required = false)
            Integer size
    );

    /**
     * Get all tsp event types
     * <p>
     * GET: /api/v1/tsp/event-types
     * </p>
     *
     * @return {@code ResponseEntity<TspEventTypesResultObject>}
     */
    @Operation(summary = "Get Tsp event types")
    @GetMapping(TSP_EVENT_TYPES_RESOURCE)
    ResponseEntity<TspEventTypesResultObject> getTspEventTypes(
            @Parameter(name = AgencyConstants.AGENCY_ID_HEADER_V2, 
                       example = AgencyConstants.AGENCY_ID_HEADER_V2 + "=4246",
                       required = true,
                       in = ParameterIn.HEADER, description = "Agency Id")
            @RequestHeader(AgencyConstants.AGENCY_ID_HEADER_V2) Integer agencyId
    );

}
