/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : PerfLogExportResultObject.java
 * Project     : SPM Platform
 */

package com.siemens.spm.perflogcrawler.api.vo.request;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.perflog.vo.PerfLogEventFilterVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@EqualsAndHashCode
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class PerfLogExportRequestVO implements Serializable {

    private static final long serialVersionUID = -8423462094339919928L;

    @JsonProperty("agency_uuid")
    @NotNull(message = "agency_required")
    private Integer agencyId;

    @JsonProperty("int_uuid")
    @NotNull(message = "intersection_required")
    private String intUUID;

    /**
     * Beginning time
     */
    @JsonProperty("from_time")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    @NotNull(message = "from_time_required")
    LocalDateTime fromTime;

    /**
     * Ending time
     */
    @JsonProperty("to_time")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    @NotNull(message = "to_time_required")
    LocalDateTime toTime;

    /**
     * List of event code and params to filter exported events. If this field is null or empty then should export all
     * event codes
     */
    @JsonProperty("event_list")
    private List<PerfLogEventFilterVO> eventCodeParams;

    @JsonProperty("export_type")
    @NotEmpty(message = "invalid_export_type")
    private String exportType;

}
