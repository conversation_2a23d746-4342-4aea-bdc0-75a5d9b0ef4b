package com.siemens.spm.perflogcrawler.api.tspevent.vo.request;

import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TspEventSearchRequest {

    private Integer agencyId;

    private LocalDateTime fromTime;

    private LocalDateTime toTime;

    private String text;

    private String requestType;

    private Integer routeNumber;

    private String[] orderByColumns;

    private Integer page;

    private Integer size;
}
