package com.siemens.spm.analysis.api.vo.optimization;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * This class used for serialization to persist data into DB. Be careful when have any change in this class(field,
 * annotation, ..). This can break when deserialize data fetch from DB
 *
 * <AUTHOR> Nguyen - <EMAIL>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProgrammedRingDataVO implements Serializable {

    private static final long serialVersionUID = 3239647042501781154L;

    @JsonProperty("lights")
    private List<LightBlockVO> lightDataBlocks;

    @JsonProperty("coord_phase")
    private Integer coordinatedPhase;

}
