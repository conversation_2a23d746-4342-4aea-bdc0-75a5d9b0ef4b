package com.siemens.spm.analysis.api.vo.optimization;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.analysis.api.vo.enums.ClusterType;
import com.siemens.spm.analysis.util.DateTimeUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TrafficProfileRequestVO extends OptimizationRequestVO {

    private static final long serialVersionUID = -1380735910720301730L;

    public static final int MIN_DATE_NUM = 7;

    @NotNull
    @JsonProperty("bin_size")
    private Integer binSize;

    @NotNull
    @JsonProperty("min_profile_duration")
    private Integer minProfileDuration;

    @NotNull
    @Min(2)
    @Max(10)
    @JsonProperty("num_of_profiles")
    private Integer numOfProfiles;

    @NotNull
    @JsonProperty("cluster_type")
    private ClusterType clusterType;

    @JsonIgnore
    public boolean isValidDateRange() {
        if (startDate == null || endDate == null) {
            return false;
        }

        return DateTimeUtil.filterDates(startDate, endDate, excludeDates).size() >= MIN_DATE_NUM;
    }

}
