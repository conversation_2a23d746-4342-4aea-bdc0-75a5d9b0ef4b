package com.siemens.spm.analysis.api.vo.performancemetric;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.analysis.api.vo.TemplateScheduleVO;
import com.siemens.spm.common.shared.vo.IntersectionIdsVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PMTemplateDetailVO implements Serializable {

    private static final long serialVersionUID = 2134662745558024005L;

    @JsonProperty("general_data")
    private PMTemplateGeneralDataVO generalDataVO;

    @JsonProperty("intersection")
    private IntersectionIdsVO intersectionIdsVO;

    @JsonProperty("schedule")
    private TemplateScheduleVO scheduleVO;

}
