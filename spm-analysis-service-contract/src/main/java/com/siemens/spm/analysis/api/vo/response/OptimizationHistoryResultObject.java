package com.siemens.spm.analysis.api.vo.response;

import java.io.Serializable;
import java.util.List;

import org.springframework.http.HttpStatus;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.analysis.api.vo.optimization.OptimizationHistoryVO;
import com.siemens.spm.common.shared.vo.AbstractResultObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

@JsonInclude(Include.NON_NULL)
public class OptimizationHistoryResultObject extends
        AbstractResultObject<OptimizationHistoryResultObject.ResponseData, OptimizationHistoryResultObject.StatusCode> {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    private StatusCode statusCode;

    private ResponseData data;

    @Getter
    @AllArgsConstructor
    public enum StatusCode {
        /** OK */
        SUCCESS("success", HttpStatus.OK),

        /** OK */
        NO_DATA("no_data", HttpStatus.NO_CONTENT),

        /** OK */
        INVALID_SORT("invalid_ordering_specified", HttpStatus.BAD_REQUEST),

        /** Uncaught exception */
        UNKNOWN_ERROR("unknown_error", HttpStatus.INTERNAL_SERVER_ERROR);

        private final String errorField;
        private final String message;
        private final HttpStatus httpStatus;

        StatusCode(String message, HttpStatus httpStatus) {
            this(null, message, httpStatus);
        }
    }

    @Override
    public HttpStatus getHttpStatus() {
        return statusCode.getHttpStatus();
    }

    @Override
    protected void setStatusCode(StatusCode value) {
        this.statusCode = value;
    }

    @Data
    @AllArgsConstructor
    @Builder
    @NoArgsConstructor
    @JsonInclude(Include.NON_NULL)
    public static class ResponseData implements Serializable {

        private static final long serialVersionUID = -6369509576315928129L;

        @JsonProperty("total_count")
        private Long totalCount;

        @JsonProperty("optimization_histories")
        private List<OptimizationHistoryVO> histories;

    }

    public OptimizationHistoryResultObject(ResponseData data) {
        super(data);
    }

    public OptimizationHistoryResultObject(ResponseData data, StatusCode statusCode) {
        super(data, statusCode);
    }

    @Override
    public String getMessage() {
        return statusCode.getMessage();
    }

    @Override
    public String getErrorFieldName() {
        return statusCode.getErrorField();
    }

    @Override
    public StatusCode getStatusCode() {
        return statusCode;
    }

    @Override
    public StatusCode getSuccessfulStatusValue() {
        return StatusCode.SUCCESS;
    }

    @Override
    protected void setData(ResponseData value) {
        this.data = value;
    }

    @Override
    protected StatusCode getErrorStatusValue() {
        return StatusCode.UNKNOWN_ERROR;
    }

    @Override
    public ResponseData getData() {
        return data;
    }
}
