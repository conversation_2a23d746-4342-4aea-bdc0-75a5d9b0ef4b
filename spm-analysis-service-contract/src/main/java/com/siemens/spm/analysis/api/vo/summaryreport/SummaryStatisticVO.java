package com.siemens.spm.analysis.api.vo.summaryreport;

import java.io.Serializable;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.common.shared.vo.StatisticDataVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SummaryStatisticVO implements Serializable {

    private static final long serialVersionUID = 6877047945063157949L;

    @JsonProperty("statistics_data")
    private Map<String, Map<String, StatisticDataVO>> statisticsDataMap;

}
