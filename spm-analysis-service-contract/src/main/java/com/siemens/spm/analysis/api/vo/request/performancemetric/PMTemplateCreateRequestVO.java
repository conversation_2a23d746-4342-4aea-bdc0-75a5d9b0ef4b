package com.siemens.spm.analysis.api.vo.request.performancemetric;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.analysis.api.vo.TemplateScheduleVO;
import com.siemens.spm.analysis.api.vo.constraint.ValidPMTemplateSchedule;
import com.siemens.spm.analysis.api.vo.performancemetric.PMTemplateSimpleDataVO;
import com.siemens.spm.common.shared.vo.IntersectionIdsVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PMTemplateCreateRequestVO {

    @Valid
    @NotNull
    @JsonProperty("general_data")
    private PMTemplateSimpleDataVO generalDataVO;

    @Valid
    @NotNull
    @JsonProperty("intersection")
    private IntersectionIdsVO intersectionIdsVO;

    @Valid
    @NotNull
    @ValidPMTemplateSchedule(message = "templates.schedule.invalid")
    @JsonProperty("schedule")
    private TemplateScheduleVO scheduleVO;

}
