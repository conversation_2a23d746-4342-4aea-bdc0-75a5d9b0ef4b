package com.siemens.spm.analysis.api.vo.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.siemens.spm.common.shared.vo.AbstractSimpleResultObject;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.http.HttpStatus;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class CreatingOptimizationResultObject
        extends AbstractSimpleResultObject<CreatingOptimizationResultObject.StatusCode> {

    private static final long serialVersionUID = 1L;

    @Getter
    @AllArgsConstructor
    public enum StatusCode {
        /** OK */
        SUCCESS("success", HttpStatus.OK),

        /** INVALID_TYPE */
        INVALID_TYPE("optimization.validation.invalid_type", HttpStatus.BAD_REQUEST),
        INVALID_TRAFFIC_PROFILE("optimization.validation.invalid_traffic_profile", HttpStatus.BAD_REQUEST),

        /** INVALID_INTERSECTION */
        INVALID_INTERSECTION("invalid_intersection", HttpStatus.BAD_REQUEST),
        INVALID_INTERSECTION_AGENCY("invalid_intersection_agency", HttpStatus.BAD_REQUEST),

        INVALID_AGENCY("invalid_agency", HttpStatus.BAD_REQUEST),

        /** INVALID_TIME_RANGE */
        INVALID_TIME_RANGE("invalid_time_range", HttpStatus.BAD_REQUEST),

        INVALID_BIAS("invalid_bias", HttpStatus.BAD_REQUEST),

        INVALID_CORRIDOR("invalid_corridor", HttpStatus.BAD_REQUEST),

        UNAUTHORIZED("unauthorized", HttpStatus.UNAUTHORIZED),

        /** Uncaught exception */
        ERROR("unknown_exception", HttpStatus.INTERNAL_SERVER_ERROR),

        /** Forbidden access to unavailable intersection(s) */
        INTERSECTION_DISABLED("intersection_unavailable", HttpStatus.FORBIDDEN);

        private final String errorField;
        private final String message;
        private final HttpStatus httpStatus;

        StatusCode(String message, HttpStatus httpStatus) {
            this(null, message, httpStatus);
        }
    }

    private StatusCode statusCode;

    private String taskId;

    public CreatingOptimizationResultObject(StatusCode statusCode) {
        this.statusCode = statusCode;
    }

    public CreatingOptimizationResultObject(StatusCode statusCode, String taskId) {
        this.statusCode = statusCode;
        this.taskId = taskId;
    }

    @Override
    protected StatusCode getErrorStatusValue() {
        return StatusCode.ERROR;
    }

    @Override
    public StatusCode getSuccessfulStatusValue() {
        return StatusCode.SUCCESS;
    }

    @Override
    public StatusCode getStatusCode() {
        return statusCode;
    }

    @Override
    protected void setStatusCode(StatusCode value) {
        this.statusCode = value;
    }

    @Override
    public HttpStatus getHttpStatus() {
        return statusCode.getHttpStatus();
    }

    @Override
    public String getMessage() {
        return statusCode.getMessage();
    }

    @Override
    public String getErrorFieldName() {
        return statusCode.getErrorField();
    }

    public static CreatingOptimizationResultObject success(String taskId) {
        return new CreatingOptimizationResultObject(StatusCode.SUCCESS, taskId);
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

}
