package com.siemens.spm.analysis.api.vo.optimization;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.siemens.spm.common.shared.resource.UUIDConstants;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "type")
@JsonSubTypes({
        @JsonSubTypes.Type(value = TrafficProfileRequestVO.class, name = "TRAFFIC_PROFILE"),
        @JsonSubTypes.Type(value = OffsetOptimizationRequestVO.class, name = "OFFSET_OPTIMIZATION"),
        @JsonSubTypes.Type(value = HistoricTimeSpaceRequestVO.class, name = "HISTORIC_TIME_SPACE"),
        @JsonSubTypes.Type(value = CorridorOffsetOptimizationRequestVO.class, name = "CORRIDOR_OFFSET_OPTIMIZATION"),
})
public abstract class OptimizationRequestVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("agency_id")
    protected Integer agencyId;

    @Pattern(regexp = UUIDConstants.REGEXP_FOR_1_INTERSECTION_ID, message = "intersection_id_invalid")
    @JsonProperty("intersection_id")
    protected String intUUID;

    @JsonProperty("exclude_dates")
    protected transient List<LocalDate> excludeDates;

    @NotNull
    @JsonProperty("start_date")
    protected LocalDate startDate;

    @NotNull
    @JsonProperty("end_date")
    protected LocalDate endDate;

    @NotNull
    @JsonProperty("start_time")
    protected LocalTime startTime;

    @NotNull
    @JsonProperty("end_time")
    protected LocalTime endTime;

    @JsonIgnore
    public abstract boolean isValidDateRange();

}
