package com.siemens.spm.analysis.api.vo.optimization;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BaseOptimizationResponseVO implements Serializable {

    private static final long serialVersionUID = 4018795781334664056L;

    @JsonProperty("id")
    protected Long id;

    @JsonProperty("general_data")
    protected OptimizationHistoryVO generalData;

}
