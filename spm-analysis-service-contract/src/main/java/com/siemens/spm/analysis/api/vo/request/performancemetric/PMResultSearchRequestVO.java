package com.siemens.spm.analysis.api.vo.request.performancemetric;

import java.io.Serializable;
import java.sql.Timestamp;

import jakarta.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PMResultSearchRequestVO implements Serializable {

    private static final long serialVersionUID = -6639000687362234780L;

    @NotNull
    @JsonProperty("agency_id")
    private Integer agencyId;

    @JsonProperty("text")
    private String text;

    @JsonProperty("metric_type")
    private String metricTypeId;

    @JsonProperty("created_at_from")
    private Timestamp createdAtFrom;

    @JsonProperty("created_at_to")
    private Timestamp createdAtTo;

    @JsonProperty("owner_id")
    private Long ownerId;

    @JsonProperty("order_by_columns")
    private String[] orderByColumns;

    @JsonProperty("page")
    private Integer page;

    @JsonProperty("size")
    private Integer size;

}

