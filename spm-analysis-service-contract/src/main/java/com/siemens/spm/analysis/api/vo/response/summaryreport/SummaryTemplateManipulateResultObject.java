package com.siemens.spm.analysis.api.vo.response.summaryreport;

import org.springframework.http.HttpStatus;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.siemens.spm.common.shared.vo.AbstractSimpleResultObject;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SummaryTemplateManipulateResultObject extends
        AbstractSimpleResultObject<SummaryTemplateManipulateResultObject.SummaryTemplateManipulateStatusCode> {

    private static final long serialVersionUID = 2595893491265448546L;

    private SummaryTemplateManipulateStatusCode statusCode;

    @Override
    public SummaryTemplateManipulateStatusCode getStatusCode() {
        return statusCode;
    }

    @Override
    public HttpStatus getHttpStatus() {
        return statusCode.getHttpStatus();
    }

    @Override
    public String getErrorFieldName() {
        return statusCode.getErrorField();
    }

    @Override
    protected void setStatusCode(SummaryTemplateManipulateStatusCode value) {
        this.statusCode = value;
    }

    @Override
    protected SummaryTemplateManipulateStatusCode getErrorStatusValue() {
        return SummaryTemplateManipulateStatusCode.ERROR;
    }

    /**
     * @return Which value in the enumeration of status values indicates success
     */
    @Override
    public SummaryTemplateManipulateStatusCode getSuccessfulStatusValue() {
        return SummaryTemplateManipulateStatusCode.SUCCESS;
    }

    @Override
    public String getMessage() {
        return statusCode.getMessage();
    }

    @Getter
    @AllArgsConstructor
    public enum SummaryTemplateManipulateStatusCode {

        SUCCESS("success", HttpStatus.OK),
        NOT_FOUND("not_found", HttpStatus.NOT_FOUND),
        NO_CONTENT("no_content", HttpStatus.NO_CONTENT),
        CREATED("created", HttpStatus.CREATED),
        TEMPLATE_NOT_FOUND("templates.not_found", HttpStatus.BAD_REQUEST),
        NO_ACTION("action", "no_action", HttpStatus.BAD_REQUEST),
        ACTION_INVALID("action", "templates.action_invalid", HttpStatus.BAD_REQUEST),
        INTERSECTION_SCOPE_INVALID("intersection: scope", "templates.intersections.scope_invalid",
                HttpStatus.BAD_REQUEST),

        // Status codes map 1-1 with status code of
        // IntersectionsInAgencyVerifyResultObject
        MISSING_AGENCY_ID("agency_id", "agency_id_is_missing", HttpStatus.BAD_REQUEST),
        AGENCY_NOT_FOUND("agency_id", "agency_not_found", HttpStatus.BAD_REQUEST),
        INTERSECTION_NOT_FOUND("intersection", "intersection_not_found", HttpStatus.BAD_REQUEST),
        INTERSECTION_NOT_UNDER_AGENCY_MANAGEMENT("intersection", "intersection_not_under_agency_management",
                HttpStatus.BAD_REQUEST),
        // end status codes mapping

        ERROR("unknown_exception", HttpStatus.INTERNAL_SERVER_ERROR);

        private final String errorField;
        private final String message;
        private final HttpStatus httpStatus;

        SummaryTemplateManipulateStatusCode(String message, HttpStatus httpStatus) {
            this(null, message, httpStatus);
        }

    }

}
