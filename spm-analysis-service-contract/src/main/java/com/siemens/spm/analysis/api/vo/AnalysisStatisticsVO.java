/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AorPercent24hVO.java
 * Project     : SPM Platform
 */
package com.siemens.spm.analysis.api.vo;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AnalysisStatisticsVO implements Serializable {

    private static final long serialVersionUID = -4919413092417150534L;

    @JsonProperty("intersections")
    private List<IntersectionStatisticsVO> intStatisticsVOList;

    @JsonProperty("updated_time")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Timestamp updatedTime;

}
