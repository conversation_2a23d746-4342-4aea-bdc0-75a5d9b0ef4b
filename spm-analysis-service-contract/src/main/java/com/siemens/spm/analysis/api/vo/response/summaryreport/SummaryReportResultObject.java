package com.siemens.spm.analysis.api.vo.response.summaryreport;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.analysis.api.vo.summaryreport.SummaryReportInfoVO;
import com.siemens.spm.analysis.api.vo.response.summaryreport.SummaryReportResultObject.ResponseData;
import com.siemens.spm.analysis.api.vo.response.summaryreport.SummaryReportResultObject.StatusCode;
import com.siemens.spm.common.shared.vo.AbstractResultObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.springframework.http.HttpStatus;

/**
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SummaryReportResultObject extends AbstractResultObject<ResponseData, StatusCode> {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    private ResponseData data;

    private StatusCode statusCode;

    @Override
    public String getMessage() {
        return statusCode.getMessage();
    }

    @Override
    public StatusCode getStatusCode() {
        return statusCode;
    }

    @Override
    protected StatusCode getErrorStatusValue() {
        return StatusCode.ERROR;
    }

    @Override
    protected void setStatusCode(StatusCode value) {
        statusCode = value;
    }

    @Getter
    @AllArgsConstructor
    public enum StatusCode {
        SUCCESS("success", HttpStatus.OK),
        NO_DATA("no_data", HttpStatus.NO_CONTENT),

        ERROR("unknown_exception", HttpStatus.INTERNAL_SERVER_ERROR),
        NOT_FOUND("no_data", HttpStatus.NOT_FOUND),

        INVALID_SORT_COLUMN("sort", "sort_column_invalid", HttpStatus.BAD_REQUEST),
        INVALID_SORT_ORDER("sort", "sort_order_invalid", HttpStatus.BAD_REQUEST);

        private final String errorField;
        private final String message;
        private final HttpStatus httpStatus;

        StatusCode(String message, HttpStatus httpStatus) {
            this(null, message, httpStatus);
        }
    }

    @Override
    public String getErrorFieldName() {
        return statusCode.getErrorField();
    }

    @Override
    public StatusCode getSuccessfulStatusValue() {
        return StatusCode.SUCCESS;
    }

    @Builder
    @AllArgsConstructor
    @Data
    @NoArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class ResponseData implements Serializable {

        private static final long serialVersionUID = -6369509576315928129L;

        @JsonProperty("total_count")
        private Long totalCount;

        @JsonProperty("summary_reports")
        private List<SummaryReportInfoVO> reportTemplates;

    }

    @Override
    protected void setData(ResponseData value) {
        data = value;
    }

    @Override
    public ResponseData getData() {
        return data;
    }

    @Override
    public HttpStatus getHttpStatus() {
        return statusCode.getHttpStatus();
    }

}
