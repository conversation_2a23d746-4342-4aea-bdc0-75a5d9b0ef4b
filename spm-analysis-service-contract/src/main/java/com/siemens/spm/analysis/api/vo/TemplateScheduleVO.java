package com.siemens.spm.analysis.api.vo;

import java.io.Serializable;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;
import java.util.Set;

import jakarta.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.analysis.api.vo.enums.ScheduleScope;
import com.siemens.spm.analysis.api.vo.summaryreport.SummaryMetricVO;
import com.siemens.spm.common.constant.TimeConstants;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TemplateScheduleVO implements Serializable {

    private static final long serialVersionUID = -3350686724397566750L;

    @NotNull(message = "templates.schedule.scope_not_null")
    @JsonProperty("scope")
    private ScheduleScope scope;

    @JsonProperty("time")
    private LocalTime time;

    @JsonProperty("value")
    private Value value;

    @JsonProperty("metric_settings")
    private List<SummaryMetricVO> metricSettings;

    @JsonProperty("mail_receive")
    private Boolean mailReceive;

    @JsonIgnore
    public boolean isValidValue() {
        if (value == null) {
            return false;
        }

        switch (scope) {
        case ONCE:
            return value.getDate() != null;

        case EVERY_DAY:
            return value.isValidEveryDay();

        case DAY_OF_WEEK:
            return value.isValidDayOfWeeks();

        case DAY_OF_MONTH:
            return value.isValidDayOfMonths();

        default:
            return false;
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Value implements Serializable {

        private static final long serialVersionUID = 7086222170670247216L;

        @JsonProperty("date")
        private LocalDate date;

        @JsonProperty("offset")
        private Integer offset;

        @JsonProperty("day_of_week")
        private Set<DayOfWeek> dayOfWeeks;

        @JsonProperty("day_of_month")
        private Set<Integer> dayOfMonths;

        @JsonIgnore
        public boolean isValidEveryDay() {
            return date != null && offset != null
                    && offset >= TimeConstants.MIN_DAY_PER_MONTH
                    && offset <= TimeConstants.MAX_DAY_PER_MONTH;
        }

        @JsonIgnore
        public boolean isValidDayOfWeeks() {
            if (dayOfWeeks == null
                    || dayOfWeeks.size() < TimeConstants.MIN_DAY_PER_WEEK
                    || dayOfWeeks.size() > TimeConstants.MAX_DAY_PER_WEEK) {
                return false;
            }

            for (DayOfWeek dayOfWeek : dayOfWeeks) {
                if (dayOfWeek == null) {
                    return false;
                }
            }

            return true;
        }

        @JsonIgnore
        public boolean isValidDayOfMonths() {
            if (dayOfMonths == null
                    || dayOfMonths.size() < TimeConstants.MIN_DAY_PER_MONTH
                    || dayOfMonths.size() > TimeConstants.MAX_DAY_PER_MONTH) {
                return false;
            }

            for (Integer dayOfMonth : dayOfMonths) {
                if (dayOfMonth == null) {
                    return false;
                }
            }

            return true;
        }
    }

    public static Value resolveScheduleValue(@NotNull TemplateScheduleVO scheduleVO) {
        Value value = scheduleVO.getValue();
        if (scheduleVO.getScope() == null) {
            throw new IllegalArgumentException("Template schedule Scope can not be null!");
        }

        switch (scheduleVO.getScope()) {
        case ONCE:
            return Value.builder()
                    .date(value.getDate())
                    .build();
        case EVERY_DAY:
            return Value.builder()
                    .date(value.getDate())
                    .offset(value.getOffset())
                    .build();
        case DAY_OF_WEEK:
            return Value.builder()
                    .dayOfWeeks(value.getDayOfWeeks())
                    .build();
        case DAY_OF_MONTH:
            return Value.builder()
                    .dayOfMonths(value.getDayOfMonths())
                    .build();
        default:
            return null;
        }
    }

}
