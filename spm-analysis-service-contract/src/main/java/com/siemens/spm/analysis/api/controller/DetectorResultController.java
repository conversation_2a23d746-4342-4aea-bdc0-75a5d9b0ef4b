// package com.siemens.spm.analysis.api.controller;

// import java.util.List;

// import com.siemens.spm.analysis.api.vo.enums.TemplateAggregation;
// import com.siemens.spm.analysis.api.vo.response.detectorreport.DetectorIntersectionResultObject;

// import org.springframework.http.HttpStatus;
// import org.springframework.http.ResponseEntity;
// import org.springframework.web.bind.annotation.DeleteMapping;
// import org.springframework.web.bind.annotation.GetMapping;
// import org.springframework.web.bind.annotation.PathVariable;
// import org.springframework.web.bind.annotation.RequestMapping;
// import org.springframework.web.bind.annotation.RequestParam;
// import org.springframework.web.bind.annotation.ResponseStatus;

// import com.siemens.spm.analysis.api.vo.response.detectorreport.DetectorReportDetailResultObject;
// import com.siemens.spm.analysis.api.vo.response.detectorreport.DetectorResultManipulateResultObject;
// import com.siemens.spm.analysis.api.vo.response.detectorreport.DetectorResultSearchResultObject;
// import com.siemens.spm.common.api.PublicController;
// import com.siemens.spm.common.shared.resource.UUIDConstants;
// import io.swagger.v3.oas.annotations.Operation;
// import io.swagger.v3.oas.annotations.Parameter;
// import io.swagger.v3.oas.annotations.enums.ParameterIn;
// import io.swagger.v3.oas.annotations.responses.ApiResponse;
// import io.swagger.v3.oas.annotations.responses.ApiResponses;
// import io.swagger.v3.oas.annotations.tags.Tag;
// import jakarta.validation.Valid;
// import jakarta.validation.constraints.NotEmpty;
// import jakarta.validation.constraints.NotNull;
// import jakarta.validation.constraints.Pattern;

// @Tag(name = "detector-report-result", description = "Detector Report Result Resources")
// @RequestMapping(DetectorResultController.API_ROOT)
// public interface DetectorResultController extends PublicController {

//     String VERSION = "/v1";

//     String DETECTOR_RESOURCES = "/detector-report";

//     String DETECTOR_RESULT_RESOURCES = DETECTOR_RESOURCES + "/results";

//     String API_ROOT = PUBLIC_API + VERSION + AGENCY_RESOURCE + AGENCY_ID_RESOURCE + DETECTOR_RESULT_RESOURCES;

//     /**
//      * Retrieve all detector report results
//      *
//      * @return {@code ResponseEntity<DetectorResultSearchResultObject>}
//      */
//     @Operation(summary = "Retrieve all detector report results")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "200", description = "Successful"),
//             @ApiResponse(responseCode = "404", description = "Not Found")
//     })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
//             description = "Bearer Token type")
//     @ResponseStatus(HttpStatus.OK)
//     @GetMapping
//     ResponseEntity<DetectorResultSearchResultObject> search(
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(description = "Text to search")
//             @RequestParam(value = "text", required = false)
//             String text,

//             @Parameter(description = "Id of an owner that the report belong to")
//             @RequestParam(value = "owner_id", required = false)
//             Long ownerId,

//             @Parameter(description = "Date that filter data by from date")
//             @RequestParam(value = "from_date", required = false)
//             Long fromDate,

//             @Parameter(description = "Date that filter data by to date")
//             @RequestParam(value = "to_date", required = false)
//             Long toDate,

//             @Parameter(description = "Fields will be used to sort returning data")
//             @RequestParam(value = "sort", required = false)
//             String[] orderByColumns,

//             @Parameter(description = "Aggregation unit of template")
//             @RequestParam(value = "aggregation", required = false)
//             TemplateAggregation aggregation,

//             @Parameter(description = "Page is used to page the returning result")
//             @RequestParam(value = "page", required = false)
//             Integer page,

//             @Parameter(description = "Size is used to size the returning result")
//             @RequestParam(value = "size", required = false)
//             Integer size
//     );

//     /**
//      * Retrieve the detector report result details
//      *
//      * @return {@code ResponseEntity<DetectorReportDetailResultObject>}
//      */
//     @Operation(summary = "Retrieve the detector report result details")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "200", description = "success"),
//             @ApiResponse(responseCode = "404", description = "Not Found")
//     })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true,
//             in = ParameterIn.HEADER, description = "Bearer Token type")
//     @ResponseStatus(HttpStatus.OK)
//     @GetMapping("/{id}")
//     ResponseEntity<DetectorReportDetailResultObject> getDetectorReportAggregate(
//             @PathVariable(AGENCY_ID) Integer agencyId,
//             @Parameter(description = "The id of detector report result")
//             @Valid
//             @PathVariable("id") Long resultId);

//     @Operation(summary = "Retrieve the detector report result details of a intersection")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "200", description = "success"),
//             @ApiResponse(responseCode = "404", description = "Not Found")
//     })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true,
//             in = ParameterIn.HEADER, description = "Bearer Token type")
//     @ResponseStatus(HttpStatus.OK)
//     @GetMapping("/{id}/intersections/{int_id}")
//     ResponseEntity<DetectorIntersectionResultObject> getDetectorIntersectionDetail(
//             @PathVariable(AGENCY_ID) Integer agencyId,
//             @Parameter(description = "The id of detector report result")
//             @Valid
//             @PathVariable("id") Long resultId,
//             @Parameter(description = "id of the selected intersection")
//             @Valid
//             @PathVariable("int_id") String intersectionId);

//     /**
//      * Soft delete result record
//      *
//      * @param ids contains List of result id to delete
//      * @return {@code DetectorResultManipulateResultObject} object
//      */
//     @Operation(summary = "Delete detector results")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "200", description = "Successful"),
//             @ApiResponse(responseCode = "404", description = "Not Found")
//     })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
//             description = "Bearer Token type")
//     @ResponseStatus(HttpStatus.OK)
//     @DeleteMapping
//     ResponseEntity<DetectorResultManipulateResultObject> deletes(
//             @PathVariable(AGENCY_ID) Integer agencyId,
//             @Valid @NotEmpty(message = "detector_report.results.result_ids_not_empty")
//             @RequestParam(value = "result_ids") List<Long> ids
//     );
// }
