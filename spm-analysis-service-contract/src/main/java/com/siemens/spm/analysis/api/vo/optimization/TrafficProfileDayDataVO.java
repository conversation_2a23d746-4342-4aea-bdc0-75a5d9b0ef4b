package com.siemens.spm.analysis.api.vo.optimization;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TrafficProfileDayDataVO implements Serializable {

    private static final long serialVersionUID = 8014069614198302719L;

    @JsonProperty("date")
    private LocalDate date;

    @JsonProperty("data_points")
    private List<TrafficProfileDataPointVO> dataPointList;

}
