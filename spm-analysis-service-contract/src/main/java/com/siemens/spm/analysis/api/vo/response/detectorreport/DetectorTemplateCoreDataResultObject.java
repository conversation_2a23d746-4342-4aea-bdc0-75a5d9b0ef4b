package com.siemens.spm.analysis.api.vo.response.detectorreport;

import java.io.Serial;

import org.springframework.http.HttpStatus;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.siemens.spm.analysis.api.vo.detectorreport.DetectorTemplateCoreDataVO;
import com.siemens.spm.common.shared.vo.AbstractResultObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DetectorTemplateCoreDataResultObject extends AbstractResultObject<DetectorTemplateCoreDataVO, DetectorTemplateCoreDataResultObject.StatusCode> {

    @Serial
    private static final long serialVersionUID = -3492069276570918946L;

    private DetectorTemplateCoreDataVO data;

    private StatusCode statusCode;
    @Override
    public String getMessage() {
        return statusCode.getMessage();
    }

    @Override
    protected DetectorTemplateCoreDataResultObject.StatusCode getErrorStatusValue() {
        return StatusCode.ERROR;
    }

    @Override
    public DetectorTemplateCoreDataResultObject.StatusCode getStatusCode() {
        return statusCode;
    }

    @Override
    protected void setStatusCode(DetectorTemplateCoreDataResultObject.StatusCode value) {
        statusCode = value;
    }

    @Override
    public DetectorTemplateCoreDataResultObject.StatusCode getSuccessfulStatusValue() {
        return StatusCode.SUCCESS;
    }

    @Getter
    @AllArgsConstructor
    public enum StatusCode {
        /**
         * OK
         */
        SUCCESS("success", HttpStatus.OK),
        CREATED("created", HttpStatus.CREATED),
        NOT_FOUND("not_found", HttpStatus.NOT_FOUND),

        /**
         * Internal Server Error
         */
        ERROR("unknown_exception", HttpStatus.INTERNAL_SERVER_ERROR);

        private final String errorField;
        private final String message;
        private final HttpStatus httpStatus;

        StatusCode(String message, HttpStatus httpStatus) {
            this(null, message, httpStatus);
        }
    }

    @Override
    public String getErrorFieldName() {
        return statusCode.getErrorField();
    }

    @Override
    protected void setData(DetectorTemplateCoreDataVO value) {
        data = value;
    }

    @Override
    public HttpStatus getHttpStatus() {
        return statusCode.getHttpStatus();
    }
}
