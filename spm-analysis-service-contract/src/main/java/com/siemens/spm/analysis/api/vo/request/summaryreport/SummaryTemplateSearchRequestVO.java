package com.siemens.spm.analysis.api.vo.request.summaryreport;

import java.io.Serializable;
import java.sql.Timestamp;
import java.time.DayOfWeek;
import java.util.Set;

import jakarta.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.analysis.api.vo.enums.TemplateAggregation;
import com.siemens.spm.analysis.api.vo.enums.TemplateStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SummaryTemplateSearchRequestVO implements Serializable {

    private static final long serialVersionUID = 1129980516231220761L;

    @NotNull
    @JsonProperty("agency_id")
    private Integer agencyId;

    @JsonProperty("created_at_from")
    private Timestamp createdAtFrom;

    @JsonProperty("created_at_to")
    private Timestamp createdAtTo;

    @JsonProperty("week_days")
    private Set<DayOfWeek> weekDays;

    @JsonProperty("aggregation")
    private TemplateAggregation aggregation;

    @JsonProperty("order_by_columns")
    private String[] orderByColumns;

    @JsonProperty("owner_id")
    private Long ownerId;

    @JsonProperty("page")
    private Integer page;

    @JsonProperty("size")
    private Integer size;

    @JsonProperty("status")
    private TemplateStatus status;

    @JsonProperty("text")
    private String text;

}
