package com.siemens.spm.analysis.api.vo.optimization;

import java.io.Serializable;
import java.time.LocalTime;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ClusterDataVO extends ClusterInfoVO implements Serializable {

    private static final long serialVersionUID = -4514353907920422507L;

    @JsonProperty("from_time")
    private LocalTime fromTime;

    @JsonProperty("to_time")
    private LocalTime toTime;

    @JsonProperty("cluster")
    private Integer cluster;

}
