/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AnalysisController.java
 * Project     : SPM Platform
 */
package com.siemens.spm.analysis.api.controller;

import com.siemens.spm.analysis.api.vo.request.AnalysisStatisticsRequestVO;
import com.siemens.spm.analysis.api.vo.response.AnalysisStatisticsResultObject;
import com.siemens.spm.common.intercom.InterComController;
import com.siemens.spm.common.util.RestUtils;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * Common interface for internal Analysis resources
 */
@Tag(name = "internal-analysis", description = "Internal Analysis API")
@RequestMapping(InternalAnalysisController.API_ROOT)
public interface InternalAnalysisController extends InterComController {

    String VERSION = "/v1";
    String ANALYSIS_RESOURCE = "/analysis";
    String API_ROOT = INTERNAL_API + VERSION + ANALYSIS_RESOURCE;

    String AGENCY_UUID = "agency_uuid";
    String AGENCY_UUID_RESOURCE = "/{" + AGENCY_UUID + "}";

    String ANALYSIS_STATISTICS_RESOURCE = AGENCY_UUID_RESOURCE + "/statistics";
    String POST_ANALYSIS_STATISTICS_URL = API_ROOT + "/%d/statistics";

    /**
     * Get analysis statistic belongs to selected Intersections during last 24 hours.
     * <p>
     * POST /internal-api/v1/analysis/aor-percent-24h
     *
     * @param requestVO
     * @return
     */
    @PostMapping(ANALYSIS_STATISTICS_RESOURCE)
    ResponseEntity<AnalysisStatisticsResultObject> getAnalysisStatistics(
            @Parameter(description = "Agency UUID") @PathVariable(AGENCY_UUID) Integer agencyUUID,
            @Parameter(description = "Selected Intersections") @RequestBody AnalysisStatisticsRequestVO requestVO
    );

    /**
     * Invoke getAnalysisStatistics (POST)
     *
     * @param endpoint   target service endpoint
     * @param agencyUUID agency's id
     * @param requestVO  payload data of request
     * @return ResponseEntity<AnalysisStatisticsResultObject>
     */
    static ResponseEntity<AnalysisStatisticsResultObject> invokeGetAnalysisStatistics(String endpoint,
                                                                                      Integer agencyUUID,
                                                                                      AnalysisStatisticsRequestVO requestVO) {
        String url = endpoint + String.format(POST_ANALYSIS_STATISTICS_URL, agencyUUID);

        return RestUtils.postWithAgencyHeader(url, String.valueOf(agencyUUID), requestVO,
                AnalysisStatisticsResultObject.class);
    }

}
