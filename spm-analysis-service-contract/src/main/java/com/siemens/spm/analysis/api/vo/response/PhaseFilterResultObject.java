/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : PhaseFiltersResultObject.java
 * Project     : SPM Platform
 */

package com.siemens.spm.analysis.api.vo.response;

import java.util.ArrayList;

import org.springframework.http.HttpStatus;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.siemens.spm.analysis.api.vo.PhaseFilterVO;
import com.siemens.spm.common.shared.vo.AbstractResultObject;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * NOTE: Include null data
 */
@JsonIgnoreProperties({ "statusCode", "message", "httpStatus", "errorFieldName" })
public class PhaseFilterResultObject
        extends AbstractResultObject<ArrayList<PhaseFilterVO>, PhaseFilterResultObject.StatusCode> {

    private static final long serialVersionUID = 5713718549010544650L;

    private StatusCode statusCode;

    private ArrayList<PhaseFilterVO> data;

    /**
     * Create result object with SUCCESS status and data
     *
     * @param phaseFilterVOList
     */
    public PhaseFilterResultObject(ArrayList<PhaseFilterVO> phaseFilterVOList) {
        this.data = phaseFilterVOList;
        this.statusCode = getSuccessfulStatusValue();
    }

    /**
     * Create result object with null data and an error code
     *
     * @param statusCode Indicating error
     */
    public PhaseFilterResultObject(StatusCode statusCode) {
        this.statusCode = statusCode;
    }

    @Override
    protected void setData(ArrayList<PhaseFilterVO> value) {
        this.data = value;
    }

    @Override
    public ArrayList<PhaseFilterVO> getData() {
        return data;
    }

    @Override
    public String getMessage() {
        return statusCode.getMessage();
    }

    @Override
    protected void setStatusCode(StatusCode value) {
        this.statusCode = value;
    }

    @Override
    public HttpStatus getHttpStatus() {
        return statusCode.getHttpStatus();
    }

    @Override
    protected StatusCode getErrorStatusValue() {
        return StatusCode.UNKNOWN_ERROR;
    }

    @Override
    public StatusCode getStatusCode() {
        return statusCode;
    }

    @Override
    public StatusCode getSuccessfulStatusValue() {
        return StatusCode.SUCCESS;
    }

    @Override
    public String getErrorFieldName() {
        return this.statusCode.getErrorField();
    }

    @Getter
    @AllArgsConstructor
    public enum StatusCode {
        /** OK */
        SUCCESS("success", HttpStatus.OK),

        UNSUPPORTED_ANALYSIS("unsupported_analysis", HttpStatus.BAD_REQUEST),
        INTERSECTION_NOT_AVAILABLE("int_uuid", "intersection_is_not_available", HttpStatus.BAD_REQUEST),
        INVALID_TIME_RANGE("invalid_time_range", HttpStatus.BAD_REQUEST),
        NO_DATA("no_data", HttpStatus.NO_CONTENT),

        /** Internal Server Error */
        UNKNOWN_ERROR("unknown_error", HttpStatus.INTERNAL_SERVER_ERROR);

        private final String errorField;
        private final String message;
        private final HttpStatus httpStatus;

        StatusCode(String message, HttpStatus httpStatus) {
            this(null, message, httpStatus);
        }
    }

}
