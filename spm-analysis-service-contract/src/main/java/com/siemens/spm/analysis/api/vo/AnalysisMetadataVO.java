/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AnalysisMetadataVO.java
 * Project     : spm-analysis-lib
 */
package com.siemens.spm.analysis.api.vo;

import java.io.Serializable;
import java.util.ArrayList;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.common.message.MessageService;
import com.siemens.spm.common.shared.domaintype.analysis.AnalysisType;
import com.siemens.spm.common.shared.vo.TranslatableVO;
import lombok.Getter;
import lombok.Setter;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AnalysisMetadataVO implements Serializable, TranslatableVO {

    private static final long serialVersionUID = -7619492864469341330L;

    /**
     * Get metadata for Arrivals on Red Analysis
     *
     * @return {@code AnalysisMetadataVO}
     */
    public static AnalysisMetadataVO aor() {
        AnalysisMetadataVO vo = new AnalysisMetadataVO(AnalysisType.ARRIVALS_ON_RED.getId(),
                AnalysisType.ARRIVALS_ON_RED.getName(),
                true);
        vo.extraFieldList.add(ExtraFieldVO.binSize());
        return vo;
    }

    /**
     * Get metadata for Arrivals on Green Analysis
     *
     * @return {@code AnalysisMetadataVO}
     */
    public static AnalysisMetadataVO aog() {
        AnalysisMetadataVO vo = new AnalysisMetadataVO(AnalysisType.ARRIVALS_ON_GREEN.getId(),
                AnalysisType.ARRIVALS_ON_GREEN.getName(),
                true);
        vo.extraFieldList.add(ExtraFieldVO.binSize());
        return vo;
    }

    /**
     * Get metadata for Phase Terminations Analysis
     *
     * @return {@code AnalysisMetadataVO}
     */
    public static AnalysisMetadataVO pt() {
        return new AnalysisMetadataVO(AnalysisType.PHASE_TERMINATION.getId(),
                AnalysisType.PHASE_TERMINATION.getName(),
                true);
    }

    /**
     * Get metadata for Coordination Analysis
     *
     * @return {@code AnalysisMetadataVO}
     */
    public static AnalysisMetadataVO coordination() {
        AnalysisMetadataVO vo = new AnalysisMetadataVO(AnalysisType.COORDINATION.getId(),
                AnalysisType.COORDINATION.getName(),
                true);
        vo.extraFieldList.add(ExtraFieldVO.binSize());
        return vo;
    }

    /**
     * Get metadata for Coordination Health Analysis
     *
     * @return {@code AnalysisMetadataVO}
     */

    public static AnalysisMetadataVO coordinationHealth() {
        AnalysisMetadataVO vo = new AnalysisMetadataVO(AnalysisType.COORDINATION_HEALTH.getId(),
                AnalysisType.COORDINATION_HEALTH.getName(),
                true);
        vo.extraFieldList.add(ExtraFieldVO.binSize());
        return vo;
    }

    /**
     * Get metadata for Split Failure Analysis
     *
     * @return {@code AnalysisMetadataVO}
     */
    public static AnalysisMetadataVO splitFailure() {
        return new AnalysisMetadataVO(AnalysisType.SPLIT_FAILURE.getId(),
                AnalysisType.SPLIT_FAILURE.getName(),
                true);
    }

    /**
     * Get metadata for Approach Delay Analysis
     *
     * @return {@code AnalysisMetadataVO}
     */
    public static AnalysisMetadataVO approachDelay() {
        AnalysisMetadataVO vo = new AnalysisMetadataVO(AnalysisType.APPROACH_DELAY.getId(),
                AnalysisType.APPROACH_DELAY.getName(),
                true);
        ExtraFieldVO voBinSize = ExtraFieldVO.binSize();
        voBinSize.getValueList().add(new ExtraFieldValueVO(ExtraFieldVO.BIN_SIZE_1800, ExtraFieldVO.BIN_SIZE_1800_DISPLAY_KEY));
        voBinSize.getValueList().add(new ExtraFieldValueVO(ExtraFieldVO.BIN_SIZE_3600, ExtraFieldVO.BIN_SIZE_3600_DISPLAY_KEY));
        vo.extraFieldList.add(voBinSize);
        return vo;

    }

    /**
     * Get metadata for Pedestrian Analysis
     *
     * @return {@code AnalysisMetadataVO}
     */
    public static AnalysisMetadataVO pedestrian() {
        return new AnalysisMetadataVO(AnalysisType.PEDESTRIAN.getId(),
                AnalysisType.PEDESTRIAN.getName(),
                true);
    }

    public static AnalysisMetadataVO queueLength() {
        return new AnalysisMetadataVO(AnalysisType.QUEUE_LENGTH.getId(),
                AnalysisType.QUEUE_LENGTH.getName(), true);
    }

    public static AnalysisMetadataVO pp() {
        return new AnalysisMetadataVO(AnalysisType.PREEMPTION_PRIORITY.getId(),
                AnalysisType.PREEMPTION_PRIORITY.getName(), false);
    }

    public static AnalysisMetadataVO turningMovement() {
        AnalysisMetadataVO vo = new AnalysisMetadataVO(AnalysisType.TURNING_MOVEMENT.getId(),
                AnalysisType.TURNING_MOVEMENT.getName(),
                false);
        vo.extraFieldList.add(ExtraFieldVO.binSize());

        return vo;
    }

    public static AnalysisMetadataVO splitMonitor() {
        return new AnalysisMetadataVO(AnalysisType.SPLIT_MONITOR.getId(),
                AnalysisType.SPLIT_MONITOR.getName(),
                false);
    }

    public static AnalysisMetadataVO redLightViolation() {
        AnalysisMetadataVO vo = new AnalysisMetadataVO(AnalysisType.RED_LIGHT_VIOLATION.getId(),
                AnalysisType.RED_LIGHT_VIOLATION.getName(),
                false);
        vo.extraFieldList.add(ExtraFieldVO.binSize());

        return vo;
    }


    public static AnalysisMetadataVO abnormalData() {
        AnalysisMetadataVO vo = new AnalysisMetadataVO(AnalysisType.ABNORMAL_DATA.getId(),
                AnalysisType.ABNORMAL_DATA.getName(),
                false);
        vo.extraFieldList.add(ExtraFieldVO.dayPeriods());
        return vo;
    }

    /**
     * Get metadata for Volume Analysis
     *
     * @return {@code AnalysisMetadataVO}
     */
    public static AnalysisMetadataVO volume() {
        return new AnalysisMetadataVO(AnalysisType.VOLUME.getId(),
                AnalysisType.VOLUME.getName(),
                true);
    }

    /**
     * Get metadata for Yellow Trap Occurrences Analysis
     *
     * @return {@code AnalysisMetadataVO}
     */
    public static AnalysisMetadataVO yellowTrapOccurrences() {
        return new AnalysisMetadataVO(AnalysisType.YELLOW_TRAP_OCCURRENCES.getId(),
                AnalysisType.YELLOW_TRAP_OCCURRENCES.getName(),
                true);
    }

    public static AnalysisMetadataVO moeAnalysis() {
        return new AnalysisMetadataVO(AnalysisType.MOE_ANALYSIS.getId(),
                AnalysisType.MOE_ANALYSIS.getName(),
                false);
    }

    /**
     * Analysis type ID. Same as Analysis type.
     */
    @Getter
    @Setter
    @JsonProperty("id")
    protected String id;

    /**
     * Key for translating Analysis name
     */
    @Getter
    @JsonIgnore
    protected String nameKey;

    @Getter
    @Setter
    @JsonProperty("name")
    protected String name;

    @Getter
    @Setter
    @JsonProperty("is_phase_analysis")
    protected boolean isPhaseAnalysis;

    @Getter
    @Setter
    @JsonProperty("extra_fields")
    protected ArrayList<ExtraFieldVO> extraFieldList;

    public AnalysisMetadataVO(String id, String nameKey, boolean isPhaseAnalysis) {
        super();
        this.id = id;
        setNameKey(nameKey);
        this.isPhaseAnalysis = isPhaseAnalysis;
        this.extraFieldList = new ArrayList<>();
    }

    /**
     * Will reset name until translator is accepted
     *
     * @param nameKey
     */
    public void setNameKey(String nameKey) {
        this.nameKey = nameKey;
        this.name = nameKey;
    }

    @Override
    public void acceptTranslator(MessageService translator) {
        if (translator == null)
            throw new IllegalArgumentException();

        name = translator.getMessage(nameKey);

        for (ExtraFieldVO field : extraFieldList) {
            field.acceptTranslator(translator);
        }
    }

}
