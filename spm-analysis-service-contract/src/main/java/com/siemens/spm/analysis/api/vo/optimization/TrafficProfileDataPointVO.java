package com.siemens.spm.analysis.api.vo.optimization;

import java.io.Serializable;
import java.time.LocalTime;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TrafficProfileDataPointVO implements Serializable {

    private static final long serialVersionUID = -7503256258138253008L;

    @JsonProperty("from_time")
    private LocalTime fromTime;

    @JsonProperty("to_time")
    private LocalTime toTime;

    @JsonProperty("group1")
    private Double group1;

    @JsonProperty("group2")
    private Double group2;

}
