package com.siemens.spm.analysis.api.vo.summaryreport;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.analysis.api.vo.DateRange;
import com.siemens.spm.analysis.api.vo.constraint.ValidDateRange;
import com.siemens.spm.analysis.api.vo.constraint.ValidRoundedHour;
import com.siemens.spm.analysis.api.vo.enums.TemplateAggregation;
import com.siemens.spm.common.constant.TimeConstants;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.time.DayOfWeek;
import java.time.LocalTime;
import java.util.Set;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class SummaryTemplateSimpleVO implements Serializable {

    private static final long serialVersionUID = -4115681197393183208L;

    @NotEmpty(message = "templates.name_not_blank")
    @JsonProperty("name")
    protected String name;

    @JsonProperty("description")
    protected String description;

    @JsonProperty("agency_id")
    protected Integer agencyId;

    @NotNull(message = "templates.week_days_not_null")
    @JsonProperty("week_days")
    private Set<DayOfWeek> weekDays;

    @JsonProperty("time_zone")
    protected String timeZone;

    @Pattern(regexp = TimeConstants.REGEXP_FOR_ZONE_ID, message = "time_zone_invalid")
    @JsonProperty("timezone_id")
    private String timezoneId;

    @NotNull(message = "start_time_not_null")
    @ValidRoundedHour(message = "time_not_valid_format")
    @JsonProperty("start_time")
    protected LocalTime startTime;

    @NotNull(message = "end_time_not_null")
    @ValidRoundedHour(message = "time_not_valid_format")
    @JsonProperty("end_time")
    protected LocalTime endTime;

    @NotNull(message = "summary_report.templates.aggregation_not_null")
    @JsonProperty("aggregation")
    protected TemplateAggregation aggregation;

    @Valid
    @ValidDateRange(message = "templates.date_range.invalid")
    @JsonProperty("date_range")
    protected DateRange dateRange;

}
