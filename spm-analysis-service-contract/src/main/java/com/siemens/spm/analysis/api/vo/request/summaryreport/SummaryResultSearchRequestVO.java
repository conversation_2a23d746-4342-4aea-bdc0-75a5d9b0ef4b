package com.siemens.spm.analysis.api.vo.request.summaryreport;

import java.io.Serializable;
import java.sql.Timestamp;
import java.time.DayOfWeek;
import java.util.Set;

import jakarta.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.analysis.api.vo.enums.TemplateAggregation;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SummaryResultSearchRequestVO implements Serializable {

    private static final long serialVersionUID = -6639000687362234780L;

    @NotNull
    @JsonProperty("agency_id")
    private Integer agencyId;

    @JsonProperty("text")
    private String text;

    @JsonProperty("from_date")
    private Timestamp fromDate;

    @JsonProperty("to_date")
    private Timestamp toDate;

    @JsonProperty("from_time")
    private Timestamp fromTime;

    @JsonProperty("to_time")
    private Timestamp toTime;

    @JsonProperty("week_days")
    private Set<DayOfWeek> weekDays;

    @JsonProperty("aggregation")
    private TemplateAggregation aggregation;

    @JsonProperty("owner_id")
    private Long ownerId;

    @JsonProperty("order_by_columns")
    private String[] orderByColumns;

    @JsonProperty("page")
    private Integer page;

    @JsonProperty("size")
    private Integer size;

}

