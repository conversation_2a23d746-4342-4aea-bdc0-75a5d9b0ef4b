package com.siemens.spm.analysis.api.vo.response.basepaging;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class PageResponse<T> implements Serializable {

    @JsonProperty("total_count")
    private Long total;

    @JsonProperty("result")
    private transient List<T> result;

}
