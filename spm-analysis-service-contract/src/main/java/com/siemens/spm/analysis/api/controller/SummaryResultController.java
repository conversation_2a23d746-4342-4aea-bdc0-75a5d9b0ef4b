// package com.siemens.spm.analysis.api.controller;

// import java.time.DayOfWeek;
// import java.util.List;

// import jakarta.validation.Valid;
// import jakarta.validation.constraints.NotEmpty;
// import jakarta.validation.constraints.NotNull;

// import org.springframework.http.HttpStatus;
// import org.springframework.http.ResponseEntity;
// import org.springframework.web.bind.annotation.DeleteMapping;
// import org.springframework.web.bind.annotation.GetMapping;
// import org.springframework.web.bind.annotation.PathVariable;
// import org.springframework.web.bind.annotation.RequestMapping;
// import org.springframework.web.bind.annotation.RequestParam;
// import org.springframework.web.bind.annotation.ResponseStatus;

// import com.siemens.spm.analysis.api.vo.enums.TemplateAggregation;
// import com.siemens.spm.analysis.api.vo.response.summaryreport.SummaryReportDetailResultObject;
// import com.siemens.spm.analysis.api.vo.response.summaryreport.SummaryReportResultObject;
// import com.siemens.spm.analysis.api.vo.response.summaryreport.SummaryResultManipulateResultObject;
// import com.siemens.spm.common.api.PublicController;
// import io.swagger.v3.oas.annotations.Operation;
// import io.swagger.v3.oas.annotations.Parameter;
// import io.swagger.v3.oas.annotations.enums.ParameterIn;
// import io.swagger.v3.oas.annotations.responses.ApiResponse;
// import io.swagger.v3.oas.annotations.responses.ApiResponses;
// import io.swagger.v3.oas.annotations.tags.Tag;

// @Tag(name = "summary-report", description = "Summary Report Result Resources")
// @RequestMapping(SummaryResultController.API_ROOT)
// public interface SummaryResultController extends PublicController {

//     String VERSION = "/v1";
//     String SUMMARY_REPORT_RESOURCES = "/summary-report";
//     String SUMMARY_REPORT_RESULT_RESOURCES = SUMMARY_REPORT_RESOURCES + "/results";
//     String API_ROOT = PUBLIC_API + VERSION + AGENCY_RESOURCE + AGENCY_ID_RESOURCE + SUMMARY_REPORT_RESULT_RESOURCES;

//     /**
//      * Retrieve all summary report results
//      *
//      * @return {@code ResponseEntity<SummaryReportResultObject>}
//      */
//     @Operation(summary = "Retrieve all summary report results")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "200", description = "Successful"),
//             @ApiResponse(responseCode = "404", description = "Not Found")
//     })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true,
//             in = ParameterIn.HEADER, description = "Bearer Token type")
//     @ResponseStatus(HttpStatus.OK)
//     @GetMapping
//     ResponseEntity<SummaryReportResultObject> searchSummaryReportInfos(
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(description = "Text to search")
//             @RequestParam(value = "text", required = false)
//             String text,

//             @Parameter(description = "This day must be lower than created_at")
//             @RequestParam(value = "from_date", required = false)
//             Long fromDate,

//             @Parameter(description = "This day must be greater than created_at")
//             @RequestParam(value = "to_date", required = false)
//             Long toDate,

//             @Parameter(description = "This time must be lower or equal than from_time_utc")
//             @RequestParam(value = "from_time", required = false)
//             Long fromTime,

//             @Parameter(description = "This time must be greater or equal than to_time_utc")
//             @RequestParam(value = "to_time", required = false)
//             Long toTime,

//             @Parameter(description = "Day of the week must be between MONDAY -> SUNDAY")
//             @RequestParam(value = "week_days", required = false)
//             DayOfWeek[] weekDays,

//             @Parameter(description = "Aggregation unit of report")
//             @RequestParam(value = "aggregation", required = false)
//             TemplateAggregation aggregation,

//             @Parameter(description = "Id of an owner that the report belong to")
//             @RequestParam(value = "owner_id", required = false)
//             Long ownerId,

//             @Parameter(description = "Fields will be used to sort returning data")
//             @RequestParam(value = "sort", required = false)
//             String[] orderByColumns,

//             @Parameter(description = "Page is used to page the returning result")
//             @RequestParam(value = "page", required = false)
//             Integer page,

//             @Parameter(description = "Size is used to size the returning result")
//             @RequestParam(value = "size", required = false)
//             Integer size
//     );

//     /**
//      * Retrieve the summary report details
//      *
//      * @return {@code ResponseEntity<SummaryReportDetailResultObject>}
//      */
//     @Operation(summary = "Retrieve the summary report details")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "200", description = "success"),
//             @ApiResponse(responseCode = "404", description = "Not Found")
//     })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true,
//             in = ParameterIn.HEADER, description = "Bearer Token type")
//     @ResponseStatus(HttpStatus.OK)
//     @GetMapping("/{id}")
//     ResponseEntity<SummaryReportDetailResultObject> getSummaryReportDetail(
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(description = "The id of summary report result")
//             @Valid @NotNull(message = "summary_report.results.id_not_null")
//             @PathVariable("id") Long reportId);

//     /**
//      * Delete multiple report result by list of result ids This method is SOFT delete
//      *
//      * @param resultIds {@code List<Long>} List of result ids need to delete
//      * @return {@code ReportResultManipulateResultObject} object
//      */
//     @Operation(summary = "Delete multiple report results")
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true,
//             in = ParameterIn.HEADER, description = "Bearer Token type")
//     @ResponseStatus(HttpStatus.NO_CONTENT)
//     @DeleteMapping
//     ResponseEntity<SummaryResultManipulateResultObject> deleteReportResults(
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(description = "List of id of report results will be delete")
//             @Valid @NotEmpty(message = "summary_report.results.result_ids_not_empty")
//             @RequestParam(value = "result_ids")
//             List<Long> resultIds);

// }
