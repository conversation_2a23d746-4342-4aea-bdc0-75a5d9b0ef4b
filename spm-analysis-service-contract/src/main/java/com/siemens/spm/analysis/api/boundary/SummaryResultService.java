package com.siemens.spm.analysis.api.boundary;

import com.siemens.spm.analysis.api.vo.request.summaryreport.SummaryResultDeleteRequestVO;
import com.siemens.spm.analysis.api.vo.request.summaryreport.SummaryResultSearchRequestVO;
import com.siemens.spm.analysis.api.vo.response.summaryreport.SummaryReportDetailResultObject;
import com.siemens.spm.analysis.api.vo.response.summaryreport.SummaryReportResultObject;
import com.siemens.spm.analysis.api.vo.response.summaryreport.SummaryResultManipulateResultObject;

public interface SummaryResultService {

    /**
     * Retrieve all summary report results
     *
     * @return {@link SummaryReportResultObject}
     */
    SummaryReportResultObject searchSummaryReportResults(SummaryResultSearchRequestVO searchRequestVO);

    /**
     * Retrieve the summary report details
     *
     * @return {@link SummaryReportDetailResultObject}
     */
    SummaryReportDetailResultObject getSummaryReportDetail(Long reportId);

    /**
     * Delete multiple summary report results
     *
     * @param deleteRequestVO {@link SummaryResultDeleteRequestVO} list of results ids
     * @return {@link SummaryResultManipulateResultObject}
     */
    SummaryResultManipulateResultObject deleteReportResults(SummaryResultDeleteRequestVO deleteRequestVO);

}
