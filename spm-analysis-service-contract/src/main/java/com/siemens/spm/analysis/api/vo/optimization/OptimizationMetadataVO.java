package com.siemens.spm.analysis.api.vo.optimization;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.analysis.api.vo.BinSizeVO;
import com.siemens.spm.analysis.api.vo.PhaseNumberVO;

import com.siemens.spm.analysis.api.vo.enums.ClusterType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OptimizationMetadataVO implements Serializable {
    /**
     *
     */
    private static final long serialVersionUID = 1L;

    @JsonProperty("types")
    private List<OptimizationTypeVO> types;

    @JsonProperty("phase_numbers")
    private List<PhaseNumberVO> phases;

    /**
     * List of bin Size option in second
     */
    @JsonProperty("bin_sizes")
    private List<BinSizeVO> binSizes;

    @JsonProperty("cluster_types")
    private List<ClusterType> clusterTypes;

}
