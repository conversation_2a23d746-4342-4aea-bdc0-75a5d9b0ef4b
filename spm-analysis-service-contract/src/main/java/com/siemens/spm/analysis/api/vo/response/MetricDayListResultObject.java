/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : MetricDayListResultObject.java
 * Project     : SPM Platform
 */

package com.siemens.spm.analysis.api.vo.response;

import org.springframework.http.HttpStatus;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.siemens.spm.analysis.api.vo.MetricDayListVO;
import com.siemens.spm.analysis.api.vo.response.MetricDayListResultObject.StatusCode;
import com.siemens.spm.common.shared.vo.AbstractResultObject;

import lombok.AllArgsConstructor;
import lombok.Getter;

@JsonInclude(Include.NON_NULL)
@JsonIgnoreProperties({ "statusCode", "message", "httpStatus" })
public class MetricDayListResultObject extends AbstractResultObject<MetricDayListVO, StatusCode> {

    private static final long serialVersionUID = 5713718549010544650L;

    private StatusCode statusCode;

    private MetricDayListVO data;

    /**
     * Create result object with SUCCESS status and data
     *
     * @param data
     */
    public MetricDayListResultObject(MetricDayListVO data) {
        this.data = data;
        this.statusCode = getSuccessfulStatusValue();
    }

    /**
     * Create result object with null data and an error code
     *
     * @param statusCode Indicating error
     */
    public MetricDayListResultObject(StatusCode statusCode) {
        this.statusCode = statusCode;
    }

    @Override
    public HttpStatus getHttpStatus() {
        return statusCode.getHttpStatus();
    }

    @Override
    public MetricDayListVO getData() {
        return data;
    }

    @Override
    protected void setData(MetricDayListVO data) {
        this.data = data;
    }

    @Override
    public StatusCode getStatusCode() {
        return statusCode;
    }

    @Override
    protected void setStatusCode(StatusCode value) {
        this.statusCode = value;
    }

    @Override
    public String getMessage() {
        return statusCode.getMessage();
    }

    @Override
    public String getErrorFieldName() {
        return this.statusCode.getErrorField();
    }

    @Getter
    @AllArgsConstructor
    public enum StatusCode {
        /** OK */
        SUCCESS("success", HttpStatus.OK),

        INTERSECTION_NOT_AVAILABLE("int_uuid", "intersection_is_not_available", HttpStatus.BAD_REQUEST),
        INVALID_TIME_RANGE("invalid_time_range", HttpStatus.BAD_REQUEST),
        INVALID_ANALYSIS_ID("analysis_id", "invalid_analysis_id", HttpStatus.BAD_REQUEST),

        /* PerfLog Errors */
        NO_PERFLOG_DATA("no_perflog_data", HttpStatus.NOT_FOUND),
        INVALID_PERFLOG_DATA("invalid_perflog_data", HttpStatus.NOT_FOUND),

        /** Internal Server Error */
        UNKNOWN_ERROR("unknown_error", HttpStatus.INTERNAL_SERVER_ERROR);

        private final String errorField;
        private final String message;
        private final HttpStatus httpStatus;

        StatusCode(String message, HttpStatus httpStatus) {
            this(null, message, httpStatus);
        }
    }

    @Override
    protected StatusCode getErrorStatusValue() {
        return StatusCode.UNKNOWN_ERROR;
    }

    @Override
    public StatusCode getSuccessfulStatusValue() {
        return StatusCode.SUCCESS;
    }

}
