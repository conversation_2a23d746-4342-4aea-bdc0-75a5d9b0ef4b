package com.siemens.spm.analysis.api.vo.response.performancemetric;

import org.springframework.http.HttpStatus;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.siemens.spm.analysis.api.vo.performancemetric.PMTemplateDetailVO;
import com.siemens.spm.common.shared.vo.AbstractResultObject;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@RequiredArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PMTemplateDetailResultObject
        extends AbstractResultObject<PMTemplateDetailVO, PMTemplateDetailResultObject.StatusCode> {

    private static final long serialVersionUID = -2600483124854410190L;

    private static final String INTERSECTION_FILED = "intersection";

    private PMTemplateDetailVO data;

    @NonNull
    private StatusCode statusCode;

    @Override
    public String getMessage() {
        return statusCode.getMessage();
    }

    @Override
    public HttpStatus getHttpStatus() {
        return statusCode.getHttpStatus();
    }

    @Override
    public String getErrorFieldName() {
        return statusCode.getErrorField();
    }

    @Override
    protected StatusCode getErrorStatusValue() {
        return StatusCode.ERROR;
    }

    @Override
    public StatusCode getSuccessfulStatusValue() {
        return StatusCode.SUCCESS;
    }

    @Getter
    @AllArgsConstructor
    public enum StatusCode {
        /**
         * OK
         */
        SUCCESS("success", HttpStatus.OK),
        CREATED("created", HttpStatus.CREATED),

        // Bad request
        INVALID_METRIC_TYPE("metric_id", "performance_metric.templates.invalid_metric_type", HttpStatus.BAD_REQUEST),
        INVALID_DATE_RANGE("date_range", "performance_metric.templates.invalid_date_range", HttpStatus.BAD_REQUEST),
        INVALID_ABNORMAL_DATA_FILTER("abnormal_data_filter",
                "performance_metric.templates.invalid_abnormal_data_filter", HttpStatus.BAD_REQUEST),
        // below status codes map 1-1 with status code of
        // IntersectionsInAgencyVerifyResultObject
        MISSING_AGENCY_ID("agency_id", "agency_id_is_missing", HttpStatus.BAD_REQUEST),
        AGENCY_NOT_FOUND("agency_id", "agency_not_found", HttpStatus.BAD_REQUEST),
        INTERSECTION_NOT_FOUND(INTERSECTION_FILED, "intersection_not_found", HttpStatus.BAD_REQUEST),
        INTERSECTION_INVALID(INTERSECTION_FILED, "intersection_in_valid", HttpStatus.BAD_REQUEST),
        INTERSECTION_NOT_UNDER_AGENCY_MANAGEMENT(INTERSECTION_FILED, "intersection_not_under_agency_management",
                HttpStatus.BAD_REQUEST),
        SCHEDULE_IS_INVALID("schedule", "templates.schedule.invalid", HttpStatus.BAD_REQUEST),

        UNAUTHORIZED("unauthorized", HttpStatus.UNAUTHORIZED),

        /**
         * Internal Server Error
         */
        ERROR("unknown_exception", HttpStatus.INTERNAL_SERVER_ERROR);

        private final String errorField;
        private final String message;
        private final HttpStatus httpStatus;

        StatusCode(String message, HttpStatus httpStatus) {
            this(null, message, httpStatus);
        }
    }

    public static PMTemplateDetailResultObject success(PMTemplateDetailVO data) {
        return new PMTemplateDetailResultObject(data, StatusCode.SUCCESS);
    }

    public static PMTemplateDetailResultObject error(StatusCode statusCode) {
        if (statusCode == null) {
            throw new IllegalArgumentException();
        }

        return new PMTemplateDetailResultObject(null, statusCode);
    }

}
