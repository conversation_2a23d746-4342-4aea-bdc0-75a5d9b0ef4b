package com.siemens.spm.analysis.api.vo.optimization;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CorridorOffsetOptimizationResultVO extends BaseOptimizationResponseVO {

    private static final long serialVersionUID = 5072935737529500657L;

    @JsonProperty("result")
    private CorridorOffsetOptimizationVO resultVO;

}
