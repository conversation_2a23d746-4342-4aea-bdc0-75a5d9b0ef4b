package com.siemens.spm.analysis.api.vo.request.detectorreport;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.analysis.api.vo.enums.TemplateAggregation;
import com.siemens.spm.analysis.api.vo.enums.TemplateStatus;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.sql.Timestamp;
import java.time.DayOfWeek;
import java.util.Set;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DetectorTemplateSearchRequestVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1491426828100969312L;

    @NotNull
    @JsonProperty("agency_id")
    private Integer agencyId;

    @JsonProperty("created_at_from")
    private Timestamp createdAtFrom;

    @JsonProperty("created_at_to")
    private Timestamp createdAtTo;

    @JsonProperty("week_days")
    private Set<DayOfWeek> weekDays;

    @JsonProperty("aggregation")
    private TemplateAggregation aggregation;

    @JsonProperty("order_by_columns")
    private String[] orderByColumns;

    @JsonProperty("owner_id")
    private Long ownerId;

    @JsonProperty("page")
    private Integer page;

    @JsonProperty("size")
    private Integer size;

    @JsonProperty("status")
    private TemplateStatus status;

    @JsonProperty("text")
    private String text;
}
