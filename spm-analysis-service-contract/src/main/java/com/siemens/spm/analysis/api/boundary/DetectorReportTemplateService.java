package com.siemens.spm.analysis.api.boundary;

import java.util.List;

import com.siemens.spm.analysis.api.vo.detectorreport.DetectorTemplateScheduleVO;
import com.siemens.spm.analysis.api.vo.request.IntersectionIdsRequestVO;
import com.siemens.spm.analysis.api.vo.request.TemplateIntersectionSearchRequestVO;
import com.siemens.spm.analysis.api.vo.request.detectorreport.DetectorTemplateActivateRequestVO;
import com.siemens.spm.analysis.api.vo.request.detectorreport.DetectorTemplateCreateRequestVO;
import com.siemens.spm.analysis.api.vo.request.detectorreport.DetectorTemplateSearchRequestVO;
import com.siemens.spm.analysis.api.vo.request.detectorreport.DetectorTemplateUpdateCoreDataRequestVO;
import com.siemens.spm.analysis.api.vo.response.IntersectionSearchResultObject;
import com.siemens.spm.analysis.api.vo.response.detectorreport.DetectorMetricResultObject;
import com.siemens.spm.analysis.api.vo.response.detectorreport.DetectorScheduleResultObject;
import com.siemens.spm.analysis.api.vo.response.detectorreport.DetectorTemplateCoreDataResultObject;
import com.siemens.spm.analysis.api.vo.response.detectorreport.DetectorTemplateDetailResultObject;
import com.siemens.spm.analysis.api.vo.response.detectorreport.DetectorTemplateManipulateResultObject;
import com.siemens.spm.analysis.api.vo.response.detectorreport.DetectorTemplateProcessResultObject;
import com.siemens.spm.analysis.api.vo.response.detectorreport.DetectorTemplateSearchResultObject;
import com.siemens.spm.common.shared.vo.SimpleResultObject;

public interface DetectorReportTemplateService {

    /**
     * Create detector report template from request
     * @param requestVO {@link DetectorTemplateCreateRequestVO}
     * @return {@link DetectorTemplateDetailResultObject}
     */
    DetectorTemplateDetailResultObject createReportTemplate(DetectorTemplateCreateRequestVO requestVO);

    DetectorTemplateCoreDataResultObject getTemplateCoreData(Long templateId);

    /**
     * Get All metrics of detector report
     * @return {@link DetectorMetricResultObject}
     */
    DetectorMetricResultObject getAllMetrics();

    /**
     * Get template schedule data by id
     * @param templateId
     * @return {@link DetectorScheduleResultObject}
     */
    DetectorScheduleResultObject getTemplateSchedule(Long templateId);

    /**
     * Search all available intersections to add in detector report template
     * @param searchRequestVO {@link TemplateIntersectionSearchRequestVO}
     * @return {@link IntersectionSearchResultObject}
     */
    IntersectionSearchResultObject searchAvailableTemplateIntersections(TemplateIntersectionSearchRequestVO searchRequestVO);

    /**
     * update core data of detector template
     * @param templateId
     * @param updateRequestVO {@link DetectorTemplateUpdateCoreDataRequestVO}
     * @return {@link DetectorTemplateManipulateResultObject}
     */
    DetectorTemplateManipulateResultObject updateCoreData(Long templateId, DetectorTemplateUpdateCoreDataRequestVO updateRequestVO);

    /**
     * Update schedule data of a detector template by id
     * @param templateId
     * @param templateScheduleVO {@link DetectorTemplateScheduleVO}
     * @return {@link DetectorTemplateManipulateResultObject}
     */
    DetectorTemplateManipulateResultObject updateTemplateScheduleData(Long templateId, DetectorTemplateScheduleVO templateScheduleVO);

    /**
     * Soft delete a list of templates
     * @param templateIds - list of template id
     * @return {@link DetectorTemplateManipulateResultObject}
     */
    DetectorTemplateManipulateResultObject softDeletes(List<Long> templateIds);

    /**
     * Update intersection(s) of a detector report template(delete or add depend on action field)
     *
     * @param templateId               id of template need to update intersection(s)
     * @param intersectionIdsRequestVO {@link IntersectionIdsRequestVO} object request
     * @return {@link DetectorTemplateManipulateResultObject}
     */
    DetectorTemplateManipulateResultObject updateIntersections(Long templateId, IntersectionIdsRequestVO intersectionIdsRequestVO);

    /**
     * Activate or deactivate a list of templates
     * @param activeRequestVO {@link DetectorTemplateActivateRequestVO}
     * @return {@link DetectorTemplateManipulateResultObject}
     */
    DetectorTemplateManipulateResultObject activateTemplates(DetectorTemplateActivateRequestVO activeRequestVO);

    /**
     * Search intersection(s) in a detector report template.
     *
     * @param searchRequestVO {@link TemplateIntersectionSearchRequestVO} request object
     * @return {@link IntersectionSearchResultObject}
     */
    IntersectionSearchResultObject searchTemplateIntersections(TemplateIntersectionSearchRequestVO searchRequestVO);

    /**
     * Search all detector template(s) be filter in {@link DetectorTemplateSearchRequestVO} object
     *
     * @param requestVO {@link DetectorTemplateSearchRequestVO} object to filter
     * @return {@link DetectorTemplateSearchResultObject}
     */
    DetectorTemplateSearchResultObject searchTemplates(DetectorTemplateSearchRequestVO requestVO);

    /**
     * Run manually a detector template
     *
     * @param templateId
     * @return {@link DetectorTemplateProcessResultObject}
     */
    DetectorTemplateProcessResultObject runTemplate(Long templateId);

    /**
     * Scanning the report template
     *
     * @return
     */
    SimpleResultObject scanReportTemplate();
}
