package com.siemens.spm.analysis.api.vo;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.common.message.MessageService;
import com.siemens.spm.common.shared.domaintype.report.ReportType;
import com.siemens.spm.common.shared.resource.TextKey;
import com.siemens.spm.common.shared.vo.TranslatableVO;
import lombok.Getter;
import lombok.Setter;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ReportMetadataVO implements Serializable, TranslatableVO {

    public static ReportMetadataVO summaryReport() {
        return new ReportMetadataVO(ReportType.SUMMARY_REPORT.getId(), TextKey.SUMMARY_REPORT);
    }

    public static ReportMetadataVO performanceMetric() {
        return new ReportMetadataVO(ReportType.PERFORMANCE_METRIC.getId(), TextKey.PERFORMANCE_METRIC);
    }

    public static ReportMetadataVO detectorReport() {
        return new ReportMetadataVO(ReportType.DETECTOR_REPORT.getId(), TextKey.DETECTOR_REPORT);
    }

    /**
     * Report type ID. Same as Report type.
     */
    @Getter
    @Setter
    @JsonProperty("id")
    protected String id;

    /**
     * Key for translating Report name
     */
    @Getter
    @JsonIgnore
    protected String nameKey;

    @Getter
    @Setter
    @JsonProperty("name")
    protected String name;

    public ReportMetadataVO(String id, String nameKey) {
        super();
        this.id = id;
        setNameKey(nameKey);
    }


    /**
     * Will reset name until translator is accepted
     *
     * @param nameKey
     */
    public void setNameKey(String nameKey) {
        this.nameKey = nameKey;
        this.name = nameKey;
    }

    @Override
    public void acceptTranslator(MessageService translator) {
        if (translator == null)
            throw new IllegalArgumentException();

        name = translator.getMessage(nameKey);
    }
}
