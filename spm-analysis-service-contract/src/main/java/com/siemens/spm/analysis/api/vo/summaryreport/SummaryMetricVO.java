package com.siemens.spm.analysis.api.vo.summaryreport;

import java.io.Serializable;

import org.springframework.util.StringUtils;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.analysis.api.vo.enums.TrendingCondition;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
public class SummaryMetricVO implements Serializable {

    private static final long serialVersionUID = 4004289871714044976L;

    public SummaryMetricVO(String keyName) {
        this.keyName = keyName;
    }

    @JsonProperty("key_name")
    private String keyName;

    @JsonProperty("good_condition")
    private String goodCondition;

    @JsonProperty("bad_condition")
    private String badCondition;

    @JsonProperty("enabled")
    private Boolean enabled;

    @JsonIgnore
    public boolean isValid() {
        if (!StringUtils.hasText(this.keyName) || this.goodCondition == null || this.badCondition == null) {
            return false;
        }

        TrendingCondition goodConditionObj = TrendingCondition.resolve(this.goodCondition);
        TrendingCondition badConditionObj = TrendingCondition.resolve(this.badCondition);

        if (goodConditionObj == TrendingCondition.INCREASE && badConditionObj == TrendingCondition.DECREASE) {
            return true;
        } else if (goodConditionObj == TrendingCondition.DECREASE && badConditionObj == TrendingCondition.INCREASE) {
            return true;
        } else {
            return goodConditionObj == TrendingCondition.NONE && badConditionObj == TrendingCondition.NONE;
        }
    }

}
