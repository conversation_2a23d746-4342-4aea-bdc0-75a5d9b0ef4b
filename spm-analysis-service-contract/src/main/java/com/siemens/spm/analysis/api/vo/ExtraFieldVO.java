/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : ExtraFieldVO.java
 * Project     : SPM Platform
 */
package com.siemens.spm.analysis.api.vo;

import java.io.Serializable;
import java.util.ArrayList;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.common.message.MessageService;
import com.siemens.spm.common.shared.vo.TranslatableVO;
import lombok.Getter;
import lombok.Setter;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ExtraFieldVO implements Serializable, TranslatableVO {

    private static final long serialVersionUID = -2945030754291721854L;

    public static final String LIST_TYPE = "list";
    public static final String NUMBER_TYPE = "number";

    public static final String BIN_SIZE_ID = "bin_size";
    public static final String DAY_PERIOD_ID = "day_period";
    public static final String BIN_SIZE_LABEL_KEY = "label.bin_size";
    public static final String DAY_PERIOD_LABEL_KEY = "label.day_period";

    public static final String BIN_SIZE_0 = "0";
    
    /**
     * 5 minutes
     */
    public static final String BIN_SIZE_300 = "300";

    /**
     * 15 minutes
     */
    public static final String BIN_SIZE_900 = "900";

    /**
     * 30 minutes   
     */
    public static final String BIN_SIZE_1800 = "1800";

    /**
     * 1 hour
     */
    public static final String BIN_SIZE_3600 = "3600";

    public static final String BIN_SIZE_0_DISPLAY_KEY = "display.bin_size_0";
    public static final String BIN_SIZE_300_DISPLAY_KEY = "display.bin_size_300";
    public static final String BIN_SIZE_900_DISPLAY_KEY = "display.bin_size_900";
    public static final String BIN_SIZE_1800_DISPLAY_KEY = "display.bin_size_1800";
    public static final String BIN_SIZE_3600_DISPLAY_KEY = "display.bin_size_3600";

    public static ExtraFieldVO binSize() {
        ExtraFieldVO vo = new ExtraFieldVO(BIN_SIZE_ID, LIST_TYPE, BIN_SIZE_LABEL_KEY);

        ExtraFieldValueVO value300 = new ExtraFieldValueVO(BIN_SIZE_300, BIN_SIZE_300_DISPLAY_KEY);
        ExtraFieldValueVO value900 = new ExtraFieldValueVO(BIN_SIZE_900, BIN_SIZE_900_DISPLAY_KEY);

        vo.setDefaultValue(value900);

        vo.getValueList().add(value300);
        vo.getValueList().add(value900);

        return vo;
    }

    public static ExtraFieldVO dayPeriods() {
        return new ExtraFieldVO(DAY_PERIOD_ID, NUMBER_TYPE, DAY_PERIOD_LABEL_KEY);
    }

    @Getter
    @Setter
    @JsonProperty("id")
    private String id;

    @Getter
    @Setter
    @JsonProperty("type")
    private String type;

    @Getter
    @JsonIgnore
    private String labelKey;

    @Getter
    @Setter
    @JsonProperty("label")
    private String label;

    @Getter
    @JsonIgnore
    private ExtraFieldValueVO defaultValue;

    @Getter
    @Setter
    @JsonProperty("default_value")
    private String defaultValueDisplay;

    @Getter
    @Setter
    @JsonProperty("values")
    private ArrayList<ExtraFieldValueVO> valueList;

    public ExtraFieldVO(String id, String type, String labelKey) {
        super();
        this.id = id;
        this.type = type;
        setLabelKey(labelKey);
        this.valueList = new ArrayList<>();
    }

    /**
     * Will reset label until translator is accepted
     *
     * @param labelKey
     */
    public void setLabelKey(String labelKey) {
        this.labelKey = labelKey;
        this.label = labelKey;
    }

    /**
     * Will reset default value display until translator is accepted
     *
     * @param defaultValue
     */
    public void setDefaultValue(ExtraFieldValueVO defaultValue) {
        this.defaultValue = defaultValue;
        this.defaultValueDisplay = defaultValue != null ? defaultValue.getValue() : null;
    }

    @Override
    public void acceptTranslator(MessageService translator) {
        if (translator == null)
            throw new IllegalArgumentException();

        label = translator.getMessage(labelKey);

        if (defaultValue != null) {
            defaultValue.acceptTranslator(translator);
            defaultValueDisplay = defaultValue.getValue();
        }

        for (ExtraFieldValueVO value : valueList) {
            if (value == defaultValue)
                continue; // Avoid translating again
            value.acceptTranslator(translator);
        }
    }

}
