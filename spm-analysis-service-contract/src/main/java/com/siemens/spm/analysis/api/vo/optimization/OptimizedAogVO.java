package com.siemens.spm.analysis.api.vo.optimization;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OptimizedAogVO implements Serializable {

    /**
    * 
    */
    private static final long serialVersionUID = 1L;

    @JsonProperty("existing_aog")
    private long existingAog;

    @JsonProperty("predicted_aog")
    private long predictedAog;

    @JsonProperty("percent_existing_aog")
    private double percentExistingAog;

    @JsonProperty("percent_predicted_aog")
    private double percentPredictedgAog;
}
