package com.siemens.spm.analysis.api.vo.optimization;

import java.time.DayOfWeek;
import java.util.Set;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.analysis.domain.Phase;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CorridorOffsetOptimizationRequestVO extends OptimizationRequestVO {

    private static final long serialVersionUID = -7667332133561131552L;

    @NotNull(message = "corridor_id_not_null")
    @JsonProperty("corridor_id")
    private String corridorId;

    @JsonProperty("plan_number")
    @NotNull(message = "plan_number_required")
    @Min(value = 1, message = "plan_number_invalid")
    @Max(value = 253, message = "plan_number_invalid")
    private Integer planNumber;

    @NotNull
    @JsonProperty("cycle_length")
    private Integer cycleLength;

    @NotNull
    @JsonProperty("bias")
    private Integer bias;

    @JsonProperty("bias_direction")
    private String biasDirection;
    @NotEmpty
    @JsonProperty("week_days")
    private Set<DayOfWeek> weekDays;

    @JsonIgnore
    @Override
    public boolean isValidDateRange() {
        return startDate != null && endDate != null && !startDate.isAfter(endDate);
    }

    @JsonIgnore
    public boolean isValidBias() {
        if (bias == null) {
            return false;
        }
        if (bias == 0) {
            return true;
        }
        return bias > 0 && biasDirection != null
                && Set.of(Phase.Approach.UPSTREAM.name(), Phase.Approach.DOWNSTREAM.name()).contains(biasDirection);
    }

}
