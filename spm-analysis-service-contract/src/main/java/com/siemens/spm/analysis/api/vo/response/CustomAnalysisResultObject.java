package com.siemens.spm.analysis.api.vo.response;

import org.springframework.http.HttpStatus;

import com.siemens.spm.analysis.api.vo.CustomAnalysisVO;
import com.siemens.spm.common.shared.vo.AbstractResultObject;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@RequiredArgsConstructor
public class CustomAnalysisResultObject extends
        AbstractResultObject<CustomAnalysisVO, CustomAnalysisResultObject.StatusCode> {

    @NonNull
    private StatusCode statusCode;

    private CustomAnalysisVO data;

    @Override
    public CustomAnalysisVO getData() {
        return data;
    }

    @Override
    public StatusCode getStatusCode() {
        return statusCode;
    }

    @Override
    public String getMessage() {
        return statusCode.getMessage();
    }

    @Override
    public HttpStatus getHttpStatus() {
        return statusCode.getHttpStatus();
    }

    @Override
    public String getErrorFieldName() {
        return statusCode.getErrorField();
    }

    @Override
    protected void setData(CustomAnalysisVO data) {
        this.data = data;
    }

    @Override
    protected void setStatusCode(StatusCode statusCode) {
        this.statusCode = statusCode;
    }

    @Override
    protected StatusCode getErrorStatusValue() {
        return StatusCode.UNKNOWN_ERROR;
    }

    @Override
    public StatusCode getSuccessfulStatusValue() {
        return StatusCode.SUCCESS;
    }

    @Getter
    @AllArgsConstructor
    public enum StatusCode {
        /**
         * OK
         */
        SUCCESS("success", HttpStatus.OK),

        SUCCESS_AND_NO_CONTENT("success", HttpStatus.NO_CONTENT),

        NOT_FOUND("not_found", HttpStatus.NOT_FOUND),

        INVALID_TIME_RANGE("invalid_time_range", HttpStatus.BAD_REQUEST),

        INVALID_AGENCY("invalid_agency", HttpStatus.BAD_REQUEST),

        INVALID_EVENT("invalid_event", HttpStatus.BAD_REQUEST),

        UNAUTHORIZED("unauthorized", HttpStatus.UNAUTHORIZED),

        /**
         * Uncaught exception
         */
        UNKNOWN_ERROR("unknown_exception", HttpStatus.INTERNAL_SERVER_ERROR);

        private final String errorField;
        private final String message;
        private final HttpStatus httpStatus;

        StatusCode(String message, HttpStatus httpStatus) {
            this(null, message, httpStatus);
        }
    }
}
