package com.siemens.spm.analysis.api.vo.detectorreport;

import java.io.Serial;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.analysis.api.vo.constraint.ValidOperator;
import com.siemens.spm.analysis.domain.DetectorOperator;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DetectorMetricConditionVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 8755641085249678094L;

    private static final String CONDITION_MIN_VALUE = "0.0";

    @JsonProperty("operator")
    @ValidOperator
    private String operator;

    @JsonProperty("value")
    private Double value;

    public void setValue(Double value) {
        if (value == null) {
            this.value = 0D;
            return;
        }
        this.value = value;
    }

    public void setOperator(String operator) {
        if (operator == null) {
            this.operator = DetectorOperator.NONE.getValue();
            return;
        }
        this.operator = operator;
    }
}
