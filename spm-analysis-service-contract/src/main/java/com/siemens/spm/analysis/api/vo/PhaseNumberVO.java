package com.siemens.spm.analysis.api.vo;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PhaseNumberVO implements Serializable {

    /**
    * 
    */
    private static final long serialVersionUID = 1L;

    @JsonProperty("phase_number")
    private Integer phaseNumber;

    @JsonProperty("name")
    private String name;

}
