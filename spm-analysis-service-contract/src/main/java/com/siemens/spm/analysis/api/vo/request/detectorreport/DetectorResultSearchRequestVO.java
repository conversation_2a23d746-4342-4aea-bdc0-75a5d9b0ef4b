package com.siemens.spm.analysis.api.vo.request.detectorreport;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.analysis.api.vo.enums.TemplateAggregation;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DetectorResultSearchRequestVO implements Serializable {

    private static final long serialVersionUID = -579341006252543042L;

    @NotNull
    @JsonProperty("agency_id")
    private Integer agencyId;

    @JsonProperty("text")
    private String text;

    @JsonProperty("created_at_from")
    private Timestamp createdAtFrom;

    @JsonProperty("created_at_to")
    private Timestamp createdAtTo;

    @JsonProperty("owner_id")
    private Long ownerId;

    @JsonProperty("order_by_columns")
    private String[] orderByColumns;

    @JsonProperty("page")
    private Integer page;

    @JsonProperty("size")
    private Integer size;

    @JsonProperty("aggregation")
    private TemplateAggregation aggregation;
}
