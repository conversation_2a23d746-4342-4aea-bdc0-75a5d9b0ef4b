/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AgencyIdListRequestVO.java
 * Project     : SPM Platform
 */
package com.siemens.spm.analysis.api.vo.request;

import java.io.Serializable;
import java.util.List;

import jakarta.validation.constraints.NotEmpty;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_NULL)
public class IntersectionIdListRequestVO implements Serializable {

    private static final long serialVersionUID = -1145888581986936743L;

    @NotEmpty(message = "ids_not_empty")
    @JsonProperty("ids")
    private List<String> ids;

}
