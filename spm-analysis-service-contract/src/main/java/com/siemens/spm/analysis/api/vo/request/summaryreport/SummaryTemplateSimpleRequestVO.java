package com.siemens.spm.analysis.api.vo.request.summaryreport;

import java.io.Serializable;
import java.util.List;

import jakarta.validation.constraints.NotEmpty;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SummaryTemplateSimpleRequestVO implements Serializable {

    private static final long serialVersionUID = 716936422801192009L;

    @NotEmpty(message = "templates.template_ids_not_empty")
    @JsonProperty("template_ids")
    private List<Long> templateIds;

}
