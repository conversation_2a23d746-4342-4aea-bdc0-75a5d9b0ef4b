package com.siemens.spm.analysis.api.vo.response.summaryreport;

import org.springframework.http.HttpStatus;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.siemens.spm.analysis.api.vo.summaryreport.SummaryTemplateCoreDataVO;
import com.siemens.spm.analysis.api.vo.response.summaryreport.SummaryTemplateCoreDataResultObject.StatusCode;
import com.siemens.spm.common.shared.vo.AbstractResultObject;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SummaryTemplateCoreDataResultObject extends AbstractResultObject<SummaryTemplateCoreDataVO, StatusCode> {

    private static final long serialVersionUID = -2752946900015468586L;

    private SummaryTemplateCoreDataVO data;

    private StatusCode statusCode;

    @Override
    public SummaryTemplateCoreDataVO getData() {
        return data;
    }

    @Override
    public String getMessage() {
        return statusCode.getMessage();
    }

    @Override
    protected StatusCode getErrorStatusValue() {
        return StatusCode.ERROR;
    }

    @Override
    public StatusCode getStatusCode() {
        return statusCode;
    }

    @Override
    protected void setStatusCode(StatusCode value) {
        statusCode = value;
    }

    @Override
    public StatusCode getSuccessfulStatusValue() {
        return StatusCode.SUCCESS;
    }

    @Getter
    @AllArgsConstructor
    public enum StatusCode {
        /**
         * OK
         */
        SUCCESS("success", HttpStatus.OK),
        CREATED("created", HttpStatus.CREATED),
        NOT_FOUND("not_found", HttpStatus.NOT_FOUND),

        /**
         * Internal Server Error
         */
        ERROR("unknown_exception", HttpStatus.INTERNAL_SERVER_ERROR);

        private final String errorField;
        private final String message;
        private final HttpStatus httpStatus;

        StatusCode(String message, HttpStatus httpStatus) {
            this(null, message, httpStatus);
        }
    }

    @Override
    public String getErrorFieldName() {
        return statusCode.getErrorField();
    }

    @Override
    protected void setData(SummaryTemplateCoreDataVO value) {
        data = value;
    }

    @Override
    public HttpStatus getHttpStatus() {
        return statusCode.getHttpStatus();
    }

}
