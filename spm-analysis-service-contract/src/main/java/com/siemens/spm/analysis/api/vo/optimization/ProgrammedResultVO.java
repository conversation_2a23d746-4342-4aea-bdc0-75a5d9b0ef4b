package com.siemens.spm.analysis.api.vo.optimization;

import java.io.Serializable;
import java.time.LocalTime;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * This class used for serialization to persist data into DB. Be careful when have any change in this class(field,
 * annotation, ..). This can break when deserialize data fetch from DB
 *
 * <AUTHOR> Nguyen - <EMAIL>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProgrammedResultVO implements Serializable {

    private static final long serialVersionUID = 2875560458116370142L;

    @JsonProperty("from_time")
    private LocalTime fromTime;

    @JsonProperty("to_time")
    private LocalTime toTime;

    @JsonProperty("intersections")
    private List<ProgrammedIntersectionVO> intersections;

}
