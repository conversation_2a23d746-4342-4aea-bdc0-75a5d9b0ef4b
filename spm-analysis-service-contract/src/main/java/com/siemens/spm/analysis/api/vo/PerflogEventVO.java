package com.siemens.spm.analysis.api.vo;

import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PerflogEventVO {

    @JsonProperty("date_time")
    private LocalDateTime dateTime;

    @JsonProperty("event_code")
    private String eventCode;

    @JsonProperty("parameter")
    private long parameter;
}
