package com.siemens.spm.analysis.api.vo.summaryreport;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.analysis.api.vo.enums.Trending;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UnitDataVO implements Serializable {

    private static final long serialVersionUID = -9094152167864578480L;

    @JsonProperty("value")
    private Double value;

    @JsonProperty("fluctuation")
    private Double fluctuation;

    @JsonProperty("trending")
    private Trending trending;

    public UnitDataVO(Double value) {
        this.value = value;
    }

    public UnitDataVO(Double value, Trending trending) {
        this.value = value;
        this.trending = trending;
    }

}
