//package com.siemens.spm.analysis.api.vo;
//
//import java.sql.Timestamp;
//import java.time.LocalDateTime;
//
//import org.junit.jupiter.api.Assertions;
//import org.junit.jupiter.api.Test;
//
//class IntersectionStatisticsVOTest {
//
//    @Test
//    void test_constructor() {
//        Timestamp test = Timestamp.valueOf(LocalDateTime.MIN);
//        IntersectionStatisticsVO actual = new IntersectionStatisticsVO("uuidTest", test);
//        Assertions.assertEquals("uuidTest", actual.getIntUUID());
//        Assertions.assertEquals(test, actual.getUpdatedTime());
//        Assertions.assertEquals(0, actual.getDetHit());
//        Assertions.assertEquals(0, actual.getAorHit());
//        Assertions.assertEquals(0, actual.getAogHit());
//        Assertions.assertEquals(0, actual.getTransHit());
//        Assertions.assertEquals(0, actual.getInstepHit());
//        Assertions.assertEquals(0, actual.getSplitFail());
//        Assertions.assertEquals(0, actual.getTotalAppDelayHit());
//        Assertions.assertEquals(0, actual.getTotalAppDelayTime());
//    }
//
//    @Test
//    void test_addAorHit() {
//        Timestamp test = Timestamp.valueOf(LocalDateTime.MIN);
//        IntersectionStatisticsVO actual = new IntersectionStatisticsVO("uuidTest", test);
//
//        actual.addAorHit(2);
//        Assertions.assertEquals(2, actual.getAorHit());
//        Assertions.assertEquals(0, actual.getAorPercent());
//
//        actual.setDetHit(20);
//        actual.addAorHit(18);
//        Assertions.assertEquals(20, actual.getAorHit());
//        Assertions.assertEquals(100, actual.getAorPercent());
//    }
//
//    @Test
//    void test_addAogHit() {
//        Timestamp test = Timestamp.valueOf(LocalDateTime.MIN);
//        IntersectionStatisticsVO actual = new IntersectionStatisticsVO("uuidTest", test);
//
//        actual.addAogHit(2);
//        Assertions.assertEquals(2, actual.getAogHit());
//        Assertions.assertEquals(0, actual.getAogPercent());
//
//        actual.setDetHit(20);
//        actual.addAogHit(18);
//        Assertions.assertEquals(20, actual.getAogHit());
//        Assertions.assertEquals(100, actual.getAogPercent());
//    }
//
//    @Test
//    void test_updateAorPercent() {
//        Timestamp test = Timestamp.valueOf(LocalDateTime.MIN);
//        IntersectionStatisticsVO actual = new IntersectionStatisticsVO("uuidTest", test);
//
//        actual.setAorHit(2);
//        Assertions.assertEquals(0, actual.getAorPercent());
//
//        actual.setDetHit(20);
//        actual.updateAorPercent();
//        Assertions.assertEquals(10, actual.getAorPercent());
//    }
//
//    @Test
//    void test_updateAogPercent() {
//        Timestamp test = Timestamp.valueOf(LocalDateTime.MIN);
//        IntersectionStatisticsVO actual = new IntersectionStatisticsVO("uuidTest", test);
//
//        actual.setAogHit(2);
//        Assertions.assertEquals(0, actual.getAogPercent());
//
//        actual.setDetHit(20);
//        actual.updateAogPercent();
//        Assertions.assertEquals(10, actual.getAogPercent());
//    }
//
//    @Test
//    void test_addTransHit() {
//        Timestamp test = Timestamp.valueOf(LocalDateTime.MIN);
//        IntersectionStatisticsVO actual = new IntersectionStatisticsVO("uuidTest", test);
//
//        actual.addTransHit(null, null);
//        Assertions.assertEquals(0, actual.getTransHit());
//        Assertions.assertEquals(0, actual.getInstepHit());
//        Assertions.assertEquals(0, actual.getCoordHealthTrans());
//
//        actual.addTransHit(null, 1);
//        Assertions.assertEquals(0, actual.getTransHit());
//        Assertions.assertEquals(1, actual.getInstepHit());
//        Assertions.assertEquals(0, actual.getCoordHealthTrans());
//
//        actual.addTransHit(1, null);
//        Assertions.assertEquals(1, actual.getTransHit());
//        Assertions.assertEquals(1, actual.getInstepHit());
//        Assertions.assertEquals(50, actual.getCoordHealthTrans());
//
//        actual.addTransHit(1, 1);
//        Assertions.assertEquals(2, actual.getTransHit());
//        Assertions.assertEquals(2, actual.getInstepHit());
//        Assertions.assertEquals(50, actual.getCoordHealthTrans());
//    }
//
//    @Test
//    void test_updateCoordHealthTransPercent() {
//        Timestamp test = Timestamp.valueOf(LocalDateTime.MIN);
//        IntersectionStatisticsVO actual = new IntersectionStatisticsVO("uuidTest", test);
//
//        actual.updateCoordHealthTransPercent();
//        Assertions.assertEquals(0, actual.getCoordHealthTrans());
//
//        actual.setTransHit(2);
//        actual.setInstepHit(2);
//        Assertions.assertEquals(0, actual.getCoordHealthTrans());
//
//        actual.updateCoordHealthTransPercent();
//        Assertions.assertEquals(50, actual.getCoordHealthTrans());
//    }
//
//    @Test
//    void test_addSplitFailure() {
//        Timestamp test = Timestamp.valueOf(LocalDateTime.MIN);
//        IntersectionStatisticsVO actual = new IntersectionStatisticsVO("uuidTest", test);
//
//        Assertions.assertEquals(0, actual.getSplitFail());
//
//        actual.addSplitFailure(null);
//        Assertions.assertEquals(0, actual.getSplitFail());
//
//        actual.addSplitFailure(2);
//        Assertions.assertEquals(2, actual.getSplitFail());
//
//        actual.addSplitFailure(3);
//        Assertions.assertEquals(5, actual.getSplitFail());
//    }
//
//
//    @Test
//    void test_updatePedDelay() {
//        Timestamp test = Timestamp.valueOf(LocalDateTime.MIN);
//        IntersectionStatisticsVO actual = new IntersectionStatisticsVO("uuidTest", test);
//
//        actual.updatePedDelay(null, null);
//        Assertions.assertEquals(0, actual.getAvgPedDelay());
//
//        actual.updatePedDelay(0, 0L);
//        Assertions.assertEquals(0, actual.getAvgPedDelay());
//
//        actual.updatePedDelay(2, 2L);
//        Assertions.assertEquals(1, actual.getAvgPedDelay());
//
//        actual.updatePedDelay(18, 8L);
//        Assertions.assertEquals(0.5, actual.getAvgPedDelay());
//    }
//
//    @Test
//    void test_addAppDelay() {
//        Timestamp test = Timestamp.valueOf(LocalDateTime.MIN);
//        IntersectionStatisticsVO actual = new IntersectionStatisticsVO("uuidTest", test);
//
//        actual.addAppDelay(null, null);
//        Assertions.assertEquals(0, actual.getAvgAppDelay());
//
//        actual.addAppDelay(0, 0D);
//        Assertions.assertEquals(0, actual.getAvgAppDelay());
//
//        actual.addAppDelay(2, 2D);
//        Assertions.assertEquals(1, actual.getAvgAppDelay());
//
//        actual.addAppDelay(18, 8D);
//        Assertions.assertEquals(0.5, actual.getAvgAppDelay());
//    }
//
//    @Test
//    void test_addRLVCount() {
//        Timestamp test = Timestamp.valueOf(LocalDateTime.MIN);
//        IntersectionStatisticsVO actual = new IntersectionStatisticsVO("uuidTest", test);
//
//        actual.addRLVCount(1);
//        Assertions.assertEquals(1, actual.getRlvCount());
//
//        actual.addRLVCount(null);
//        Assertions.assertEquals(1, actual.getRlvCount());
//    }
//}
