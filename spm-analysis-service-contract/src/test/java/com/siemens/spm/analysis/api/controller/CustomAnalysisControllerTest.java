// package com.siemens.spm.analysis.api.controller;

// import org.junit.jupiter.api.Assertions;
// import org.junit.jupiter.api.Test;

// class CustomAnalysisControllerTest {

//     @Test
//     void test_constant_valid() {
//         Assertions.assertEquals("/v1", CustomAnalysisController.VERSION);
//         Assertions.assertEquals("/analysis/custom-analysis", CustomAnalysisController.CUSTOM_ANALYSIS_RESOURCE);
//         Assertions.assertEquals("/api/v1/agencies/{agency_id}/analysis/custom-analysis", CustomAnalysisController.API_ROOT);
//     }
// }
