package com.siemens.spm.common.agency.utils;

/**
 * <AUTHOR> Admin
 * @version : 1.0
 * @since : 25/12/2024
 **/
public final class AgencyPersistenceConstants {

    private AgencyPersistenceConstants() {
    }

    /**
     * Constants for multi-tenancy in agency zone
     */
    public static final String AGENCY_ENTITY_MANAGER_FACTORY = "agencyEntityManagerFactory";

    public static final String AGENCY_TRANSACTION_MANAGER = "agencyTransactionManager";

    public static final String AGENCY_DATA_SOURCE = "agencyDataSource";

    public static final String AGENCY_HIKARI_CONFIG = "agencyHikariConfig";

    public static final String AGENCY_PERSISTENCE_UNIT_NAME = "agency-persistence-unit";



    public static final String AGENCY_LIQUIBASE_PROPERTIES = "agencyLiquibaseProperties";

    public static final String AGENCY_JDBC_TEMPLATE = "agencyJdbcTemplate";

    /**
     * Constants for multi-tenancy in master zone
     */
    public static final String MASTER_ENTITY_MANAGER_FACTORY = "masterEntityManagerFactory";

    public static final String MASTER_TRANSACTION_MANAGER = "masterTransactionManager";

    public static final String MASTER_DATA_SOURCE = "masterDataSource";

    public static final String MASTER_HIKARI_CONFIG = "masterHikariConfig";

    public static final String MASTER_JDBC_TEMPLATE = "masterJdbcTemplate";

    public static final String MASTER_PERSISTENCE_UNIT_NAME = "master-persistence-unit";

    public static final String MASTER_LIQUIBASE_PROPERTIES = "masterLiquibaseProperties";

    public static final String MASTER_SCHEMA = "public";

}
