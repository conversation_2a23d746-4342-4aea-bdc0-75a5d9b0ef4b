package com.siemens.spm.common.agency.utils;

import org.slf4j.MDC;

import static com.siemens.spm.common.constant.AgencyConstants.AGENCY_ID_KEY;
import static com.siemens.spm.common.constant.AgencyConstants.AGENCY_PREFIX;

/**
 * <AUTHOR> Admin
 * @version : 1.0
 * @since : 26/12/2024
 **/
public final class AgencyUtils {

    private AgencyUtils() {
    }

    public static String getAgencyId() {
        return MDC.get(AGENCY_ID_KEY) != null ? MDC.get(AGENCY_ID_KEY) : null;
    }

    /**
     * Get the schema name for the given agencyId
     *
     * @param agencyId
     * @return
     */
    public static String getSchemaName(String agencyId) {
        return AGENCY_PREFIX + agencyId;
    }
}
