package com.siemens.spm.common.agency.supports.provision;

import com.siemens.spm.common.agency.master.AgencySchemaReadService;
import com.siemens.spm.common.agency.master.AgencySchemaWriteService;
import com.siemens.spm.common.agency.master.AgencySchemaWriteServiceImpl;
import com.siemens.spm.common.agency.master.repository.AgencySchemaRepository;
import liquibase.integration.spring.SpringLiquibase;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.liquibase.LiquibaseProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ResourceLoader;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;

import static com.siemens.spm.common.agency.utils.AgencyPersistenceConstants.AGENCY_DATA_SOURCE;
import static com.siemens.spm.common.agency.utils.AgencyPersistenceConstants.AGENCY_JDBC_TEMPLATE;
import static com.siemens.spm.common.agency.utils.AgencyPersistenceConstants.AGENCY_LIQUIBASE_PROPERTIES;
import static com.siemens.spm.common.agency.utils.AgencyPersistenceConstants.MASTER_DATA_SOURCE;
import static com.siemens.spm.common.agency.utils.AgencyPersistenceConstants.MASTER_LIQUIBASE_PROPERTIES;
import static com.siemens.spm.common.agency.utils.AgencyPersistenceConstants.MASTER_SCHEMA;

/**
 * <AUTHOR> Admin
 * @version : 1.0
 * @since : 26/12/2024
 **/
@Configuration
@AutoConfigureAfter({AgencySchemaReadService.class})
@ConditionalOnBean(annotation = EnableAgencyProvisionAutoConfiguration.class)
public class AgencyProvisionAutoConfiguration {

    @Bean
    public AgencyProvision agencyProvision(
        @Qualifier("agencyLiquibaseService") AgencyLiquibaseService agencyLiquibaseService,
        AgencySchemaReadService agencySchemaReadService) {
        return new AgencyProvision(agencyLiquibaseService, agencySchemaReadService);
    }

    @Bean
    public AgencySchemaWriteService agencySchemaWriteService(
        @Qualifier("agencyLiquibaseService") AgencyLiquibaseService agencyLiquibaseService,
        AgencySchemaRepository agencySchemaRepository, @Qualifier(AGENCY_JDBC_TEMPLATE) JdbcTemplate jdbcTemplate) {
        return new AgencySchemaWriteServiceImpl(agencySchemaRepository, agencyLiquibaseService, jdbcTemplate);
    }

    @Bean("agencyLiquibaseService")
    public AgencyLiquibaseService agencyLiquibaseService(
        @Qualifier(AGENCY_LIQUIBASE_PROPERTIES) LiquibaseProperties agencyLiquibaseProperties,
        @Qualifier(AGENCY_DATA_SOURCE) DataSource dataSource, ResourceLoader resourceLoader,
        @Qualifier(AGENCY_JDBC_TEMPLATE) JdbcTemplate jdbcTemplate) {

        return new AgencyLiquibaseService(agencyLiquibaseProperties, dataSource, resourceLoader, jdbcTemplate);

    }

    @Bean(AGENCY_LIQUIBASE_PROPERTIES)
    @ConfigurationProperties("multi-agency.provision.agency.liquibase")
    public LiquibaseProperties agencyLiquibaseProperties() {
        return new LiquibaseProperties();
    }

    @Bean(MASTER_LIQUIBASE_PROPERTIES)
    @ConfigurationProperties("multi-agency.provision.master.liquibase")
    public LiquibaseProperties masterLiquibaseProperties() {
        return new LiquibaseProperties();
    }

    @Bean
    @ConditionalOnProperty(prefix = "multi-agency.provision.master.liquibase", name = "enabled", havingValue = "true")
    public SpringLiquibase masterLiquibase(@Qualifier(MASTER_DATA_SOURCE) DataSource dataSource) {
        SpringLiquibase liquibase = new SpringLiquibase();

        liquibase.setDataSource(dataSource);
        liquibase.setDefaultSchema(MASTER_SCHEMA);
        liquibase.setChangeLog(masterLiquibaseProperties().getChangeLog());
        liquibase.setContexts(masterLiquibaseProperties().getContexts());

        liquibase.setShouldRun(masterLiquibaseProperties().isEnabled());

        return liquibase;
    }

}
