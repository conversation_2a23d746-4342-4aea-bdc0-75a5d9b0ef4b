package com.siemens.spm.common.agency.supports.multiagencies;

import com.siemens.spm.common.agency.supports.multiagencies.interceptor.SchemaQueryInterceptor;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import jakarta.persistence.EntityManagerFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.orm.jpa.JpaProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.ImportAware;
import org.springframework.context.annotation.Primary;
import org.springframework.core.type.AnnotationMetadata;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.JpaVendorAdapter;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

import static com.siemens.spm.common.agency.utils.AgencyPersistenceConstants.AGENCY_DATA_SOURCE;
import static com.siemens.spm.common.agency.utils.AgencyPersistenceConstants.AGENCY_ENTITY_MANAGER_FACTORY;
import static com.siemens.spm.common.agency.utils.AgencyPersistenceConstants.AGENCY_HIKARI_CONFIG;
import static com.siemens.spm.common.agency.utils.AgencyPersistenceConstants.AGENCY_JDBC_TEMPLATE;
import static com.siemens.spm.common.agency.utils.AgencyPersistenceConstants.AGENCY_PERSISTENCE_UNIT_NAME;
import static com.siemens.spm.common.agency.utils.AgencyPersistenceConstants.AGENCY_TRANSACTION_MANAGER;

@Slf4j
@Configuration
@EnableTransactionManagement
public class AgencyPersistenceConfiguration implements ImportAware {

    private final JpaProperties jpaProperties;

    private String[] entityPackages;

    public AgencyPersistenceConfiguration(JpaProperties jpaProperties) {
        this.jpaProperties = jpaProperties;
    }

    @Bean(name = AGENCY_JDBC_TEMPLATE)
    public JdbcTemplate jdbcTemplate(@Qualifier(AGENCY_DATA_SOURCE) DataSource agencyDataSource) {
        return new JdbcTemplate(agencyDataSource);
    }

    @Bean(name = AGENCY_HIKARI_CONFIG)
    @Primary
    @ConfigurationProperties(prefix = "spring.datasource.agency.hikari")
    public HikariConfig primaryHikariConfig() {
        return new HikariConfig();
    }

    @Primary
    @Bean(name = AGENCY_DATA_SOURCE)
    public DataSource primaryDataSource(@Qualifier(AGENCY_HIKARI_CONFIG) HikariConfig hikariConfig) {
        return new HikariDataSource(hikariConfig);
    }

    @Bean(AGENCY_ENTITY_MANAGER_FACTORY)
    @Primary
    public LocalContainerEntityManagerFactoryBean agencyEntityManagerFactory(
        @Qualifier(AGENCY_DATA_SOURCE) DataSource dataSource) {
        log.info("Creating Agency EntityManagerFactory");

        LocalContainerEntityManagerFactoryBean emf = new LocalContainerEntityManagerFactoryBean();
        JpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
        emf.setJpaVendorAdapter(vendorAdapter);
        emf.setPersistenceUnitName(AGENCY_PERSISTENCE_UNIT_NAME);
        emf.setPackagesToScan(entityPackages);
        emf.setDataSource(dataSource);

        Map<String, Object> properties = new HashMap<>(this.jpaProperties.getProperties());
        properties.put("hibernate.session_factory.statement_inspector", new SchemaQueryInterceptor());
        emf.setJpaPropertyMap(properties);

        return emf;
    }

    @Bean(name = AGENCY_TRANSACTION_MANAGER)
    @Primary
    public PlatformTransactionManager primaryTransactionManager(
        @Qualifier(AGENCY_ENTITY_MANAGER_FACTORY) EntityManagerFactory entityManagerFactory) {
        JpaTransactionManager transactionManager = new JpaTransactionManager();
        transactionManager.setEntityManagerFactory(entityManagerFactory);
        return transactionManager;
    }

    @Override
    public void setImportMetadata(AnnotationMetadata metadata) {
        Map<String, Object> attributes =
            metadata.getAnnotationAttributes(EnableMultiAgenciesPersistence.class.getName());

        this.entityPackages = (String[]) attributes.get("entityPackages");
    }

}