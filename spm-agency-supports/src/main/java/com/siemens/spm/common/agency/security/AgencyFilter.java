package com.siemens.spm.common.agency.security;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.siemens.spm.common.agency.exception.ErrorResponse;
import com.siemens.spm.common.agency.master.AgencySchemaReadService;
import com.siemens.spm.common.agency.supports.multiagencies.EnableMultiAgenciesPersistence;
import com.siemens.spm.common.shared.exception.BadRequestException;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

import java.util.Optional;

import static com.siemens.spm.common.constant.AgencyConstants.AGENCY_ID_HEADER;
import static com.siemens.spm.common.constant.AgencyConstants.AGENCY_ID_KEY;

@Order(1)
@Component
@Slf4j
@ConditionalOnBean(annotation = EnableMultiAgenciesPersistence.class)
public class AgencyFilter extends OncePerRequestFilter {

    private final AntPathMatcher antPathMatcher;

    private final SecurityProperties securityProperties;

    private final AgencySchemaReadService agencySchemaReadService;

    private final ObjectMapper objectMapper;

    public AgencyFilter(SecurityProperties securityProperties, AgencySchemaReadService agencySchemaReadService,
        ObjectMapper objectMapper) {
        this.securityProperties = securityProperties;
        this.agencySchemaReadService = agencySchemaReadService;
        this.objectMapper = objectMapper;
        this.antPathMatcher = new AntPathMatcher();
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
        throws ServletException, IOException {
        try {
            processAgencyIdHeader(request);
            response.setHeader("Content-Security-Policy", "frame-src 'self'; frame-ancestors 'self'; object-src 'self'; script-src 'self';require-trusted-types-for 'script';");
            filterChain.doFilter(request, response);
        } catch (NumberFormatException | BadRequestException e) {
            if (!response.isCommitted()) {
                try {
                    response.setContentType(MediaType.APPLICATION_JSON_VALUE);
                    response.setStatus(HttpStatus.FORBIDDEN.value());
                    objectMapper.writeValue(response.getWriter(),
                        new ErrorResponse("You don't have permission to perform this operation!"));
                } catch (IOException ex) {
                    log.error("Failed to write error response", ex);
                }
            }
        } finally {
            MDC.remove(AGENCY_ID_KEY);
        }
    }

    private void processAgencyIdHeader(HttpServletRequest request) {
        String agencyId = null;
        try {
            agencyId = Optional
                        .ofNullable(request.getHeader(AGENCY_ID_HEADER))
                        .or(() -> Optional.ofNullable(request.getHeader(AGENCY_ID_HEADER.toLowerCase())))
                        .orElseThrow(() -> new BadRequestException(HttpStatus.BAD_REQUEST, "header",
                                                "Missing " + AGENCY_ID_HEADER + " header"));
        } catch (BadRequestException e) {
            if (request.getRequestURI().contains("internal-api")) 
            {
                // not all internal-api endpoints need agencyId
                return; 
            } else {
                throw e;
            }
        }
        
        //validate to prevent any further logic from executing. This method is cached so this will not hit DB too much
        if (!agencySchemaReadService.isExists(Integer.valueOf(agencyId))) {
            throw new BadRequestException(HttpStatus.BAD_REQUEST, "header", "Not existed agency for id : " + agencyId);
        }
        MDC.put(AGENCY_ID_KEY, agencyId);
    }

    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) {
        String servletPath = request.getServletPath();
        return securityProperties.getAllowedAgencyFilters()
                                 .stream()
                                 .anyMatch(pattern -> antPathMatcher.match(pattern, servletPath));
    }

}
