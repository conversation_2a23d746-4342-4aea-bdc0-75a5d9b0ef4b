package com.siemens.spm.common.agency.exception;

public class AgencyOperationException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    public AgencyOperationException(String message) {
        super(message);
    }

    public AgencyOperationException(String message, Throwable cause) {
        super(message, cause);
    }

    public AgencyOperationException(Throwable cause) {
        super(cause);
    }

    public AgencyOperationException(String message, Throwable cause, boolean enableSuppression,
        boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }

}