package com.siemens.spm.common.agency.supports.provision;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR> Admin
 * @version : 1.0
 * @since : 26/12/2024
 **/
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface EnableAgencyProvisionAutoConfiguration {
}
