package com.siemens.spm.common.agency.exception;

import lombok.Getter;

import java.io.Serializable;

/**
 * <AUTHOR> Admin
 * @version : 1.0
 * @since : 26/12/2024
 **/
@Getter
public class ErrorResponse implements Serializable {

    private final String message;

    private final long timestamp;

    public ErrorResponse(String message) {
        this.message = message;
        this.timestamp = System.currentTimeMillis();
    }

}
