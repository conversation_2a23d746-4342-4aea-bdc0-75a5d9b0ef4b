/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AlarmRuleEvaluationJobsDoRequestVO.java
 * Project     : SPM Platform
 */

package com.siemens.spm.ruleevaluation.api.vo.request;

import java.io.Serializable;
import java.util.List;

import jakarta.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.ruleevaluation.api.vo.AlarmRuleEvaluationJobVO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class AlarmRuleEvaluationJobsDoRequestVO implements Serializable {

    private static final long serialVersionUID = 1946241998661568108L;

    @NotNull(message = "rule_evaluation.alarm_rule_evaluation_job_vos_not_null")
    @JsonProperty("alarm_rule_evaluation_job_vos")
    private List<AlarmRuleEvaluationJobVO> alarmRuleEvaluationJobVOs;

}
