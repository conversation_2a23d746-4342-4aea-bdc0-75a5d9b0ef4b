/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AlarmRuleEvaluationJobVO.java
 * Project     : SPM Platform
 */

package com.siemens.spm.ruleevaluation.api.vo;

import java.io.Serializable;
import java.sql.Timestamp;
import java.time.LocalDateTime;

import jakarta.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class AlarmRuleEvaluationJobVO implements Serializable {

    private static final long serialVersionUID = 5298654562370915760L;

    @NotNull
    @JsonProperty("job_id")
    private String jobId;

    @JsonProperty("number_of_redos")
    private Long numberOfRedos;

    @NotNull
    @JsonProperty("rule_id")
    private Long ruleId;

    @NotNull
    @JsonProperty("rule_name")
    private String ruleName;

    @JsonProperty("rule_description")
    private String ruleDescription;

    @JsonProperty("rule_analysis_id")
    private String ruleAnalysisId;

    @NotNull
    @JsonProperty("rule_category_id")
    private Long ruleCategoryId;

    @NotNull
    @JsonProperty("rule_sampling_period")
    private Long ruleSamplingPeriod;

    @NotNull
    @JsonProperty("rule_condition")
    private String ruleCondition;

    @NotNull
    @JsonProperty("rule_agency_id")
    private Integer ruleAgencyId;

    @NotNull
    @JsonProperty("rule_intersection_list")
    private String ruleIntersectionList;

    @NotNull
    @JsonProperty("perflog_start_time")
    private LocalDateTime perfLogStartTime;

    @NotNull
    @JsonProperty("perflog_end_time")
    private LocalDateTime perfLogEndTime;

    @JsonProperty("rule_last_updated_time")
    private Timestamp ruleLastUpdatedTime;

    @NotNull
    @JsonProperty("zone_offset")
    private String zoneOffset;

}
