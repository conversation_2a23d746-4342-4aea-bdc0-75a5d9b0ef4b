/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : RuleEvaluationService.java
 * Project     : SPM Platform
 */

package com.siemens.spm.ruleevaluation.api.boundary;

import jakarta.validation.constraints.NotNull;

import com.siemens.spm.common.shared.vo.SimpleResultObject;
import com.siemens.spm.ruleevaluation.api.vo.request.AlarmRuleEvaluationJobsDoRequestVO;

public interface RuleEvaluationService {

    /**
     * Do alarm rule evaluation jobs
     *
     * @param requestVO AlarmRuleEvaluationJobsDoRequestVO
     * @return SimpleResultObject
     */
    SimpleResultObject doAlarmRuleEvaluationJobsInternal(@NotNull AlarmRuleEvaluationJobsDoRequestVO requestVO);

}
