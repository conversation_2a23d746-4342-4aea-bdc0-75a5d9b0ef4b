package com.siemens.spm.common.shared.vo;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StatisticDataVO implements Serializable {

    private static final long serialVersionUID = 6330540486808710068L;

    @JsonProperty("min")
    private StatisticDataUnitVO min;

    @JsonProperty("max")
    private StatisticDataUnitVO max;

    @JsonProperty("avg")
    private StatisticDataUnitVO average;

}
