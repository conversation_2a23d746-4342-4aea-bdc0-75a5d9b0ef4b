/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : JpaConstants.java
 * Project     : SPM Platform
 */

package com.siemens.spm.common.constant;

public final class JpaConstants {

    private JpaConstants() {
    }

    public static final class Page {
        private Page() {
        }

        public static final int DEFAULT_PAGE = 0;
    }

    public static final class Size {

        private Size() {
        }

        public static final int DEFAULT_SIZE = 10;

        public static final int MAX_SIZE = 100;

        public static final int BIG_MAX_SIZE = 1000;

    }

    public static final class Sorting {

        private Sorting() {
        }

        public static final String ASC = "ascend";

        public static final String DESC = "descend";

    }

    public static final String COLUMN_SEPARATOR = ",";

    // the longest supported zone offset format is "+hh:mm:ss" or "-hh:mm:ss"
    public static final int ZONE_OFFSET_MAX_LENGTH = 9;

}
