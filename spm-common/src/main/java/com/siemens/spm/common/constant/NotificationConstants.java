/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : NotificationConstants.java
 * Project     : SPM Platform
 */
package com.siemens.spm.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
public final class NotificationConstants {

    private NotificationConstants() {
    }

    @Getter
    @AllArgsConstructor
    public enum ReadStatus {

        READ("READ", "read"), UNREAD("UNREAD", "unread");

        private String value;

        private String description;

    }

    @Getter
    @AllArgsConstructor
    public enum FlagStatus {

        FLAG("FLAG", "flag"), UNFLAG("UNFLAG", "unflag");

        private String value;

        private String description;

    }

    @Getter
    @AllArgsConstructor
    public enum SendStatus {

        SENT("SENT", "The notification was sent to user via websocket."),
        UNSENT("UNSENT", "The notification was not sent to user via websocket.");

        private String value;

        private String description;

    }

    public static final String MESSAGE_NOTIFICATION_FORMAT = "{\"notification\": %s}";

    public static final String ALARM_RULE_CREATED = "alarm_rule.noti.name.rule_created";
    public static final String ALARM_RULE_CREATED_DESC = "alarm_rule.noti.desc.rule_created";

    public static final String ALARM_RULE_ACTIVATED = "alarm_rule.noti.name.rule_activated";
    public static final String ALARM_RULE_ACTIVATED_DESC = "alarm_rule.noti.desc.rule_activated";

    public static final String ALARM_RULE_DEACTIVATED = "alarm_rule.noti.name.rule_deactivated";
    public static final String ALARM_RULE_DEACTIVATED_DESC = "alarm_rule.noti.desc.rule_deactivated";

    public static final String ALARM_RULE_UPDATED = "alarm_rule.noti.name.rule_updated";
    public static final String ALARM_RULE_UPDATED_DESC = "alarm_rule.noti.desc.rule_updated";

    public static final String ALARM_RULE_SUBSCRIBED_NO_EMAIL = "alarm_rule.noti.name.rule_subscribed_no_email";
    public static final String ALARM_RULE_SUBSCRIBED_DESC_NO_EMAIL = "alarm_rule.noti.desc.rule_subscribed_no_email";

    public static final String ALARM_RULE_SUBSCRIBED_WITH_EMAIL = "alarm_rule.noti.name.rule_subscribed_with_email";
    public static final String ALARM_RULE_SUBSCRIBED_DESC_WITH_EMAIL = "alarm_rule.noti.desc.rule_subscribed_with_email";

    public static final String ALARM_RULE_UNSUBSCRIBED = "alarm_rule.noti.name.rule_unsubscribed";
    public static final String ALARM_RULE_UNSUBSCRIBED_DESC = "alarm_rule.noti.desc.rule_unsubscribed";

    public static final String ALARM_RULE_DELETED = "alarm_rule.noti.name.rule_deleted";
    public static final String ALARM_RULE_DELETED_DESC = "alarm_rule.noti.desc.rule_deleted";

    // Summary Report Template
    public static final String SUMMARY_TEMPLATE_CREATED = "summary-report.templates.noti.created";
    public static final String SUMMARY_TEMPLATE_CREATED_DESC = "summary-report.templates.noti.desc.created";

    public static final String SUMMARY_TEMPLATE_UPDATED = "summary-report.templates.noti.updated";
    public static final String SUMMARY_TEMPLATE_UPDATED_DESC = "summary-report.templates.noti.desc.updated";

    public static final String SUMMARY_TEMPLATE_ACTIVATED = "summary-report.templates.noti.activated";
    public static final String SUMMARY_TEMPLATE_ACTIVATED_DESC = "summary-report.templates.noti.desc.activated";

    public static final String SUMMARY_TEMPLATE_DEACTIVATED = "summary-report.templates.noti.deactivated";
    public static final String SUMMARY_TEMPLATE_DEACTIVATED_DESC = "summary-report.templates.noti.desc.deactivated";

    public static final String SUMMARY_TEMPLATE_DELETED = "summary-report.templates.noti.deleted";
    public static final String SUMMARY_TEMPLATE_DELETED_DESC = "summary-report.templates.noti.desc.deleted";

    // Summary Report result
    public static final String SUMMARY_RESULT_CREATED = "summary-report.results.noti.created";
    public static final String SUMMARY_RESULT_CREATED_DESC = "summary-report.results.noti.desc.created";

    // Performance metric template
    public static final String PM_TEMPLATE_CREATED = "performance-metric.templates.noti.created";
    public static final String PM_TEMPLATE_CREATED_DESC = "performance-metric.templates.noti.desc.created";

    public static final String PM_TEMPLATE_UPDATED = "performance-metric.templates.noti.updated";
    public static final String PM_TEMPLATE_UPDATED_DESC = "performance-metric.templates.noti.desc.updated";

    public static final String PM_TEMPLATE_ACTIVATED = "performance-metric.templates.noti.activated";
    public static final String PM_TEMPLATE_ACTIVATED_DESC = "performance-metric.templates.noti.desc.activated";

    public static final String PM_TEMPLATE_DEACTIVATED = "performance-metric.templates.noti.deactivated";
    public static final String PM_TEMPLATE_DEACTIVATED_DESC = "performance-metric.templates.noti.desc.deactivated";

    public static final String PM_TEMPLATE_DELETED = "performance-metric.templates.noti.deleted";
    public static final String PM_TEMPLATE_DELETED_DESC = "performance-metric.templates.noti.desc.deleted";

    // Performance metric result
    public static final String PM_RESULT_CREATED = "performance-metric.results.noti.created";
    public static final String PM_RESULT_CREATED_DESC = "performance-metric.results.noti.desc.created";

    // Performance metric template
    public static final String DETECTOR_TEMPLATE_CREATED = "detector-report.templates.noti.created";
    public static final String DETECTOR_TEMPLATE_CREATED_DESC = "detector-report.templates.noti.desc.created";
    public static final String DETECTOR_TEMPLATE_UPDATED = "detector-report.templates.noti.updated";
    public static final String DETECTOR_TEMPLATE_UPDATED_DESC = "detector-report.templates.noti.desc.updated";

    public static final String DETECTOR_TEMPLATE_ACTIVATED = "detector-report.templates.noti.activated";
    public static final String DETECTOR_TEMPLATE_ACTIVATED_DESC = "detector-report.templates.noti.desc.activated";

    public static final String DETECTOR_TEMPLATE_DEACTIVATED = "detector-report.templates.noti.deactivated";
    public static final String DETECTOR_TEMPLATE_DEACTIVATED_DESC = "detector-report.templates.noti.desc.deactivated";

    public static final String DETECTOR_TEMPLATE_DELETED = "detector-report.templates.noti.deleted";
    public static final String DETECTOR_TEMPLATE_DELETED_DESC = "detector-report.templates.noti.desc.deleted";

    public static final String DETECTOR_RESULT_CREATED = "detector-report.results.noti.created";
    public static final String DETECTOR_RESULT_CREATED_DESC = "detector-report.results.noti.desc.created";

}
