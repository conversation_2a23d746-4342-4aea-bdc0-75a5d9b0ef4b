/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AbstractSimpleResultObject.java
 * Project     : SPM Platform
 */
package com.siemens.spm.common.shared.vo;

import java.io.Serializable;
import java.text.MessageFormat;

import org.springframework.http.HttpStatus;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.siemens.spm.common.util.TypeUtils;
import io.swagger.v3.oas.annotations.media.Schema;

import static io.swagger.v3.oas.annotations.media.Schema.AccessMode.READ_ONLY;

/**
 * Base class to create custom result objects to be used when return values over RESTful services. In contrast to
 * AbstractResultObject, this class can only provide a status, no wrapped result value. <br/>
 *
 * @param <S_STATUS_CODE> an enumeration that represents your result status, such as "SUCCESS, ERROR", etc.
 */
@JsonIgnoreProperties({ "statusCode", "message", "httpStatus" })
public abstract class AbstractSimpleResultObject<S_STATUS_CODE extends Serializable> implements Serializable {

    private static final long serialVersionUID = -8627032769254756108L;

    /**
     * "Default" ERROR Constructor
     */
    protected AbstractSimpleResultObject() {
        setStatusCode(getErrorStatusValue());
    }

    /**
     * Secondary constructor to set status to other than success, for example to indicate a warning (this is of course
     * use case specific).
     */
    protected AbstractSimpleResultObject(S_STATUS_CODE statusCode) {
        setStatusCode(statusCode);
    }

    /**
     * This method is used to get the status code
     */
    @Schema(accessMode = READ_ONLY)
    public abstract S_STATUS_CODE getStatusCode();

    /**
     * This method is used to get the message
     */
    @Schema(accessMode = READ_ONLY)
    public abstract String getMessage();

    /**
     * This method is used to get the HttpStatus
     */
    @Schema(accessMode = READ_ONLY)
    public abstract HttpStatus getHttpStatus();

    /**
     * This method is used to get the ErrorField name
     */
    @Schema(accessMode = READ_ONLY)
    public abstract String getErrorFieldName();

    /**
     * Internal set function for serialization, do not use in application code, instead use CTOR!
     */
    protected abstract void setStatusCode(S_STATUS_CODE value);

    /**
     * @return which value in the enumeration represents an error and will be set if no data can be provided in the
     * result
     */
    @JsonIgnore
    protected abstract S_STATUS_CODE getErrorStatusValue();

    /**
     * @return Which value in the enumeration of status values indicates success
     */
    @JsonIgnore
    public abstract S_STATUS_CODE getSuccessfulStatusValue();

    /**
     * Test if current result object contain a "successful" status flag.
     *
     * @return if current result is considered a success or not, true if success!
     */
    @JsonIgnore
    public boolean isSuccessful() {
        return getSuccessfulStatusValue() == getStatusCode();
    }

    /**
     * @return complete message for log output generation only
     */
    @Override
    @JsonIgnore
    public String toString() {
        // STEP: define out put format, we do NOT trace the message parameters
        // (maybe too much stuff)
        String format = "{0}: statusCode=<{1}>; dataType=<{2}>";

        // STEP: get class name
        String className = this.getClass().getSimpleName();

        // STEP: Get data information
        String dataType = null;

        // STEP: build message
        return MessageFormat.format(format, className, getStatusCode(), dataType);
    }

    @Override
    public boolean equals(Object obj) {
        if (obj instanceof AbstractSimpleResultObject<?> other) {
            return TypeUtils.equals(this.getStatusCode(), other.getStatusCode());
        }
        return false;
    }

    @Override
    public int hashCode() {
        return TypeUtils.hashCode(this.getStatusCode());
    }
}
