/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : IntersectionStatus.java
 * Project     : SPM Platform
 */
package com.siemens.spm.common.shared.domaintype;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * Define all status of intersection
 */
@Getter
public enum IntersectionStatus {
    AVAILABLE("AVAILABLE", "ACTIVE"),
    UNAVAILABLE("UNAVAILABLE", "INACTIVE");

    /**
     * {@code AVAILABLE | UNAVAILABLE}
     */
    private final String insight;
    /**
     * {@code ACTIVE | INACTIVE}
     */
    private final String dataHub;

    IntersectionStatus(String insight, String dataHub) {
        this.insight = insight;
        this.dataHub = dataHub;
    }

    private static final Map<String, IntersectionStatus> STATUS_MAP = new HashMap<>();

    static {
        for (IntersectionStatus status : IntersectionStatus.values()) {
            STATUS_MAP.put(status.insight, status);
            STATUS_MAP.put(status.dataHub, status);
        }
    }

    /**
     * Get DataHub mapped value by Insight value (case-insensitive)
     *
     * @param value Value from Insight {@code AVAILABLE|UNAVAILABLE}
     * @return Mapped value from DataHub {@code ACTIVE|INACTIVE}
     * <p>{@code ACTIVE|INACTIVE} if matched itself</p>
     * <p>{@code NULL} if non-matched</p>
     */
    public static String toDataHub(String value) {
        if (value == null || value.isBlank())
            return null;

        String v = value.toUpperCase();
        var matched = STATUS_MAP.get(v);
        return Optional.ofNullable(matched).map(m -> m.dataHub).orElse(null);
    }

    /**
     * Get Insight mapped value by DataHub value (case-insensitive)
     *
     * @param value Value from DataHub {@code ACTIVE|INACTIVE}
     * @return Mapped value from Insight {@code AVAILABLE|UNAVAILABLE}.
     * <p>{@code AVAILABLE|UNAVAILABLE} if matched itself</p>
     * <p>{@code NULL} if non-matched</p>
     */
    public static String toInsight(String value) {
        if (value == null || value.isBlank())
            return null;

        String v = value.toUpperCase();
        var matched = STATUS_MAP.get(v);
        return Optional.ofNullable(matched).map(m -> m.insight).orElse(null);
    }
}
