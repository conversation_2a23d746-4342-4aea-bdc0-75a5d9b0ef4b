/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : Resource.java
 * Project     : SPM Platform
 */
package com.siemens.spm.common.shared.domaintype;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import org.springframework.core.io.ClassPathResource;
import org.springframework.lang.Nullable;

import com.siemens.spm.common.resource.SpmClassPathResource;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
public final class Resource {

    public static final String ALL_RESOURCES = "*";

    public static final String ACC = "account";

    public static final String ACC_AGENCIES = "account.agencies";

    public static final String ACC_DEF_AGENCY = "account.default-agency";

    public static final String ACC_CHANGE_PWD = "account.change-password";

    public static final String ACC_LANGUAGE = "account.language";

    public static final String ACC_RESET_PWD = "account.reset-password";

    public static final String ACC_USER_KEY = "account.user-key";

    public static final String ACC_USER_NEW_KEY = "account.user-new-key";

    public static final String AGY_AGENCY_ID = "agencies.{agency_id}";

    public static final String AGY_ASSIGN_USER = "agencies.{agency_id}.assign-user";

    public static final String AGY_REMOVE_USER = "agencies.{agency_id}.remove-users";

    public static final String AGY_DETAIL = "agency.detail";

    public static final String ALR_ANALYSIS = "alarm.analyses";

    public static final String ALR_CATEGORY = "alarm.categories";

    public static final String ALR_MESSAGES = "alarm.messages";

    public static final String ALR_NOTIFICATIONS = "alarm.notifications";

    public static final String ALR_RECORDS = "alarm.records";

    public static final String ALR_RECORDS_STAR = "alarm.records.*";

    public static final String ALR_RULES = "alarm.rules";

    public static final String ALR_RULES_ID = "alarm.rules.{rule_id}";

    public static final String ALR_RULES_ID_INSEC = "alarm.rules.{rule_id}.intersections";

    public static final String ALR_RULES_ID_SUBERS = "alarm.rules.{rule_id}.subscribers";

    public static final String ALR_RULES_STAR = "alarm.rules.*";

    public static final String ALR_RULES_ACTIVE = "alarm.rules.active";

    public static final String ALR_RULES_INACTIVE = "alarm.rules.inactive";

    public static final String ALR_RULES_INSEC_AVL = "alarm.rules.intersections.available";

    public static final String ALR_RULES_SUBERS_AVL = "alarm.rules.subscribers.available";

    public static final String ALR_RULES_SUB = "alarm.rules.subscription";

    public static final String ALR_TRAFFIC_METRICS = "alarm.traffic-metrics.all";

    public static final String ANALYSIS = "analysis.*";

    public static final String PERFLOG = "perflog.*";

    public static final String DASHBOARD = "dashboard";

    public static final String INSEC = "intersections";

    public static final String INSEC_STAR = "intersections.*";

    public static final String NOTI = "notifications";

    public static final String NOTI_STAR = "notifications.*";

    public static final String ROLES_ALL = "roles.all";

    public static final String USERS = "users";

    public static final String USERS_AGENCY = "users?agency_id";

    public static final String USERS_ID = "users.{user_id}";

    public static final String USERS_ALL = "users.all";

    public static final String USER_OTP = "users.otp";

    public static final String CORRIDOR = "corridors.*";
    public static final String CORRIDORS = "corridors";
    public static final String CORRIDORS_ID = "corridors.{corridor_id}";
    public static final String CORRIDORS_ACTIVE = "corridors.active";

    private static final Map<String, Integer> resourceNameToIndexMap = new TreeMap<>();

    private static final String[] resourceArray;

    private Resource() {
    }

    static {
        List<String> resourceList = new ArrayList<>();

        try (BufferedReader reader = new BufferedReader(new InputStreamReader(
                new ClassPathResource(SpmClassPathResource.AUTHORITY_RESOURCES_FILE_PATH).getInputStream()))) {
            reader.lines().forEach(resourceList::add);
        } catch (IOException e) {
            log.error("Error when reading resource file", e);
        }

        resourceArray = resourceList.toArray(new String[0]);
        for (int i = 0; i < resourceArray.length; i++) {
            resourceNameToIndexMap.put(resourceArray[i], i);
        }
    }

    @Nullable
    public static Integer resolveResourceName(@Nullable String resource) {
        Integer index = null;
        if (resource != null) {
            index = resourceNameToIndexMap.get(resource);
        }

        if (index == null) {
            log.error("Unknown resource {}", resource);
        }
        return index;
    }

    @Nullable
    public static String resolveIndex(int index) {
        return (index >= 0 && index < resourceArray.length)
                ? resourceArray[index]
                : null;
    }
}
