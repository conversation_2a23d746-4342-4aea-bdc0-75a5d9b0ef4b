/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : RestExceptionHandler.java
 * Project     : SPM Platform
 */
package com.siemens.spm.common.validator;

import com.siemens.spm.common.shared.exception.DataVolumeExceededException;
import jakarta.validation.ConstraintViolationException;

import org.springframework.beans.TypeMismatchException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.client.RestClientResponseException;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import com.siemens.spm.common.shared.exception.BadCredentialsException;
import com.siemens.spm.common.shared.exception.BadRequestException;

@RestControllerAdvice
public class RestExceptionHandler extends ResponseEntityExceptionHandler {

    @Override
    protected ResponseEntity<Object> handleMethodArgumentNotValid(MethodArgumentNotValidException ex,
                                                                  HttpHeaders headers,
                                                                  HttpStatusCode status,
                                                                  WebRequest request) {
        return new ResponseEntity<>(BadRequestResponse.build(ex), HttpStatus.BAD_REQUEST);
    }

    @Override
    protected ResponseEntity<Object> handleHttpMessageNotReadable(HttpMessageNotReadableException ex,
                                                                  HttpHeaders headers,
                                                                  HttpStatusCode status,
                                                                  WebRequest request) {
        return new ResponseEntity<>(BadRequestResponse.build(ex), HttpStatus.BAD_REQUEST);
    }

    @Override
    protected ResponseEntity<Object> handleTypeMismatch(TypeMismatchException ex,
                                                        HttpHeaders headers,
                                                        HttpStatusCode status,
                                                        WebRequest request) {
        return new ResponseEntity<>(BadRequestResponse.build(ex), HttpStatus.BAD_REQUEST);
    }

    @Override
    protected ResponseEntity<Object> handleMissingServletRequestParameter(MissingServletRequestParameterException ex,
                                                                          HttpHeaders headers,
                                                                          HttpStatusCode status,
                                                                          WebRequest request) {
        return new ResponseEntity<>(BadRequestResponse.build(ex), HttpStatus.BAD_REQUEST);
    }

    /**
     * Error handle for BadRequestException
     */
    @ExceptionHandler(BadRequestException.class)
    public ResponseEntity<Object> handleBadRequestException(Exception ex) {

        if (ex instanceof BadRequestException badRequestException) {
            return new ResponseEntity<>(BadRequestResponse.build(badRequestException), HttpStatus.BAD_REQUEST);
        }

        return new ResponseEntity<>(HttpStatus.BAD_REQUEST);

    }

    @ExceptionHandler(DataVolumeExceededException.class)
    public ResponseEntity<Object> handleDataVolumeExceedException(Exception ex) {

        if (ex instanceof DataVolumeExceededException dataVolumeExceeded) {
            return new ResponseEntity<>(BadRequestResponse.build(dataVolumeExceeded), HttpStatus.BAD_REQUEST);
        }

        return new ResponseEntity<>(HttpStatus.BAD_REQUEST);

    }

    /**
     * Error handle for BadCredentialsException
     */
    @ExceptionHandler(BadCredentialsException.class)
    public ResponseEntity<Object> handleBadCredentialsException(Exception ex) {

        if (ex instanceof BadCredentialsException badCredentialsException) {

            return new ResponseEntity<>(BadCredentialsResponse.build(badCredentialsException), HttpStatus.BAD_REQUEST);
        }

        return new ResponseEntity<>(HttpStatus.BAD_REQUEST);

    }

    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<Object> handleBadRequestException(ConstraintViolationException ex) {
        return new ResponseEntity<>(BadRequestResponse.build(ex), HttpStatus.BAD_REQUEST);
    }

    /**
     * Error handle for RestClientResponseException
     */
    @ExceptionHandler(RestClientResponseException.class)
    public ResponseEntity<Object> handleRestClientResponseException(Exception ex) {

        RestClientResponseException restClientResponseException = (RestClientResponseException) ex;

        return ResponseEntity.status(restClientResponseException.getStatusCode())
                .header("Content-Type", "application/json")
                .body(restClientResponseException.getResponseBodyAsString());

    }

    @ExceptionHandler(AccessDeniedException.class)
    public ResponseEntity<Object> handleAccessDeniedException(Exception ex) {
        logger.error("Access denied for this resources", ex);
        return new ResponseEntity<>(HttpStatus.FORBIDDEN);
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<Object> handleUnknownException(Exception ex) {
        logger.error("Unknown exception", ex);

        return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
    }

}
