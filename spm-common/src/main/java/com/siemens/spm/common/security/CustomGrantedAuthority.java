/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : CustomGrantedAuthority.java
 * Project     : SPM Platform
 */
package com.siemens.spm.common.security;

import java.util.List;

import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.SpringSecurityCoreVersion;

import com.siemens.spm.common.shared.vo.AccessPermission;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * {@link  GrantedAuthority} which, in addition to the assigned role, holds the agency.
 */
@Setter
@Getter
@Builder
public final class CustomGrantedAuthority implements GrantedAuthority {

    private static final long serialVersionUID = SpringSecurityCoreVersion.SERIAL_VERSION_UID;

    private final String agencyName;

    private final String agencyId;

    private final String role;

    private final Long userId;

    private Long passwordExpireTime;

    private String language;

    private List<AccessPermission> permissions;

    private List<String> licenses;

    private boolean licenseAccepted;

    public String getAgencyName() {
        return agencyName;
    }

    public String getAgencyId() {
        return agencyId;
    }

    public Long getUserId() {
        return userId;
    }

    public Long getPasswordExpireTime() {
        return passwordExpireTime;
    }

    public List<AccessPermission> getPermissions() {
        return permissions;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    @Override
    public String getAuthority() {
        return role;
    }

    @Override
    public int hashCode() {
        return 31 ^ agencyId.hashCode() ^ role.hashCode() ^ userId.hashCode();
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }

        // the combination of agencyId, userId and role defines the key
        if (obj instanceof CustomGrantedAuthority authority) {
            return this.role.equals(authority.role)
                    && this.agencyId.equals(authority.agencyId)
                    && this.userId.equals(authority.userId);
        }

        return false;
    }

    @Override
    public String toString() {
        return "Custom Authority [" + role + "," + agencyId + "," + userId + "]";
    }

}
