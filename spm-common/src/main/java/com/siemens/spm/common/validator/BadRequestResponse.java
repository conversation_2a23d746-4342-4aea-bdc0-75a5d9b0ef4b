/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : BadRequestResponse.java
 * Project     : SPM Platform
 */
package com.siemens.spm.common.validator;

import java.util.ArrayList;
import java.util.List;

import com.siemens.spm.common.shared.exception.DataVolumeExceededException;
import jakarta.validation.ConstraintViolationException;

import org.springframework.beans.TypeMismatchException;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;

import com.siemens.spm.common.message.MessageService;
import com.siemens.spm.common.shared.exception.BadRequestException;
import com.siemens.spm.common.util.BeanFinder;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class BadRequestResponse extends BaseBadResponse {

    private static final String KEY_INVALID_DATA = "invalid_data";
    public static final String KEY_EXCESSIVE_DATA = "excessive_data";
    private static final int HTTP_STATUS = HttpStatus.BAD_REQUEST.value();

    /** HttpStatus.BAD_REQUEST */
    private int status;

    /** Error message */
    private BadRequestMessage message;

    /**
     * Build BadRequestResponse from MethodArgumentNotValidException
     */
    public static BadRequestResponse build(MethodArgumentNotValidException ex) {

        BadRequestMessage message = BadRequestMessage.builder()
                .key(KEY_INVALID_DATA)
                .errors(getErrorList(ex))
                .build();

        return BadRequestResponse.builder().status(HTTP_STATUS).message(message).build();
    }

    /**
     * Build BadRequestResponse from BadRequestException
     */
    public static BadRequestResponse build(BadRequestException ex) {

        BadRequestMessage message = BadRequestMessage.builder()
                .key(KEY_INVALID_DATA)
                .errors(getErrorList(ex))
                .build();

        return BadRequestResponse.builder().status(HTTP_STATUS).message(message).build();
    }

    /**
     * Build BadRequestResponse from BadRequestException
     */
    public static BadRequestResponse build(DataVolumeExceededException ex) {

        BadRequestMessage message = BadRequestMessage.builder()
                .key(KEY_EXCESSIVE_DATA)
                .errors(getErrorList(ex))
                .build();

        return BadRequestResponse.builder().status(HTTP_STATUS).message(message).build();
    }

    private static List<Error> getErrorList(BadRequestException ex) {
        List<Error> data = new ArrayList<>();
        MessageService messageService = BeanFinder.getDefaultMessageService();

        if (ex != null) {
            String fieldName = ex.getField();
            String message = (ex.getArgs() == null || ex.getArgs().length == 0) ?
                messageService.getMessage(ex.getMessage()) : 
                messageService.getMessage(ex.getMessage(), ex.getArgs());
            data.add(new Error(fieldName, message));
        }

        return data;
    }

    private static List<Error> getErrorList(DataVolumeExceededException ex) {
        List<Error> data = new ArrayList<>();
        MessageService messageService = BeanFinder.getDefaultMessageService();

        if (ex != null) {
            String message = messageService.getMessage(ex.getMessage());
            data.add(new Error(null, message));
        }

        return data;
    }

    private static List<Error> getErrorList(MissingServletRequestParameterException ex) {
        List<Error> data = new ArrayList<>();
        MessageService messageService = BeanFinder.getDefaultMessageService();

        if (ex != null) {
            String fieldName = ex.getParameterName();
            String message = messageService.getMessage(ex.getMessage());
            data.add(new Error(fieldName, message));
        }

        return data;
    }

    /**
     * Build BadRequestResponse from HttpMessageNotReadableException
     */
    public static BadRequestResponse build(HttpMessageNotReadableException ex) {
        BadRequestMessage message = BadRequestMessage.builder()
                .key(KEY_INVALID_DATA)
                .errors(getErrorList(ex))
                .build();

        return BadRequestResponse.builder().status(HTTP_STATUS).message(message).build();
    }

    /**
     * Build BadRequestResponse from HttpMessageNotReadableException
     */
    public static BadRequestResponse build(ConstraintViolationException ex) {
        BadRequestMessage message = BadRequestMessage.builder()
                .key(KEY_INVALID_DATA)
                .errors(getErrorList(ex))
                .build();

        return BadRequestResponse.builder().status(HTTP_STATUS).message(message).build();
    }

    /**
     * Build BadRequestResponse from TypeMismatchException
     */
    public static BadRequestResponse build(TypeMismatchException ex) {
        BadRequestMessage message = BadRequestMessage.builder()
                .key(KEY_INVALID_DATA)
                .errors(getErrorList(ex))
                .build();

        return BadRequestResponse.builder().status(HTTP_STATUS).message(message).build();
    }

    /**
     * Build BadRequestResponse from TypeMismatchException
     */
    public static BadRequestResponse build(MissingServletRequestParameterException ex) {
        BadRequestMessage message = BadRequestMessage.builder()
                .key(KEY_INVALID_DATA)
                .errors(getErrorList(ex))
                .build();

        return BadRequestResponse.builder().status(HTTP_STATUS).message(message).build();
    }
}
