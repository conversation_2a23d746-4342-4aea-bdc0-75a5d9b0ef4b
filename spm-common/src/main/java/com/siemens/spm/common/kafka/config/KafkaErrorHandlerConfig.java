package com.siemens.spm.common.kafka.config;

import com.siemens.spm.common.kafka.exception.InternalBusinessException;
import com.siemens.spm.common.kafka.exception.InvalidKafkaPayloadException;
import com.siemens.spm.common.kafka.exception.KafkaRetryException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.listener.DefaultErrorHandler;
import org.springframework.util.backoff.ExponentialBackOff;
import org.springframework.util.backoff.FixedBackOff;

import java.util.concurrent.TimeUnit;

@Slf4j
@Configuration
public class KafkaErrorHandlerConfig {

    private static final int MAX_ATTEMPTS = 5;

    @Bean
    public DefaultErrorHandler defaultKafkaHandler() {
        DefaultErrorHandler handler =
                new DefaultErrorHandler(new FixedBackOff(FixedBackOff.DEFAULT_INTERVAL, MAX_ATTEMPTS));

        handler.addNotRetryableExceptions(InvalidKafkaPayloadException.class);
        handler.addNotRetryableExceptions(InternalBusinessException.class);

        handler.addRetryableExceptions(KafkaRetryException.class);
        return handler;
    }

    @Bean(name = "exponentialBackOffKafkaErrorHandler" )
    public DefaultErrorHandler exponentialBackOffKafkaErrorHandler() {

        DefaultErrorHandler handler =
                new DefaultErrorHandler(new ExponentialBackOff(TimeUnit.SECONDS.toMillis(10), 2));

        handler.addNotRetryableExceptions(InvalidKafkaPayloadException.class);
        handler.addNotRetryableExceptions(InternalBusinessException.class);

        handler.addRetryableExceptions(KafkaRetryException.class);
        return handler;
    }

}