package com.siemens.spm.common.util;

import java.sql.Timestamp;
import java.text.ParseException;
import java.util.Date;

import org.springframework.core.convert.converter.Converter;

import com.fasterxml.jackson.databind.util.ClassUtil;
import com.fasterxml.jackson.databind.util.StdDateFormat;

/**
 * This converter uses {@link StdDateFormat}
 * to convert string (ISO-8601 and RFC-1123) to time stamp. It also supports
 * convert numeric string representing number of milliseconds since Epoch to
 * time stamp. In order to enable it, please use
 * {@link org.springframework.web.servlet.config.annotation.WebMvcConfigurer#addFormatters}
 * 
 * <AUTHOR>
 *
 */
public class StringToTimestampConverter implements Converter<String, Timestamp> {

    private static final ThreadLocal<StdDateFormat> formatter = new ThreadLocal<>();

    @Override
    public Timestamp convert(String source) {
        formatter.set(new StdDateFormat());
        try {
            Date date = formatter.get().parse(source);
            return new Timestamp(date.getTime());
        } catch (ParseException e) {
            throw new IllegalArgumentException(String.format(
                    "Failed to parse Date value '%s': %s", source,
                    ClassUtil.exceptionMessage(e)));
        } finally {
            formatter.remove();
        }
    }

}
