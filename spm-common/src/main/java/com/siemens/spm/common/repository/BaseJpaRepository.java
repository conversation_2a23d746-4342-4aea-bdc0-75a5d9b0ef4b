/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : BaseJpaRepository.java
 * Project     : SPM Platform
 */

package com.siemens.spm.common.repository;

import jakarta.persistence.EntityManager;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.repository.NoRepositoryBean;

/**
 * Base JPA repository with method to refresh entity.
 *
 * <AUTHOR>
 *
 */
@NoRepositoryBean
public interface BaseJpaRepository<T, ID> extends JpaRepository<T, ID> {

    void refresh(T entity);

    EntityManager getEntityManager();
}
