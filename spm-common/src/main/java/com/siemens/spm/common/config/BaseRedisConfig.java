/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : BaseRedisConfig.java
 * Project     : SPM Platform
 */
package com.siemens.spm.common.config;

import jakarta.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.jedis.JedisClientConfiguration;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;

import lombok.Getter;

/**
 * Base Redis configuration for blacklisting of access tokens.
 *
 * <AUTHOR>
 */
@Configuration
@Getter
public class BaseRedisConfig {

    private String hostName;

    private Integer port;

    private String password;

    @Autowired
    private Environment env;

    @PostConstruct
    private void init() {
        this.hostName = env.getProperty("spring.data.redis.host");
        this.port = env.getProperty("spring.data.redis.port", Integer.class);
        this.password = env.getProperty("spring.data.redis.password");
    }

    @Bean
    public JedisConnectionFactory jedisConnectionFactory() {
        RedisStandaloneConfiguration configuration = new RedisStandaloneConfiguration(hostName, port);
        configuration.setPassword(password);
        JedisClientConfiguration jedisClientConfiguration = JedisClientConfiguration.builder()
                .usePooling()
                .build();

        JedisConnectionFactory factory = new JedisConnectionFactory(configuration, jedisClientConfiguration);
        factory.afterPropertiesSet();

        return factory;
    }

    @Bean
    public RedisTemplate<String, Long> redisTemplate(JedisConnectionFactory jedisConnectionFactory) {
        final RedisTemplate<String, Long> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(jedisConnectionFactory);
        redisTemplate.setEnableTransactionSupport(true);
        return redisTemplate;
    }
}
