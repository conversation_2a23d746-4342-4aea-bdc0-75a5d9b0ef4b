/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : TranslatableVO.java
 * Project     : SPM Platform
 */
package com.siemens.spm.common.shared.vo;

import com.siemens.spm.common.message.MessageService;

public interface TranslatableVO {

    /**
     * Accept a translator and do all translation inside object
     * 
     * @param translator
     */
    void acceptTranslator(MessageService translator);

}
