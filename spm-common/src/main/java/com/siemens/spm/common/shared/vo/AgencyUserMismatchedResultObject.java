/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AgencyUserMismatchedResultObject.java
 * Project     : SPM Platform
 */

package com.siemens.spm.common.shared.vo;

import com.siemens.spm.common.shared.vo.AgencyUserMismatchedResultObject.AgencyUserStatusCode;

/**
 * Result object returned to client if the credential (agency and/or user) provided in JWT does not match with agency and/or user in URL. 
 * 
 * <AUTHOR>
 *
 */
public class AgencyUserMismatchedResultObject extends AbstractErrorResultObject<AgencyUserStatusCode> {
    
    /**
     * 
     */
    private static final long serialVersionUID = 4960632159148579993L;

    private AgencyUserStatusCode statusCode;
    
    public AgencyUserMismatchedResultObject(AgencyUserStatusCode statusCode) {
        super(statusCode);
    }

    @Override
    public AgencyUserStatusCode getStatusCode() {
        return statusCode;
    }

    @Override
    public String getMessage() {
        return statusCode.getMessage();
    }

    @Override
    protected void setStatusCode(AgencyUserStatusCode code) {
        statusCode = code;
    }
    
    public enum AgencyUserStatusCode {
        AGENCY_MISMATCHED("Agency in JWT doesn't match with agency in URL."), 
        USER_MISMATCHED("User in JWT doesn't match with user in URL");
        
        private final String message;
        
        AgencyUserStatusCode(String message) {
            this.message = message;
        }
        
        public String getMessage() { 
            return message; 
        }
    }
}
