package com.siemens.spm.common.util;

import org.springframework.util.StringUtils;

import java.util.Optional;

public final class EnumConverter {
    /**
     * @param enumeration Target enum-type {@code <T>} class
     * @param name        Name of enum item (case-insensitive)
     * @param <T>         Target enum-type
     * @return {@code Optional<T>} matched enum value-name (case-sensitive)
     */
    public static <T extends Enum<?>> Optional<T> value(Class<T> enumeration,
                                                        String name) {
        if (StringUtils.hasText(name)) {
            for (T each : enumeration.getEnumConstants()) {
                if (each.name().compareToIgnoreCase(name) == 0) {
                    return Optional.of(each);
                }
            }
        }
        return Optional.empty();
    }
}
