package com.siemens.spm.common.util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.siemens.spm.common.message.MessageService;
import com.siemens.spm.common.security.SecurityUtils;
import com.siemens.spm.common.shared.vo.NotificationMessageRequestVO;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public final class NotificationUtil {
    private NotificationUtil() {
    }

    /**
     * Object mapper is thread-safe after initialization
     */
    private static final ObjectMapper objectMapper = BeanFinder.getDefaultObjectMapper();

    public static List<NotificationMessageRequestVO> createNotificationMessage(String nameKey,
                                                                               Object[] nameParams,
                                                                               String descriptionKey,
                                                                               Object[] descriptionParams) {

        MessageService messageService = BeanFinder.getDefaultMessageService();

        Map<String, String> nameMap = messageService.getMessages(nameKey, nameParams);
        if (nameMap == null) {
            nameMap = new HashMap<>();
            nameMap.put(SecurityUtils.getCurrentUserLanguage(), nameKey);
        }

        Map<String, String> contentMap = messageService.getMessages(descriptionKey, descriptionParams);
        if (contentMap == null) {
            contentMap = new HashMap<>();
            contentMap.put(SecurityUtils.getCurrentUserLanguage(), descriptionKey);
        }

        List<NotificationMessageRequestVO> notificationMessageVOs = new ArrayList<>();
        for (Entry<String, String> nameEntry : nameMap.entrySet()) {
            String language = nameEntry.getKey();
            if (language != null) {
                NotificationMessageRequestVO messageVO = NotificationMessageRequestVO.builder()
                        .name(nameEntry.getValue())
                        .description(contentMap.get(language))
                        .language(language)
                        .build();

                notificationMessageVOs.add(messageVO);
            }
        }

        return notificationMessageVOs;
    }

    public static JsonNode buildActionNode(String action) {
        JsonNode actionNode = null;
        if (action != null) {
            try {
                actionNode = objectMapper.readTree(action);
            } catch (JsonProcessingException e) {
                log.error("Cannot generate action for notification: " + action, e);
            }
        }
        return actionNode;
    }

    public static JsonNode buildActionNode(String action, MessageService messageService) {
        ObjectNode actionObjNode = null;
        if (action == null)
            return null;

        try {
            JsonNode actionNode = objectMapper.readTree(action);
            if (actionNode == null || !actionNode.isObject())
                return null;

            actionObjNode = (ObjectNode) actionNode;

            // Translate btn title
            final String btnTitle = "btn_title";
            JsonNode btnNode = actionObjNode.get(btnTitle);
            if (btnNode != null) {
                String btnTitleValue = btnNode.asText();
                String translatedBtnTitle = messageService.getMessage(btnTitleValue);
                actionObjNode.put(btnTitle, translatedBtnTitle);
            }
        } catch (Exception e) {
            log.error("Cannot generate action for notification: " + action, e);
        }

        return actionObjNode;
    }
}
