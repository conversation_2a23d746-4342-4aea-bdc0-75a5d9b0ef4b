/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : MetricCategory.java
 * Project     : SPM Platform
 */
package com.siemens.spm.common.shared.domaintype.alarm.trafficmetric;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.common.message.MessageService;
import com.siemens.spm.common.shared.resource.TextKey;
import com.siemens.spm.common.shared.vo.TranslatableVO;
import lombok.Getter;

/**
 * Declare metric category properties and metrics here
 */
@Getter
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum MetricCategory implements TranslatableVO {

    // NOTE: Category ID is used to translate category label in Alarm Record and Notification
    DETECTOR_ACTUATION("cat_det_act", TextKey.DETECTOR_ACTUATION,
            new TrafficMetric[] { TrafficMetric.DETECTOR_ACTUATION, TrafficMetric.DETECTOR_ACTUATION_VOLUME }),

    PHASE_CALL("cat_phase_call", TextKey.PHASE_CALLS, new TrafficMetric[] { TrafficMetric.PHASE_CALL }),
    PED_PHASE_CALL("cat_ped_phase_call", TextKey.PEDESTRIAN_PHASE_CALLS,
            new TrafficMetric[] { TrafficMetric.PED_PHASE_CALL }),

    GAP_OUT("cat_gap_out", TextKey.GAP_OUT, new TrafficMetric[] { TrafficMetric.GAP_OUT }),
    MAX_OUT("cat_max_out", TextKey.MAX_OUT, new TrafficMetric[] { TrafficMetric.MAX_OUT }),
    FORCE_OFF("cat_force_off", TextKey.FORCE_OFF, new TrafficMetric[] { TrafficMetric.FORCE_OFF }),
    AOG("cat_aog", TextKey.ARRIVALS_ON_GREEN,
            new TrafficMetric[] { TrafficMetric.AOG, TrafficMetric.AOG_VOLUME, TrafficMetric.AOG_PERCENT }),
    AOY("cat_aoy", TextKey.ARRIVALS_ON_YELLOW,
            new TrafficMetric[] { TrafficMetric.AOY, TrafficMetric.AOY_VOLUME, TrafficMetric.AOY_PERCENT }),
    AOR("cat_aor", TextKey.ARRIVALS_ON_RED,
            new TrafficMetric[] { TrafficMetric.AOR, TrafficMetric.AOR_VOLUME, TrafficMetric.AOR_PERCENT }),

    MAX_PED_DELAY("cat_max_ped_delay", TextKey.MAX_PED_DELAY, new TrafficMetric[] { TrafficMetric.MAX_PED_DELAY }),
    AVG_PED_DELAY("cat_avg_ped_delay", TextKey.AVG_PED_DELAY, new TrafficMetric[] { TrafficMetric.AVG_PED_DELAY }),

    TRANSITION_PERCENT("cat_transition_percent", TextKey.TRANSITION_PERCENT,
            new TrafficMetric[] { TrafficMetric.TRANSITION_PERCENT }),

    MAX_GOR_PERCENT("cat_max_gor_percent", TextKey.MAX_GOR_PERCENT,
            new TrafficMetric[] { TrafficMetric.MAX_GOR_PERCENT }),
    AVG_GOR_PERCENT("cat_avg_gor_percent", TextKey.AVG_GOR_PERCENT,
            new TrafficMetric[] { TrafficMetric.AVG_GOR_PERCENT }),
    MAX_ROR5_PERCENT("cat_max_ror5_percent", TextKey.MAX_ROR5_PERCENT,
            new TrafficMetric[] { TrafficMetric.MAX_ROR5_PERCENT }),
    AVG_ROR5_PERCENT("cat_avg_ror5_percent", TextKey.AVG_ROR5_PERCENT,
            new TrafficMetric[] { TrafficMetric.AVG_ROR5_PERCENT }),
    SPLIT_FAILURE("cat_split_failure", TextKey.SPLIT_FAILURE, new TrafficMetric[] { TrafficMetric.SPLIT_FAILURE }),
    POOR_COORDINATION("cat_poor_coordination", TextKey.POOR_COORDINATION,
            new TrafficMetric[] { TrafficMetric.POOR_COORDINATION }),
    EXCESS_CAPACITY("cat_excess_capacity", TextKey.EXCESS_CAPACITY,
            new TrafficMetric[] { TrafficMetric.EXCESS_CAPACITY }),

    AVG_APPROACH_DELAY("cat_avg_approach_delay", TextKey.AVG_APPROACH_DELAY,
            new TrafficMetric[] { TrafficMetric.AVG_APP_DELAY }),

    AVG_QUEUE_LENGTH("cat_avg_queue_length", TextKey.AVG_QUEUE_LENGTH,
            new TrafficMetric[] { TrafficMetric.AVG_QUEUE_LENGTH }),

    NO_DATA("cat_no_data", TextKey.NO_DATA, new TrafficMetric[] { TrafficMetric.NO_DATA }),

    RED_LIGHT_VIOLATION("cat_red_light_violation_count", TextKey.RED_LIGHT_VIOLATION,
            new TrafficMetric[] { TrafficMetric.RED_LIGHT_VIOLATION_COUNT });

    @JsonProperty("id")
    private String id;

    @JsonIgnore
    private String labelKey;

    @JsonProperty("label")
    private String label;

    @JsonProperty("metrics")
    private TrafficMetric[] metricArr;

    MetricCategory(String id, String labelKey, TrafficMetric[] metricArr) {
        this.id = id;
        this.labelKey = labelKey;
        this.metricArr = metricArr;
    }

    @Override
    public void acceptTranslator(MessageService translator) {
        this.label = translator.getMessage(labelKey);

        if (metricArr == null)
            return;
        for (TrafficMetric metric : metricArr) {
            metric.acceptTranslator(translator);
        }
    }

}
