/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : SecurityConstants.java
 * Project     : SPM Platform
 */

package com.siemens.spm.common.security;

import java.util.Map;

public final class SecurityConstants {

    private SecurityConstants() {

    }

    public static final String RESOURCE_SEPARATOR = ";";

    public static final String BEARER_TOKEN_TYPE = "Bearer";

    public static final String ALL_RESOURCES = "*";

    public static final String ALL_ACTIONS = "*";

    public static final String REMOVE_ACTION = "remove";

    public static final String CREATE_ACTION = "create";

    public static final String READ_ACTION = "read";

    public static final String UPDATE_ACTION = "update";

    public static final String DELETE_ACTION = "delete";

    public static final String ACCESS_TOKEN_COOKIE = "spm_access_token";

    public static final String REFRESH_TOKEN_COOKIE = "spm_refresh_access_token";

    public static final String ACCESS_TOKEN_EXPIRE_COOKIE = "spm_access_token_expire";

    public static final String REFRESH_TOKEN_EXPIRE_COOKIE = "spm_refresh_token_expire";

    public static final String TOKEN_TYPE_COOKIE = "spm_token_type";

    public static final String SAME_SITE_NONE = "None";

    public static final String SAME_SITE_SCOPE = "Strict";

    @SuppressWarnings("serial")
    public static final Map<String, String> CRUDMap = Map
            .of(CREATE_ACTION, "c", READ_ACTION, "r", UPDATE_ACTION, "u", DELETE_ACTION, "d");

    @SuppressWarnings("serial")
    public static final Map<String, String> CompactCRUDMap = Map
            .of("c", CREATE_ACTION, "r", READ_ACTION, "u", UPDATE_ACTION, "d", DELETE_ACTION);

}
