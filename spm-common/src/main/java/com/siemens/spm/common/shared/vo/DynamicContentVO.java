package com.siemens.spm.common.shared.vo;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.common.shared.domaintype.notification.DynamicContentType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DynamicContentVO implements Serializable {

    private static final long serialVersionUID = 8794129760683149635L;
    
    @JsonProperty("type")
    private DynamicContentType type;

    @JsonProperty("label")
    private String label;

    @JsonProperty("values")
    private transient Object values;

}
