/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : MetricConstants.java
 * Project     : SPM Platform
 */
package com.siemens.spm.common.shared.domaintype;

public class MetricConstants {

    private MetricConstants() {
    }

    // Metric ID strings
    public static final String AOG_ID = "aog";
    public static final String AOG_PERCENT_ID = "aog_percent";
    public static final String AOG_VOLUME_ID = "aog_volume";
    public static final String AOR_ID = "aor";
    public static final String AOR_PERCENT_ID = "aor_percent";
    public static final String AOR_VOLUME_ID = "aor_volume";
    public static final String AOY_ID = "aoy";
    public static final String AOY_PERCENT_ID = "aoy_percent";
    public static final String AOY_VOLUME_ID = "aoy_volume";
    public static final String TRANSITION_PERCENT_ID = "coord_health_trans_percent";
    public static final String SPLIT_FAILURE_ID = "split_failure_volume";
    public static final String SPLIT_MONITOR_ID = "split_monitor";
    public static final String PREEMPTION_PRIORITY_ID = "preemption_priority_requests";
    public static final String MAX_GOR_PERCENT_ID = "max_gor_percent";
    public static final String AVG_GOR_PERCENT_ID = "avg_gor_percent";
    public static final String MAX_ROR5_PERCENT_ID = "max_ror5_percent";
    public static final String AVG_ROR5_PERCENT_ID = "avg_ror5_percent";
    public static final String AVG_APP_DELAY_ID = "avg_app_delay";
    public static final String RED_LIGHT_VIOLATION_COUNT_ID = "red_light_violation_count";
    public static final String DETECTOR_ACTUATION_ID = "detector_actuation";
    public static final String DETECTOR_ACTUATION_VOLUME_ID = "detector_actuation_volume";
    public static final String FORCE_OFF_ID = "force_off";
    public static final String FORCE_OFF_PERCENT_ID = "force_off_percent";
    public static final String GAP_OUT_ID = "gap_out";
    public static final String GAP_OUT_PERCENT_ID = "gap_out_percent";
    public static final String MAX_OUT_ID = "max_out";
    public static final String MAX_OUT_PERCENT_ID = "max_out_percent";
    public static final String PED_PHASE_CALL_ID = "ped_phase_call";
    public static final String PHASE_CALL_ID = "phase_call";
    public static final String MAX_PED_DELAY_ID = "max_ped_delay";
    public static final String AVG_PED_DELAY_ID = "avg_ped_delay";
    public static final String PED_DELAY_ACTIVATION_ID = "ped_delay_activation";
    public static final String AVG_QUEUE_LENGTH_ID = "avg_queue_length";
    public static final String POOR_COORDINATION_ID = "poor_coordination";
    public static final String EXCESS_CAPACITY_ID = "excess_capacity";
    public static final String MOE_ANALYSIS_ID = "moe_analysis";
    public static final String NO_DATA_ID = "no_data";

    public static final String OPEN_ALARM_NOTI_COUNT_ID = "open_alarm_noti_count";
    public static final String ALARM_RECORD_COUNT_ID = "alarm_record_count";

    // Metric label keys
    public static final String OPEN_ALARM_NOTI_COUNT_LABEL_KEY = "open_alarm_notifications";
    public static final String ALARM_RECORD_COUNT_LABEL_KEY = "alarm_records";
    public static final String AOR_PERCENT_LABEL_KEY = "aor_percent";
    public static final String AOG_PERCENT_LABEL_KEY = "aog_percent";
    public static final String VEHICLE_VOLUME_LABEL_KEY = "vehicle_volume";

    // Metric target IDs
    public static final String NONE_TARGET_ID = "none";
    public static final String PHASE_TARGET_ID = "phase";
    public static final String DETECTOR_TARGET_ID = "detector";

    // Available metric target identifiers
    public static final int MAX_DET_NUM = 64;
    public static final String[] DET_NUM_ARR;

    public static final int MAX_PHASE_NUM = 16;
    public static final String[] PHASE_NUM_ARR;

    public static final String[] NONE_NUM_ARR;

    public static final int MAX_PATTERN = 255;

    static {
        DET_NUM_ARR = new String[MAX_DET_NUM];
        for (int di = 0; di < MAX_DET_NUM; di++) {
            DET_NUM_ARR[di] = String.valueOf(di + 1);
        }

        PHASE_NUM_ARR = new String[MAX_PHASE_NUM];
        for (int pi = 0; pi < MAX_PHASE_NUM; pi++) {
            PHASE_NUM_ARR[pi] = String.valueOf(pi + 1);
        }

        NONE_NUM_ARR = new String[0];
    }

}
