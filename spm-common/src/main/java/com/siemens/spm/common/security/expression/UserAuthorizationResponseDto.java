package com.siemens.spm.common.security.expression;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserAuthorizationResponseDto {

    private String resource;

    private String action;

    /**
     * Need {@link JsonProperty} here to correct getter/setter auto-generated by Lombok
     */
    @JsonProperty("isGranted")
    private boolean isGranted;

}
