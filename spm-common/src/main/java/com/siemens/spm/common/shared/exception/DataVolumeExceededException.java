/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : BadRequestException.java
 * Project     : SPM Platform
 */
package com.siemens.spm.common.shared.exception;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.http.HttpStatus;

@Data
@EqualsAndHashCode(callSuper=false)	
public class DataVolumeExceededException extends RuntimeException  {

	private final HttpStatus httpStatus;

	public DataVolumeExceededException(HttpStatus httpStatus, String message) {
		super(message);
		this.httpStatus = httpStatus;
	}

}
