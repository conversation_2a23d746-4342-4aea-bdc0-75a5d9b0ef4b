package com.siemens.spm.common.util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * Util class related to {@link Map} data structure
 *
 * <AUTHOR> - <EMAIL>
 */
public final class MapUtil {

    private MapUtil() {
    }

    /**
     * Transform a map to other map with the same key and value depend on transformer function.
     *
     * <li>key --> key</li>
     * <li>value --> transformer.apply(value)</li>
     *
     * @param consumeMap  source map to transform
     * @param transformer transformer function to transform value of each entry in map
     * @param <K>         type of entry key (source map)
     * @param <V>         type of entry value (source map)
     * @param <R>         type of result entry value (transformed map)
     * @return transformed map
     * @throws IllegalArgumentException if any param is {@code null}
     */
    public static <K, V, R> Map<K, R> transform(Map<K, V> consumeMap,
                                                Function<V, R> transformer) {
        if (consumeMap == null || transformer == null) {
            throw new IllegalArgumentException();
        }

        Map<K, R> resultMap = new HashMap<>();
        for (Map.Entry<K, V> entry : consumeMap.entrySet()) {
            K key = entry.getKey();
            V value = entry.getValue();
            resultMap.put(key, transformer.apply(value));
        }

        return resultMap;
    }

    /**
     * Transform a map to other map with the key and value depend on transformer functions
     *
     * <li>key --> keyTransformer.apply(key)</li>
     * <li>value --> valueTransformer.apply(value)</li>
     *
     * @param consumeMap       source map to transform
     * @param keyTransformer   transformer function to transform key of each entry in map
     * @param valueTransformer transformer function to transform value of each entry in map
     * @param <K>              type of entry key (source map)
     * @param <V>              type of entry value (source map)
     * @param <KR>             type of result entry key (transformed map)
     * @param <VR>             type of result entry value (transformed map)
     * @return transformed map
     * @throws IllegalArgumentException if any param is {@code null}
     */
    public static <K, V, KR, VR> Map<KR, VR> transform(Map<K, V> consumeMap,
                                                       Function<K, KR> keyTransformer,
                                                       Function<V, VR> valueTransformer) {
        if (consumeMap == null || keyTransformer == null || valueTransformer == null) {
            throw new IllegalArgumentException();
        }

        Map<KR, VR> resultMap = new HashMap<>();
        for (Map.Entry<K, V> entry : consumeMap.entrySet()) {
            K key = entry.getKey();
            V value = entry.getValue();
            resultMap.put(keyTransformer.apply(key), valueTransformer.apply(value));
        }

        return resultMap;
    }

    /**
     * Put a value to a map with key. If the key is already existed, the value will be added to the list of values.
     * Otherwise, a new list will be created with the value.
     *
     * @param map   map to put value
     * @param key   key to put value
     * @param value value to put
     * @param <K>   key type
     * @param <V>   value type
     */
    public static <K, V> void put(Map<K, List<V>> map, K key, V value) {
        if (map.containsKey(key)) {
            map.get(key).add(value);
        } else {
            List<V> newList = new ArrayList<>();
            newList.add(value);
            map.put(key, newList);
        }
    }

}
