package com.siemens.spm.common.shared.domaintype.report;

import java.util.EnumSet;
import java.util.Map;

import org.springframework.util.StringUtils;

import com.google.common.collect.Maps;
import com.siemens.spm.common.constant.CommonConstants;
import com.siemens.spm.common.shared.resource.TextKey;
import com.siemens.spm.common.util.BeanFinder;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ReportType {

    SUMMARY_REPORT("summary-report", TextKey.SUMMARY_REPORT),
    DETECTOR_REPORT("detector-report", TextKey.DETECTOR_REPORT),
    PERFORMANCE_METRIC("performance-metric", TextKey.PERFORMANCE_METRIC);

    private final String id;
    private final String name;

    private static final Map<String, ReportType> id2ReportTypeMap = Maps
            .uniqueIndex(EnumSet.allOf(ReportType.class), ReportType::getId);

    public String getTranslatedName() {
        if (!StringUtils.hasText(name)) {
            return CommonConstants.NOT_AVAILABLE;
        }

        return BeanFinder.getDefaultMessageService().getMessage(name);
    }

    public static ReportType getById(String id) {
        return id2ReportTypeMap.get(id);
    }
}
