/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : ResourceScope.java
 * Project     : SPM Platform
 */
package com.siemens.spm.common.shared.domaintype;

import java.util.HashMap;
import java.util.Map;

import org.springframework.lang.Nullable;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
public enum ResourceScope {

    NONE("NONE"),
    OWNER("OWNER"),
    AGENCY("AGENCY"),
    ALL_AGENCIES("ALL_AGENCIES"),
    SYSTEM("*");

    private static final Map<String, ResourceScope> mappings = new HashMap<>();

    static {
        for (ResourceScope scope : values()) {
            mappings.put(scope.getText().toUpperCase(), scope);
        }
    }

    /**
     * Resolve the given scope text to an {@code ResourceScope}.
     *
     * @param scopeText text
     * @return the corresponding {@code ResourceScope}, or {@code null} if not found
     */
    @Nullable
    public static ResourceScope resolve(@Nullable String scopeText) {
        return (scopeText != null ? mappings.get(scopeText.toUpperCase()) : null);
    }

    /**
     * Resolve the given scope index to scope text.
     *
     * @param scopeIndex index
     * @return the corresponding scope text or {@code null} if not found
     */
    @Nullable
    public static String resolveIndex(int scopeIndex) {
        return (scopeIndex >= 0 && scopeIndex < values().length)
                ? values()[scopeIndex].text
                : null;
    }

    /**
     * Resolve the given scope text to an ordinary index.
     *
     * @param scopeText
     * @return the corresponding index or NULL if not found
     */
    @Nullable
    public static Integer resolveText(@Nullable String scopeText) {
        ResourceScope scope = resolve(scopeText);
        if (scope != null) {
            return scope.ordinal();
        } else {
            log.error("Unknown scope text " + scopeText);
            return null;
        }
    }

    private final String text;

    private ResourceScope(String text) {
        this.text = text;
    }

    public String getText() {
        return text;
    }
}
