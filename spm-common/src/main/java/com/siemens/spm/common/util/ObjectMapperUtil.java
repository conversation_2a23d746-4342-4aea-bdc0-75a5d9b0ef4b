package com.siemens.spm.common.util;

import java.io.IOException;

import org.springframework.core.io.ClassPathResource;
import org.springframework.util.FileCopyUtils;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public final class ObjectMapperUtil {
    private ObjectMapperUtil() {
    }

    /**
     * Read Java object from JSON file
     *
     * @param <T>
     * @param classPathResource
     * @param objClass
     * @return
     */
    public static <T> T readValue(ClassPathResource classPathResource, Class<T> objClass) {
        T result = null;

        if (classPathResource == null)
            return null;

        try {
            byte[] byteData = FileCopyUtils.copyToByteArray(classPathResource.getInputStream());
            result = BeanFinder.getDefaultObjectMapper().readValue(byteData, objClass);
        } catch (IOException e) {
            log.error("Error read JSON file", e);
        }

        return result;
    }
}
