/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : MetricSpecifier.java
 * Project     : SPM Platform
 */
package com.siemens.spm.common.shared.domaintype.alarm.trafficmetric;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.common.shared.vo.MetricTargetVO;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * Specifier of a metric on a specific target (e.g. phase number, detector
 * number)
 */
@Data
@EqualsAndHashCode
@AllArgsConstructor
@NoArgsConstructor
public class MetricSpecifier implements Serializable {

    /**
     * 
     */
    private static final long serialVersionUID = 592439056046160414L;

    @JsonProperty("metric_id")
    private String metricID;

    @JsonProperty("target")
    private MetricTargetVO targetVO;

}
