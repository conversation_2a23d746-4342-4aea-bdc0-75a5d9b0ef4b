/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AnalysisActionDataVO.java
 * Project     : SPM Platform
 */
package com.siemens.spm.common.shared.vo;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.common.util.DateTimeUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class AnalysisActionDataVO implements Serializable {

    private static final long serialVersionUID = 1014679153150855873L;

    @JsonProperty("rule_id")
    private Long ruleId;

    @JsonProperty("agency_id")
    private Integer agencyId;

    @JsonProperty("intersection_id")
    private String intUUID;

    @JsonProperty("analysis_id")
    private String analysisId;

    @JsonProperty("from_time")
    @JsonFormat(pattern = DateTimeUtils.ISO_8601_LOCAL_DATE_TIME)
    private LocalDateTime fromTime;

    @JsonProperty("to_time")
    @JsonFormat(pattern = DateTimeUtils.ISO_8601_LOCAL_DATE_TIME)
    private LocalDateTime toTime;

}
