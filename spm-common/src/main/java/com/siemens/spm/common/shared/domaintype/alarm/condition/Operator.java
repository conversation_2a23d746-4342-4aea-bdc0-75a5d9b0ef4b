/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : Operator.java
 * Project     : SPM Platform
 */

package com.siemens.spm.common.shared.domaintype.alarm.condition;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum Operator {
    EQUALS("=="),
    GREATER_OR_EQUAL(">="),
    GREATER_THAN(">"),
    LESS_OR_EQUAL("<="),
    LESS_THAN("<"),
    NOT_EQUAL("!=");

    private final String label;

    private static final Map<String, Operator> LABEL_MAP = Arrays.stream(Operator.values())
            .collect(Collectors.toMap(Operator::getLabel, operator -> operator));

    @JsonValue
    public String getLabel() {
        return label;
    }

    public static Operator resolveByLabel(String label) {
        return label != null ? LABEL_MAP.get(label) : null;
    }

}
