/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : Condition.java
 * Project     : SPM Platform
 */

package com.siemens.spm.common.shared.domaintype.alarm.condition;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.common.message.MessageService;
import com.siemens.spm.common.shared.domaintype.alarm.trafficmetric.MetricSpecifier;
import com.siemens.spm.common.shared.vo.MetricTargetVO;
import com.siemens.spm.common.shared.vo.TranslatableVO;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_NULL)
public class Condition implements Serializable, TranslatableVO {

    private static final long serialVersionUID = -5929148313486321081L;

    private String categoryID;

    @JsonProperty("category_label")
    private String categoryLabel;
    
    /**
     * Metric ID and target
     */
    @JsonIgnore
    private MetricSpecifier metricSpecifier = new MetricSpecifier(null, null);

    private Operator operator;

    private Double value;

    public Condition(String metricID, MetricTargetVO targetVO, Operator operator, Double value) {
        this.metricSpecifier = new MetricSpecifier(metricID, targetVO);
        this.operator = operator;
        this.value = value;
    }

    @JsonProperty("category_id")
    public void setCategoryID(String categoryID) {
        this.categoryID = categoryID;
        this.categoryLabel = categoryID;
    }

    @JsonProperty("metric_id")
    public String getMetricID() {
        return metricSpecifier.getMetricID();
    }

    @JsonProperty("metric_id")
    public void setMetricId(String metricID) {
        metricSpecifier.setMetricID(metricID);
    }

    @JsonProperty("target")
    public MetricTargetVO getTargetVO() {
        return metricSpecifier.getTargetVO();
    }

    @JsonProperty("target")
    public void setTargetVO(MetricTargetVO target) {
        metricSpecifier.setTargetVO(target);
    }

    @JsonIgnore
    public void acceptVisitor(ConditionVisitor visitor) {
        visitor.visit(this);
    }

    @JsonIgnore
    public Condition create(String metricId, MetricTargetVO target, Operator operator, Double value) {
        return new Condition(metricId, target, operator, value);
    }

    @Override
    public void acceptTranslator(MessageService translator) {
        if (translator == null) return;

        categoryLabel = translator.getMessage(categoryID);
    }

}
