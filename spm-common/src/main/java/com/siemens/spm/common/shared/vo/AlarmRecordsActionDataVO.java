/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AnalysisActionDataVO.java
 * Project     : SPM Platform
 */
package com.siemens.spm.common.shared.vo;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;

import com.siemens.spm.common.util.DateTimeUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class AlarmRecordsActionDataVO implements Serializable {

    private static final long serialVersionUID = 825374594838782046L;

    @JsonProperty("agency_id")
    private Integer agencyId;

    @JsonProperty("intersection")
    private String intUUID;

    @JsonProperty("from_time")
    @JsonFormat(pattern = DateTimeUtils.ISO_8601_LOCAL_DATE_TIME)
    private LocalDateTime fromTime;

    @JsonProperty("to_time")
    @JsonFormat(pattern = DateTimeUtils.ISO_8601_LOCAL_DATE_TIME)
    private LocalDateTime toTime;

}
