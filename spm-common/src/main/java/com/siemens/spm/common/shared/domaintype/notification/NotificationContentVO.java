/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : NotificationContentVO.java
 * Project     : SPM Platform
 */

package com.siemens.spm.common.shared.domaintype.notification;

import java.io.Serializable;
import java.util.List;

import com.siemens.spm.common.shared.vo.DynamicContentVO;

public interface NotificationContentVO extends Serializable {

    List<DynamicContentVO> getDynamicContents();

}