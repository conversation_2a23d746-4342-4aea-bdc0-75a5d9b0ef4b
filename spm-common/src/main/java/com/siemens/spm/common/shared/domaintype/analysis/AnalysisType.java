/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AnalysisType.java
 * Project     : SPM Platform
 */

package com.siemens.spm.common.shared.domaintype.analysis;

import java.util.EnumSet;
import java.util.Map;

import org.springframework.util.StringUtils;

import com.google.common.collect.Maps;
import com.siemens.spm.common.constant.CommonConstants;
import com.siemens.spm.common.shared.resource.TextKey;
import com.siemens.spm.common.util.BeanFinder;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AnalysisType {
    ARRIVALS_ON_RED("aor", TextKey.ARRIVALS_ON_RED),
    ARRIVALS_ON_GREEN("aog", TextKey.ARRIVALS_ON_GREEN),
    PHASE_TERMINATION("pt", TextKey.PHASE_TERMINATIONS),
    COORDINATION("coord", TextKey.COORDINATION),
    P<PERSON><PERSON><PERSON><PERSON><PERSON>("ped", TextKey.PEDESTRIAN),
    COORDINATION_HEALTH("coord_health", TextKey.COORDINATION_HEALTH),
    SPLIT_FAILURE("split", TextKey.SPLIT_FAILURE),
    APPROACH_DELAY("app_delay", TextKey.APPROACH_DELAY),
    QUEUE_LENGTH("queue_length", TextKey.QUEUE_LENGTH),
    VOLUME("vol", TextKey.VOLUME),
    SPLIT_MONITOR("split_monitor", TextKey.SPLIT_MONITOR),
    PREEMPTION_PRIORITY("pp", TextKey.PREEMPTION_PRIORITY),
    TURNING_MOVEMENT("turning_movement", TextKey.TURNING_MOVEMENT),
    RED_LIGHT_VIOLATION("red_light_violation", TextKey.RED_LIGHT_VIOLATION),
    ABNORMAL_DATA("abnormal_data",TextKey.ABNORMAL_DATA),
    YELLOW_TRAP_OCCURRENCES("yellow_trap_occurrence",TextKey.YELLOW_TRAP_OCCURRENCES),
    MOE_ANALYSIS("moe_analysis", TextKey.MOE_ANALYSIS),
    DETECTOR_REPORT("detector_report", TextKey.DETECTOR_REPORT_ANALYSIS);

    private static final Map<String, AnalysisType> id2AnalysisTypeMap = Maps
            .uniqueIndex(EnumSet.allOf(AnalysisType.class), AnalysisType::getId);

    private final String id;
    private final String name;

    public String getTranslatedName() {
        if (!StringUtils.hasText(name)) {
            return CommonConstants.NOT_AVAILABLE;
        }

        return BeanFinder.getDefaultMessageService().getMessage(name);
    }

    public static AnalysisType getById(String id) {
        return id2AnalysisTypeMap.get(id);
    }
}
