/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : ConditionParser.java
 * Project     : SPM Platform
 */

package com.siemens.spm.common.shared.domaintype.alarm.condition;

import com.google.common.base.Strings;
import com.siemens.spm.common.constant.CommonConstants;
import com.siemens.spm.common.message.MessageService;
import com.siemens.spm.common.shared.domaintype.alarm.trafficmetric.TrafficMetric;
import com.siemens.spm.common.shared.resource.TextKey;
import com.siemens.spm.common.shared.vo.MetricTargetVO;
import com.siemens.spm.common.util.BeanFinder;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public final class ConditionParser {

    private ConditionParser() {
    }

    public static String parseCondition(String conditionJson) {
        ConditionGroup conditionGroup;

        // try parsing input string (expected to be in json format) to a ConditionGroup
        // object
        try {
            conditionGroup = BeanFinder.getDefaultObjectMapper().readValue(conditionJson, ConditionGroup.class);
        } catch (Exception e) {
            return CommonConstants.NOT_AVAILABLE;
        }

        String plainCondition;

        try {
            plainCondition = parseCondition(conditionGroup);
        } catch (Exception e) {
            log.error("Error when parsing condition!", e);
            return CommonConstants.NOT_AVAILABLE;
        }

        plainCondition = plainCondition.substring(1, plainCondition.length() - 1);

        return plainCondition;
    }

    public static String parseCondition(ConditionGroup conditionGroup) {
        String ret;

        if (conditionGroup == null) {
            throw new IllegalArgumentException("condition group is invalid!");
        }

        if (conditionGroup.isCondition()) {
            ret = doParseCondition(conditionGroup);
        } else if (conditionGroup.isConditionGroup()) {
            ret = doParseConditionGroup(conditionGroup);
        } else {
            throw new IllegalArgumentException("condition group has invalid structure!");
        }

        return ret;
    }

    private static String doParseCondition(ConditionGroup conditionGroup) {
        MessageService translator = BeanFinder.getDefaultMessageService();
        conditionGroup.acceptTranslator(translator);
        StringBuilder plainRuleConditionBuilder = new StringBuilder();

        TrafficMetric trafficMetric = TrafficMetric.getById(conditionGroup.getMetricID());
        trafficMetric.acceptTranslator(translator);

        plainRuleConditionBuilder.append("(");

        plainRuleConditionBuilder.append(conditionGroup.getCategoryLabel());
        plainRuleConditionBuilder.append(" ");
        plainRuleConditionBuilder.append(trafficMetric.getLabel());
        MetricTargetVO targetVO;
        if ((targetVO = conditionGroup.getTargetVO()) != null && targetVO.getType() != null) {
            plainRuleConditionBuilder.append(" of ");
            String targetType = translator != null
                    ? translator.getMessage(targetVO.getType().getID())
                    : targetVO.getType().getID();
            plainRuleConditionBuilder.append(targetType);
            plainRuleConditionBuilder.append(" (");

            String identifier;
            if (Strings.isNullOrEmpty(targetVO.getIdentifier())) {
                identifier = translator != null ? translator.getMessage(TextKey.ALL) : TextKey.ALL;
            } else {
                identifier = targetVO.getIdentifier();
            }

            plainRuleConditionBuilder.append(identifier);
            plainRuleConditionBuilder.append(")");
        }
        plainRuleConditionBuilder.append(" ");
        plainRuleConditionBuilder.append(conditionGroup.getOperator().getLabel());
        plainRuleConditionBuilder.append(" ");
        plainRuleConditionBuilder.append(conditionGroup.getValue().toString());

        plainRuleConditionBuilder.append(")");

        return plainRuleConditionBuilder.toString();
    }

    private static String doParseConditionGroup(ConditionGroup conditionGroup) {
        MessageService translator = BeanFinder.getDefaultMessageService();
        conditionGroup.acceptTranslator(translator);
        StringBuilder plainRuleConditionBuilder = new StringBuilder();

        Conjunction conjunction = conditionGroup.getConjunction();

        ConditionGroup[] subConditionGroups = conditionGroup.getConditions();

        if (conjunction != Conjunction.NONE) {
            plainRuleConditionBuilder.append("(");
        }

        String conjunctionString = conjunction.getName();

        String firstReadableSubConditionGroup = parseCondition(subConditionGroups[0]);

        plainRuleConditionBuilder.append(firstReadableSubConditionGroup);

        for (int i = 1; i < subConditionGroups.length; i++) {
            String readableSubConditionGroup = parseCondition(subConditionGroups[i]);

            plainRuleConditionBuilder.append(" ");
            plainRuleConditionBuilder.append(conjunctionString);
            plainRuleConditionBuilder.append(" ");
            plainRuleConditionBuilder.append(readableSubConditionGroup);
        }

        if (conjunction != Conjunction.NONE) {
            plainRuleConditionBuilder.append(")");
        }

        return plainRuleConditionBuilder.toString();
    }
}
