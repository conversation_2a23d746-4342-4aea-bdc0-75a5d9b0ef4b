package com.siemens.spm.common.util;

import java.sql.Timestamp;
import java.util.TimeZone;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class StringToTimestampConverterTest {
    @BeforeEach
    public void setUp() {
        TimeZone.setDefault(TimeZone.getTimeZone("UTC"));
    }
    @Test
    void test_convert() {
        StringToTimestampConverter stringToTimestampConverter = new StringToTimestampConverter();
        Assertions.assertThrows(IllegalArgumentException.class, () -> {
            stringToTimestampConverter.convert("blabla");
        });
        Timestamp timestamp = stringToTimestampConverter.convert("2022-01-01 ");
        Assertions.assertEquals(122, timestamp.getYear());
    }
}
