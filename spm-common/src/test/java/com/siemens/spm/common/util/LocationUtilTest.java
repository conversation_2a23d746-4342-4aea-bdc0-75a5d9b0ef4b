// package com.siemens.spm.common.util;

// import java.util.ArrayList;
// import java.util.Collections;
// import java.util.List;

// import org.springframework.data.util.Pair;

// import org.junit.jupiter.api.Test;

// import static org.junit.jupiter.api.Assertions.assertEquals;
// import static org.junit.jupiter.api.Assertions.assertNotNull;
// import static org.junit.jupiter.api.Assertions.assertNull;

// class LocationUtilTest {
//     @Test
//     void test_findNearestCenterLocation_noLocation() {
//         Pair<Double, Double> centerLocation = LocationUtil.findNearestCenterLocation(Collections.emptyList());
//         assertNull(centerLocation, "Center location should be null");
//     }

//     @Test
//     void test_findNearestCenterLocation_oneLocation() {
//         Pair<Double, Double> centerLocation = LocationUtil.findNearestCenterLocation(List.of(Pair.of(1.0, 2.0)));
//         assertNotNull(centerLocation, "Center location should be not null");
//         assertEquals(1.0, centerLocation.getFirst(), "Latitude of center location");
//         assertEquals(2.0, centerLocation.getSecond(), "Longitude of center location");
//     }

//     @Test
//     void test_findNearestCenterLocation_allPositivePoints() {
//         List<Pair<Double, Double>> locations = new ArrayList<>();
//         locations.add(Pair.of(30.0, 30.0));
//         locations.add(Pair.of(45.0, 45.0));
//         locations.add(Pair.of(70.0, 70.0));
//         Pair<Double, Double> centerLocation = LocationUtil.findNearestCenterLocation(locations);
//         assertNotNull(centerLocation, "Center location should be not null");
//         assertEquals(45.0, centerLocation.getFirst(), "Latitude of center location");
//         assertEquals(45.0, centerLocation.getSecond(), "Longitude of center location");
//     }

//     @Test
//     void test_findNearestCenterLocation_allNegativePoints() {
//         List<Pair<Double, Double>> locations = new ArrayList<>();
//         locations.add(Pair.of(-30.0, -30.0));
//         locations.add(Pair.of(-45.0, -45.0));
//         locations.add(Pair.of(-70.0, -70.0));
//         Pair<Double, Double> centerLocation = LocationUtil.findNearestCenterLocation(locations);
//         assertNotNull(centerLocation, "Center location should be not null");
//         assertEquals(-45.0, centerLocation.getFirst(), "Latitude of center location");
//         assertEquals(-45.0, centerLocation.getSecond(), "Longitude of center location");
//     }

//     @Test
//     void test_findNearestCenterLocation_allPositiveAndNegativePoints() {
//         List<Pair<Double, Double>> locations = new ArrayList<>();
//         locations.add(Pair.of(20.0, -170.0));
//         locations.add(Pair.of(40.0, 160.0));
//         locations.add(Pair.of(-80.0, 150.0));

//         // Central will be: (-20, 170) ==> The nearest point is (-80, 150)

//         Pair<Double, Double> centerLocation = LocationUtil.findNearestCenterLocation(locations);
//         assertNotNull(centerLocation, "Center location should be not null");
//         assertEquals(40.0, centerLocation.getFirst(), "Latitude of center location");
//         assertEquals(160, centerLocation.getSecond(), "Longitude of center location");
//     }

// }

