package com.siemens.spm.common.shared.vo;

import org.springframework.http.HttpStatus;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class SimpleResultObjectTest {

    @Test
    void test_constructor() {
        Assertions.assertDoesNotThrow(() -> {
            new SimpleResultObject();
            new SimpleResultObject(SimpleResultObject.SimpleStatusCode.SUCCESS);
        });
    }

    @Test
    void test_common() {
        SimpleResultObject simpleResultObject = new SimpleResultObject(SimpleResultObject.SimpleStatusCode.ERROR);
        Assertions.assertEquals(SimpleResultObject.SimpleStatusCode.ERROR, simpleResultObject.getErrorStatusValue());
        Assertions.assertEquals(SimpleResultObject.SimpleStatusCode.SUCCESS,
                simpleResultObject.getSuccessfulStatusValue());
        Assertions.assertEquals(SimpleResultObject.SimpleStatusCode.ERROR, simpleResultObject.getStatusCode());
        Assertions.assertEquals("unknown_exception", simpleResultObject.getMessage());
        Assertions.assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, simpleResultObject.getHttpStatus());
        Assertions.assertNull(simpleResultObject.getErrorFieldName());

        simpleResultObject.setStatusCode(SimpleResultObject.SimpleStatusCode.SUCCESS);
        Assertions.assertEquals(SimpleResultObject.SimpleStatusCode.SUCCESS, simpleResultObject.getStatusCode());

        Assertions.assertEquals("success", SimpleResultObject.SimpleStatusCode.SUCCESS.getMessage());
        Assertions.assertEquals("unknown_exception", SimpleResultObject.SimpleStatusCode.ERROR.getMessage());
        Assertions.assertEquals("no_data", SimpleResultObject.SimpleStatusCode.NO_DATA.getMessage());

        Assertions.assertEquals(HttpStatus.NO_CONTENT, SimpleResultObject.SimpleStatusCode.NO_DATA.getHttpStatus());
        Assertions.assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, SimpleResultObject.SimpleStatusCode.ERROR.getHttpStatus());
        Assertions.assertEquals(HttpStatus.OK, SimpleResultObject.SimpleStatusCode.SUCCESS.getHttpStatus());

    }
}

