package com.siemens.spm.common.util;

import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class ResponseUtilTest {
    @Test
    void test_wrapOrNotFound() {

        Assertions.assertEquals(HttpStatus.NOT_FOUND, ResponseUtil.wrapOrNotFound(null).getStatusCode());
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_LANGUAGE, "ja");
        Assertions.assertEquals(HttpStatus.NOT_FOUND, ResponseUtil.wrapOrNotFound(null, headers).getStatusCode());
        Assertions.assertEquals(HttpStatus.NOT_FOUND, ResponseUtil.wrapOrNotFound(null, null).getStatusCode());
    }
}
