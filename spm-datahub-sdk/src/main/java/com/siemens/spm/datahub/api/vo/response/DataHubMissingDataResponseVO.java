/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : DataHubMissingDataResponseVO.java
 * Project     : SPM Platform
 */
package com.siemens.spm.datahub.api.vo.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class DataHubMissingDataResponseVO implements Serializable {

    @Serial
    private static final long serialVersionUID = -2406790220567778727L;

    private LocalDateTime fromTime;

    private LocalDateTime toTime;

    private List<LocalDateTime> missingTimes;

}