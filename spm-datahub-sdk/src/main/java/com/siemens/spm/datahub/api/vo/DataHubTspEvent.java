package com.siemens.spm.datahub.api.vo;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.datahub.api.vo.response.DataHubVehicleLocation;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
public class DataHubTspEvent implements Serializable {

    private static final long serialVersionUID = 2498654206243324802L;

    private static final String[] ACCEPTABLE_REQUEST_TYPE = { "Enable Priority Request", "Cancel Priority Request" };

    private String id;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
    @JsonProperty("dateTimeStamp")
    private LocalDateTime dateTimestamp;

    @JsonProperty("requestID")
    private String requestId;

    @JsonProperty("requestType")
    private String requestType;

    @JsonProperty("vehicleID")
    private Integer vehicleId;

    @JsonProperty("networkID")
    private String networkId;

    @JsonProperty("networkIDStr")
    private String networkIdStr;

    @JsonProperty("vehicleLocation")
    private DataHubVehicleLocation vehicleLocation;

    @JsonProperty("intersectionAssetID")
    private Integer intersectionAssetId;

    @JsonProperty("intersectionUUID")
    private String intersectionUUID;

    @JsonProperty("agencyUUID")
    private String agencyUUID;

    @JsonProperty("routeNumber")
    private Integer routeNumber;

    @JsonProperty("blockNumber")
    private Integer blockNumber;

    @JsonProperty("estArrivalTime")
    private Integer estArrivalTime;

    @JsonProperty("passengerLoading")
    private Integer passengerLoading;

    @JsonProperty("passengerLoadingSpecified")
    private Boolean passengerLoadingSpecified;

    @JsonProperty("timeBehindAhead")
    private Integer timeBehindAhead;

    @JsonProperty("tripdirection")
    private String tripDirection;

    @JsonProperty("directionAtNextIntersection")
    private String nextIntersectionDirection;

    @JsonProperty("timezone")
    private String timezone;

    @JsonIgnore
    public boolean isValid() {
        return dateTimestamp != null && intersectionUUID != null && routeNumber != null
                && isValidRequestType(requestType) && DataHubVehicleLocation.isValid(vehicleLocation);
    }

    private static boolean isValidRequestType(String requestType) {
        for (String type : ACCEPTABLE_REQUEST_TYPE) {
            if (type.equals(requestType)) {
                return true;
            }
        }
        return false;
    }

}
