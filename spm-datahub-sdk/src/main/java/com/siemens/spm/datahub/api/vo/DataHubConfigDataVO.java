/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : DataHubConfigDataVO.java
 * Project     : SPM Platform
 */
package com.siemens.spm.datahub.api.vo;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(Include.NON_NULL)
public class DataHubConfigDataVO implements Serializable {

    private static final long serialVersionUID = 3480258619795798404L;

    @JsonProperty("valid_from_time")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ssXX") // TODO Standard format
    private LocalDateTime validFromTime;

    @JsonProperty("valid_to_time")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ssXX") // TODO Standard format
    private LocalDateTime validToTime;

    @JsonProperty("configuration")
    private DataHubIntersectionConfigVO config;

}
