/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : DataHubPatternInfoVO.java
 * Project     : SPM Platform
 */
package com.siemens.spm.datahub.api.vo;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DataHubPatternInfoVO implements Serializable {

    private static final long serialVersionUID = -8523032963161611491L;

    @JsonProperty("cycleLength")
    private int[] cycleLength;

    @JsonProperty("offsetTime")
    private int[] offsetTime;

    @JsonProperty("sequenceNumber")
    private int[] sequenceNumber;

    @JsonProperty("splitTime")
    private int[][] splitTime;

    @JsonProperty("splitMode")
    private int[][] splitMode;

    @JsonProperty("splitCoordPhase")
    private int[][] splitCoordPhase;

}
