package com.siemens.spm.datahub.api.vo.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.datahub.api.vo.DataHubIntersectionVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> Admin
 * @version : 1.0
 * @since : 13/3/2025
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class IntersectionResponse {
    @JsonProperty("total_count")
    private Long totalCount;

    @JsonProperty("intersections")
    private List<DataHubIntersectionVO> intersections;
}
