package com.siemens.spm.datahub.api.vo.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.common.shared.domaintype.IntersectionStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * Author: hungnm
 * Date: 18/12/2024
 */
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class DataHubIntersectionStatusHistoryResponseVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1623621950326480875L;

    @JsonProperty("intersection_id")
    private String intersectionId;

    @JsonProperty("histories")
    private List<StatusHistory> histories;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class StatusHistory {
        @JsonProperty("id")
        private String id;
        @JsonProperty("status")
        private String status;
        @JsonProperty("from")
        private Timestamp fromTime;
        @JsonProperty("to")
        private Timestamp toTime;
    }
}
