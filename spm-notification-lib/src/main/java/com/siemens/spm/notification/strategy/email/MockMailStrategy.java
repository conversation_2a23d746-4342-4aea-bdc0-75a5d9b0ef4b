/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : MockMailStrategy.java
 * Project     : SPM Platform
 */
package com.siemens.spm.notification.strategy.email;

import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import com.siemens.spm.common.constant.SProfile;
import com.siemens.spm.notification.exception.NotificationSenderException;
import com.siemens.spm.notification.vo.EmailMessageVO;

/**
 * Email strategy which sends nothing
 * 
 * <AUTHOR>
 *
 */
@Service
@Profile(SProfile.SPRING_BOOT_TEST)
public class MockMailStrategy implements MailStrategy {

    @Override
    public void sendEmail(EmailMessageVO emailVO) throws NotificationSenderException {
        // do nothing
    }

}
