package com.siemens.spm.notification.strategy.email.msgraph;

import java.util.Collections;
import java.util.List;

import jakarta.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.azure.identity.ClientSecretCredential;
import com.azure.identity.ClientSecretCredentialBuilder;
import com.microsoft.graph.authentication.TokenCredentialAuthProvider;
import com.microsoft.graph.core.ClientException;
import com.microsoft.graph.models.BodyType;
import com.microsoft.graph.models.EmailAddress;
import com.microsoft.graph.models.ItemBody;
import com.microsoft.graph.models.Message;
import com.microsoft.graph.models.Recipient;
import com.microsoft.graph.models.UserSendMailParameterSet;
import com.microsoft.graph.requests.GraphServiceClient;
import com.siemens.spm.common.constant.SProfile;
import com.siemens.spm.notification.exception.NotificationSenderException;
import com.siemens.spm.notification.strategy.email.MailStrategy;
import com.siemens.spm.notification.vo.EmailMessageVO;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Profile({ SProfile.PRODUCT, SProfile.QA, SProfile.STAGING, SProfile.DEV })
@Service("MsGraphMailStrategy")
public class MsGraphMailStrategy implements MailStrategy {

    @Value("${mail.provider.ms-graph.client-id}")
    private String clientId;

    @Value("${mail.provider.ms-graph.client-secret}")
    private String clientSecret;

    @Value("${mail.provider.ms-graph.tenant-id}")
    private String tenantId;

    @Value("${mail.provider.ms-graph.sender}")
    private String sender;

    @Value("${mail.provider.ms-graph.save-to-sent}")
    private boolean saveToSentItems = false;

    private GraphServiceClient<?> graphServiceClient;

    @PostConstruct
    private void init() {
        final ClientSecretCredential credential = new ClientSecretCredentialBuilder()
                .clientId(clientId)
                .clientSecret(clientSecret)
                .tenantId(tenantId)
                .build();
        final TokenCredentialAuthProvider authProvider = new TokenCredentialAuthProvider(credential);

        graphServiceClient = GraphServiceClient.builder()
                .authenticationProvider(authProvider)
                .buildClient();
    }

    @Override
    public void sendEmail(EmailMessageVO emailVO) throws NotificationSenderException {
        Message message = new Message();
        message.subject = emailVO.getSubject();

        message.body = new ItemBody();
        message.body.contentType = BodyType.HTML;
        message.body.content = emailVO.getContent();

        message.toRecipients = buildRecipients(emailVO.getToRecipients());
        message.ccRecipients = buildRecipients(emailVO.getCcRecipients());
        message.bccRecipients = buildRecipients(emailVO.getBccRecipients());

        try {
            graphServiceClient.users(sender)
                    .sendMail(UserSendMailParameterSet
                            .newBuilder()
                            .withMessage(message)
                            .withSaveToSentItems(saveToSentItems)
                            .build())
                    .buildRequest()
                    .post();
        } catch (ClientException e) {
            throw new NotificationSenderException(e);
        }
    }

    private static List<Recipient> buildRecipients(List<String> emails) {
        if (emails == null || emails.isEmpty()) {
            return Collections.emptyList();
        }

        return emails.stream()
                .filter(StringUtils::hasText)
                .map(MsGraphMailStrategy::buildRecipient)
                .toList();
    }

    private static Recipient buildRecipient(String email) {
        Recipient recipient = new Recipient();
        EmailAddress emailAddress = new EmailAddress();
        emailAddress.address = email;
        recipient.emailAddress = emailAddress;

        return recipient;
    }

}
