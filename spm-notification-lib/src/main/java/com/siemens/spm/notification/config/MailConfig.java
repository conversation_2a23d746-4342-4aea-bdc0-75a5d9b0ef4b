package com.siemens.spm.notification.config;

import jakarta.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

import lombok.Getter;

/**
 * <AUTHOR>
 */
@Configuration
@Getter
public class MailConfig {

    private String senderEmail;

    private String faviconUrl;

    @Autowired
    private Environment env;

    @PostConstruct
    private void init() {
        senderEmail = env.getProperty("mail.config.sender");
        faviconUrl = env.getProperty("mail.config.faviconUrl");
    }

}
