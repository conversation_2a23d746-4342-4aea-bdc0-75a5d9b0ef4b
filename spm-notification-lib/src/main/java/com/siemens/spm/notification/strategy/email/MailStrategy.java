package com.siemens.spm.notification.strategy.email;

import com.siemens.spm.notification.exception.NotificationSenderException;
import com.siemens.spm.notification.vo.EmailMessageVO;

/**
 * 
 * <AUTHOR>
 *
 */
public interface MailStrategy {

    /**
     * Sending message by email
     * 
     * @param emailVO
     */
    void sendEmail(EmailMessageVO emailVO) throws NotificationSenderException;
}
